import { z } from 'zod';

// Transaction validation schema
export const transactionSchema = z.object({
  description: z.string()
    .min(1, 'La descripción es requerida')
    .max(100, 'La descripción no puede exceder 100 caracteres'),
  date: z.string()
    .min(1, 'La fecha es requerida')
    .refine((date) => {
      const parsed = new Date(date);
      return !isNaN(parsed.getTime());
    }, 'La fecha debe ser válida'),
  type: z.enum(['income', 'expense'], {
    errorMap: () => ({ message: 'El tipo debe ser ingreso o gasto' })
  }),
  amount: z.number()
    .positive('El monto debe ser mayor a 0')
    .max(999999999, 'El monto no puede exceder $999,999,999'),
  category: z.string()
    .min(1, 'La categoría es requerida')
    .max(50, 'La categoría no puede exceder 50 caracteres'),
  icon: z.string().optional()
});

export type TransactionInput = z.infer<typeof transactionSchema>;

// Form validation
export function validateTransaction(data: unknown): { 
  success: boolean; 
  data?: TransactionInput; 
  errors?: z.ZodError 
} {
  try {
    const validData = transactionSchema.parse(data);
    return { success: true, data: validData };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return { success: false, errors: error };
    }
    throw error;
  }
}

// Email validation
export const emailSchema = z.string()
  .email('Ingresa un email válido')
  .min(1, 'El email es requerido');

// Password validation
export const passwordSchema = z.string()
  .min(6, 'La contraseña debe tener al menos 6 caracteres')
  .max(128, 'La contraseña no puede exceder 128 caracteres');

// Number validation with locale support
export function validateAmount(value: string): number | null {
  // Remove any non-numeric characters except dots and commas
  const cleaned = value.replace(/[^\d.,]/g, '');
  
  // Handle different decimal separators
  const normalized = cleaned.replace(',', '.');
  
  const parsed = parseFloat(normalized);
  
  if (isNaN(parsed) || parsed < 0) {
    return null;
  }
  
  return parsed;
}

// Date validation
export function validateDate(dateString: string): boolean {
  const date = new Date(dateString);
  const now = new Date();
  
  // Check if date is valid
  if (isNaN(date.getTime())) {
    return false;
  }
  
  // Check if date is not in the future (more than 1 day)
  const tomorrow = new Date(now);
  tomorrow.setDate(tomorrow.getDate() + 1);
  
  if (date > tomorrow) {
    return false;
  }
  
  // Check if date is not too old (more than 10 years)
  const tenYearsAgo = new Date(now);
  tenYearsAgo.setFullYear(tenYearsAgo.getFullYear() - 10);
  
  if (date < tenYearsAgo) {
    return false;
  }
  
  return true;
}

// Get user-friendly error messages
export function getValidationErrorMessage(error: z.ZodError): string {
  const firstError = error.errors[0];
  return firstError?.message || 'Error de validación';
}

// Utility to validate form data before submission
export function validateFormData(formData: FormData): {
  isValid: boolean;
  errors: Record<string, string>;
  data?: any;
} {
  const errors: Record<string, string> = {};
  const data: any = {};
  
  // Get all form fields
  for (const [key, value] of formData.entries()) {
    data[key] = value;
  }
  
  // Validate transaction if it has transaction fields
  if (data.amount !== undefined) {
    const amount = validateAmount(data.amount);
    if (amount === null) {
      errors.amount = 'El monto debe ser un número válido mayor a 0';
    } else {
      data.amount = amount;
    }
  }
  
  if (data.date && !validateDate(data.date)) {
    errors.date = 'La fecha debe ser válida y no estar en el futuro';
  }
  
  if (data.description && data.description.trim().length === 0) {
    errors.description = 'La descripción es requerida';
  }
  
  if (data.category && data.category.trim().length === 0) {
    errors.category = 'La categoría es requerida';
  }
  
  return {
    isValid: Object.keys(errors).length === 0,
    errors,
    data: Object.keys(errors).length === 0 ? data : undefined
  };
}