import { useState, useEffect } from 'react';
import { supabase } from '../lib/supabaseClient';
import { Transaction } from '../lib/types';
import { useAuth } from '../lib/AuthContext';
import { useToast } from '../components/Toast';

// Extended transaction type for planned expenses
export interface PlannedExpense extends Transaction {
  is_essential: boolean;
  is_flexible: boolean;
  status: 'pendiente' | 'pagado' | 'parcial' | 'pospuesto' | 'cancelado';
  is_planned: boolean;
  priority_order: number;
}

export function useTransactions() {
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [plannedExpenses, setPlannedExpenses] = useState<PlannedExpense[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { user } = useAuth();
  const { showError, showSuccess } = useToast();

  const fetchTransactions = async () => {
    if (!user) {
      setTransactions([]);
      setPlannedExpenses([]);
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const { data, error: supabaseError } = await supabase
        .from('transactions')
        .select('*')
        .eq('is_planned', false)
        .order('date', { ascending: false });

      if (supabaseError) {
        throw supabaseError;
      }

      setTransactions(data || []);
      
      // Fetch planned expenses separately
      const { data: plannedData, error: plannedError } = await supabase
        .from('transactions')
        .select('*')
        .eq('is_planned', true)
        .order('priority_order', { ascending: true });

      if (plannedError) {
        throw plannedError;
      }

      setPlannedExpenses(plannedData || []);
    } catch (err) {
      console.error('Error fetching transactions:', err);
      showError('Error de conexión', 'No se pudieron cargar las transacciones');
      setError(err instanceof Error ? err.message : 'Error desconocido');
    } finally {
      setLoading(false);
    }
  };

  const addTransaction = async (transaction: Omit<Transaction, 'id'>) => {
    if (!user) {
      return { success: false, error: 'Usuario no autenticado' };
    }

    // Validate transaction data
    if (!transaction.description || transaction.amount <= 0 || !transaction.category) {
      const error = 'Datos de transacción inválidos';
      showError('Error de validación', error);
      return { success: false, error };
    }

    try {
      setError(null);

      const { data, error: supabaseError } = await supabase
        .from('transactions')
        .insert({
          description: transaction.description,
          date: transaction.date,
          type: transaction.type,
          amount: transaction.amount,
          category: transaction.category,
          icon: transaction.icon,
          user_id: user.id
        })
        .select()
        .single();

      if (supabaseError) {
        throw supabaseError;
      }

      // Add the new transaction to the local state
      setTransactions(prev => [data, ...prev]);
      showSuccess('Transacción agregada', 'La transacción se guardó correctamente');
      
      return { success: true, data };
    } catch (err) {
      console.error('Error adding transaction:', err);
      showError('Error al guardar', 'No se pudo agregar la transacción');
      const errorMessage = err instanceof Error ? err.message : 'Error al agregar transacción';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    }
  };

  const addPlannedExpense = async (expense: Omit<PlannedExpense, 'id'>) => {
    if (!user) {
      return { success: false, error: 'Usuario no autenticado' };
    }

    // Validate planned expense data
    if (!expense.description || expense.amount <= 0 || !expense.category) {
      const error = 'Datos de gasto planificado inválidos';
      showError('Error de validación', error);
      return { success: false, error };
    }

    try {
      setError(null);

      const { data, error: supabaseError } = await supabase
        .from('transactions')
        .insert({
          description: expense.description,
          date: expense.date,
          type: 'expense',
          amount: expense.amount,
          category: expense.category,
          icon: expense.icon,
          is_essential: expense.is_essential,
          is_flexible: expense.is_flexible,
          status: expense.status,
          is_planned: true,
          priority_order: expense.priority_order,
          user_id: user.id
        })
        .select()
        .single();

      if (supabaseError) {
        throw supabaseError;
      }

      // Add the new planned expense to the local state
      setPlannedExpenses(prev => [...prev, data]);
      showSuccess('Gasto planificado agregado', 'El gasto se guardó correctamente');
      
      return { success: true, data };
    } catch (err) {
      console.error('Error adding planned expense:', err);
      showError('Error al guardar', 'No se pudo agregar el gasto planificado');
      const errorMessage = err instanceof Error ? err.message : 'Error al agregar gasto planificado';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    }
  };

  const updatePlannedExpense = async (id: string, updates: Partial<PlannedExpense>) => {
    if (!user) {
      return { success: false, error: 'Usuario no autenticado' };
    }

    try {
      setError(null);

      const { data, error: supabaseError } = await supabase
        .from('transactions')
        .update(updates)
        .eq('id', id)
        .eq('is_planned', true)
        .select()
        .single();

      if (supabaseError) {
        throw supabaseError;
      }

      // Update the planned expense in local state
      setPlannedExpenses(prev => prev.map(expense => 
        expense.id === id ? { ...expense, ...data } : expense
      ));
      
      showSuccess('Gasto actualizado', 'Los cambios se guardaron correctamente');
      
      return { success: true, data };
    } catch (err) {
      console.error('Error updating planned expense:', err);
      showError('Error al actualizar', 'No se pudo actualizar el gasto');
      const errorMessage = err instanceof Error ? err.message : 'Error al actualizar gasto';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    }
  };

  const deletePlannedExpense = async (id: string) => {
    if (!user) {
      return { success: false, error: 'Usuario no autenticado' };
    }

    try {
      setError(null);

      const { error: supabaseError } = await supabase
        .from('transactions')
        .delete()
        .eq('id', id)
        .eq('is_planned', true);

      if (supabaseError) {
        throw supabaseError;
      }

      // Remove the planned expense from local state
      setPlannedExpenses(prev => prev.filter(expense => expense.id !== id));
      showSuccess('Gasto eliminado', 'El gasto se eliminó correctamente');
      
      return { success: true };
    } catch (err) {
      console.error('Error deleting planned expense:', err);
      showError('Error al eliminar', 'No se pudo eliminar el gasto');
      const errorMessage = err instanceof Error ? err.message : 'Error al eliminar gasto';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    }
  };

  const convertToRealTransaction = async (plannedExpenseId: string) => {
    const plannedExpense = plannedExpenses.find(expense => expense.id === plannedExpenseId);
    if (!plannedExpense) {
      return { success: false, error: 'Gasto planificado no encontrado' };
    }

    try {
      // Update the planned expense to mark as real transaction
      const updateResult = await updatePlannedExpense(plannedExpenseId, {
        is_planned: false,
        status: 'pagado',
        date: new Date().toISOString().split('T')[0] // Today's date
      });

      if (updateResult.success) {
        // Move from planned expenses to transactions
        setPlannedExpenses(prev => prev.filter(expense => expense.id !== plannedExpenseId));
        setTransactions(prev => [updateResult.data, ...prev]);
        
        showSuccess('Gasto registrado', 'El gasto se convirtió en transacción real');
      }

      return updateResult;
    } catch (err) {
      console.error('Error converting to real transaction:', err);
      const errorMessage = err instanceof Error ? err.message : 'Error al convertir gasto';
      showError('Error al convertir', errorMessage);
      return { success: false, error: errorMessage };
    }
  };

  const deleteTransaction = async (id: string) => {
    if (!user) {
      return { success: false, error: 'Usuario no autenticado' };
    }

    try {
      setError(null);

      const { error: supabaseError } = await supabase
        .from('transactions')
        .delete()
        .eq('id', id);

      if (supabaseError) {
        throw supabaseError;
      }

      // Remove the transaction from local state
      setTransactions(prev => prev.filter(t => t.id !== id));
      showSuccess('Transacción eliminada', 'La transacción se eliminó correctamente');
      
      return { success: true };
    } catch (err) {
      console.error('Error deleting transaction:', err);
      showError('Error al eliminar', 'No se pudo eliminar la transacción');
      const errorMessage = err instanceof Error ? err.message : 'Error al eliminar transacción';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    }
  };

  useEffect(() => {
    if (user) {
      fetchTransactions();
    }
  }, [user]);

  return {
    transactions,
    plannedExpenses,
    loading,
    error,
    addTransaction,
    addPlannedExpense,
    updatePlannedExpense,
    deletePlannedExpense,
    convertToRealTransaction,
    deleteTransaction,
    refetch: fetchTransactions
  };
}