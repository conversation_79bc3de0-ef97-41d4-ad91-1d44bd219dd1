import React, { useState } from 'react';
import { Download, Smartphone, Wifi, WifiOff, RefreshCw, X, Check } from 'lucide-react';
import { useTheme } from '../lib/ThemeContext';
import { usePWA } from '../hooks/usePWA';
import { FadeIn, SlideIn } from './ui/LoadingStates';

export function PWAPrompt() {
  const { theme } = useTheme();
  const {
    isInstallable,
    isInstalled,
    isOnline,
    isUpdateAvailable,
    installPWA,
    updateServiceWorker,
    addToHomeScreen
  } = usePWA();

  const [showInstallPrompt, setShowInstallPrompt] = useState(false);
  const [isInstalling, setIsInstalling] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);

  const handleInstall = async () => {
    setIsInstalling(true);
    try {
      const success = await installPWA();
      if (success) {
        setShowInstallPrompt(false);
      }
    } catch (error) {
      console.error('Error installing PWA:', error);
    } finally {
      setIsInstalling(false);
    }
  };

  const handleUpdate = async () => {
    setIsUpdating(true);
    try {
      await updateServiceWorker();
    } catch (error) {
      console.error('Error updating PWA:', error);
    } finally {
      setIsUpdating(false);
    }
  };

  // Show install prompt if app is installable and not installed
  React.useEffect(() => {
    if (isInstallable && !isInstalled) {
      const timer = setTimeout(() => {
        setShowInstallPrompt(true);
      }, 3000); // Show after 3 seconds

      return () => clearTimeout(timer);
    }
  }, [isInstallable, isInstalled]);

  return (
    <>
      {/* Network Status Indicator */}
      <div className="fixed top-4 left-4 z-40">
        {!isOnline && (
          <FadeIn>
            <div className={`flex items-center gap-2 px-3 py-2 rounded-lg shadow-lg ${
              theme === 'dark'
                ? 'bg-red-900 border border-red-700 text-red-200'
                : 'bg-red-100 border border-red-300 text-red-800'
            }`}>
              <WifiOff className="w-4 h-4" />
              <span className="text-sm font-medium">Sin conexión</span>
            </div>
          </FadeIn>
        )}
      </div>

      {/* Update Available Banner */}
      {isUpdateAvailable && (
        <div className="fixed top-0 left-0 right-0 z-50">
          <SlideIn direction="down">
            <div className={`p-4 transition-colors duration-200 ${
              theme === 'dark'
                ? 'bg-blue-900 border-b border-blue-700'
                : 'bg-blue-100 border-b border-blue-300'
            }`}>
              <div className="flex items-center justify-between max-w-7xl mx-auto">
                <div className="flex items-center gap-3">
                  <RefreshCw className="w-5 h-5 text-blue-500" />
                  <div>
                    <p className={`font-medium transition-colors duration-200 ${
                      theme === 'dark' ? 'text-blue-200' : 'text-blue-800'
                    }`}>
                      Nueva versión disponible
                    </p>
                    <p className={`text-sm transition-colors duration-200 ${
                      theme === 'dark' ? 'text-blue-300' : 'text-blue-600'
                    }`}>
                      Actualiza para obtener las últimas funciones y mejoras
                    </p>
                  </div>
                </div>
                
                <button
                  onClick={handleUpdate}
                  disabled={isUpdating}
                  className={`px-4 py-2 rounded-lg font-medium transition-colors duration-200 ${
                    theme === 'dark'
                      ? 'bg-blue-600 hover:bg-blue-700 text-white'
                      : 'bg-blue-600 hover:bg-blue-700 text-white'
                  } disabled:opacity-50 disabled:cursor-not-allowed`}
                >
                  {isUpdating ? (
                    <div className="flex items-center gap-2">
                      <RefreshCw className="w-4 h-4 animate-spin" />
                      Actualizando...
                    </div>
                  ) : (
                    'Actualizar'
                  )}
                </button>
              </div>
            </div>
          </SlideIn>
        </div>
      )}

      {/* Install Prompt */}
      {showInstallPrompt && !isInstalled && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-end sm:items-center justify-center z-50 p-4">
          <SlideIn direction="up">
            <div className={`rounded-xl max-w-md w-full transition-colors duration-200 ${
              theme === 'dark' 
                ? 'bg-[#21222d] border border-[#2a2a3d]' 
                : 'bg-white border border-slate-200 shadow-xl'
            }`}>
              {/* Header */}
              <div className="p-6 border-b border-gray-600">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-blue-500 rounded-lg">
                      <Smartphone className="w-6 h-6 text-white" />
                    </div>
                    <div>
                      <h3 className={`font-bold transition-colors duration-200 ${
                        theme === 'dark' ? 'text-white' : 'text-slate-900'
                      }`}>
                        Instalar FinanceTracker
                      </h3>
                      <p className={`text-sm transition-colors duration-200 ${
                        theme === 'dark' ? 'text-gray-400' : 'text-slate-500'
                      }`}>
                        Acceso rápido desde tu pantalla de inicio
                      </p>
                    </div>
                  </div>
                  
                  <button
                    onClick={() => setShowInstallPrompt(false)}
                    className={`p-1 rounded-lg transition-colors duration-200 ${
                      theme === 'dark'
                        ? 'text-gray-400 hover:text-white hover:bg-gray-700'
                        : 'text-slate-400 hover:text-slate-600 hover:bg-slate-100'
                    }`}
                  >
                    <X className="w-4 h-4" />
                  </button>
                </div>
              </div>

              {/* Content */}
              <div className="p-6">
                <div className="space-y-4">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-green-100 dark:bg-green-900/30 rounded-lg">
                      <Check className="w-4 h-4 text-green-600 dark:text-green-400" />
                    </div>
                    <span className={`text-sm transition-colors duration-200 ${
                      theme === 'dark' ? 'text-gray-300' : 'text-slate-600'
                    }`}>
                      Funciona sin conexión a internet
                    </span>
                  </div>
                  
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-green-100 dark:bg-green-900/30 rounded-lg">
                      <Check className="w-4 h-4 text-green-600 dark:text-green-400" />
                    </div>
                    <span className={`text-sm transition-colors duration-200 ${
                      theme === 'dark' ? 'text-gray-300' : 'text-slate-600'
                    }`}>
                      Notificaciones de presupuestos y metas
                    </span>
                  </div>
                  
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-green-100 dark:bg-green-900/30 rounded-lg">
                      <Check className="w-4 h-4 text-green-600 dark:text-green-400" />
                    </div>
                    <span className={`text-sm transition-colors duration-200 ${
                      theme === 'dark' ? 'text-gray-300' : 'text-slate-600'
                    }`}>
                      Acceso rápido desde la pantalla de inicio
                    </span>
                  </div>
                  
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-green-100 dark:bg-green-900/30 rounded-lg">
                      <Check className="w-4 h-4 text-green-600 dark:text-green-400" />
                    </div>
                    <span className={`text-sm transition-colors duration-200 ${
                      theme === 'dark' ? 'text-gray-300' : 'text-slate-600'
                    }`}>
                      Experiencia de aplicación nativa
                    </span>
                  </div>
                </div>
              </div>

              {/* Actions */}
              <div className="p-6 border-t border-gray-600">
                <div className="flex gap-3">
                  <button
                    onClick={() => setShowInstallPrompt(false)}
                    className={`flex-1 py-2 px-4 rounded-lg border transition-colors duration-200 ${
                      theme === 'dark'
                        ? 'border-gray-600 text-gray-300 hover:bg-gray-700'
                        : 'border-slate-300 text-slate-700 hover:bg-slate-50'
                    }`}
                  >
                    Ahora no
                  </button>
                  
                  <button
                    onClick={handleInstall}
                    disabled={isInstalling}
                    className="flex-1 py-2 px-4 bg-blue-500 hover:bg-blue-600 text-white rounded-lg transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {isInstalling ? (
                      <div className="flex items-center justify-center gap-2">
                        <Download className="w-4 h-4 animate-bounce" />
                        Instalando...
                      </div>
                    ) : (
                      <div className="flex items-center justify-center gap-2">
                        <Download className="w-4 h-4" />
                        Instalar
                      </div>
                    )}
                  </button>
                </div>
              </div>
            </div>
          </SlideIn>
        </div>
      )}
    </>
  );
}

// PWA Status Component for Settings
export function PWAStatus() {
  const { theme } = useTheme();
  const {
    isInstalled,
    isOnline,
    isUpdateAvailable,
    requestNotificationPermission,
    subscribeToPush,
    addToHomeScreen
  } = usePWA();

  const [notificationPermission, setNotificationPermission] = useState(
    'Notification' in window ? Notification.permission : 'denied'
  );

  const handleEnableNotifications = async () => {
    const granted = await requestNotificationPermission();
    if (granted) {
      setNotificationPermission('granted');
      await subscribeToPush();
    }
  };

  return (
    <div className={`rounded-xl p-6 transition-colors duration-200 ${
      theme === 'dark' 
        ? 'bg-[#21222d] border border-[#2a2a3d]' 
        : 'bg-slate-50 border border-slate-200'
    }`}>
      <h3 className={`text-lg font-semibold mb-4 transition-colors duration-200 ${
        theme === 'dark' ? 'text-white' : 'text-slate-900'
      }`}>
        Estado de la Aplicación
      </h3>

      <div className="space-y-4">
        {/* Installation Status */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <Smartphone className="w-5 h-5 text-blue-500" />
            <span className={`transition-colors duration-200 ${
              theme === 'dark' ? 'text-gray-300' : 'text-slate-700'
            }`}>
              Aplicación instalada
            </span>
          </div>
          <div className="flex items-center gap-2">
            {isInstalled ? (
              <span className="text-green-500 text-sm font-medium">Instalada</span>
            ) : (
              <button
                onClick={addToHomeScreen}
                className="text-blue-500 text-sm font-medium hover:text-blue-600"
              >
                Instalar
              </button>
            )}
          </div>
        </div>

        {/* Network Status */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            {isOnline ? (
              <Wifi className="w-5 h-5 text-green-500" />
            ) : (
              <WifiOff className="w-5 h-5 text-red-500" />
            )}
            <span className={`transition-colors duration-200 ${
              theme === 'dark' ? 'text-gray-300' : 'text-slate-700'
            }`}>
              Estado de conexión
            </span>
          </div>
          <span className={`text-sm font-medium ${
            isOnline ? 'text-green-500' : 'text-red-500'
          }`}>
            {isOnline ? 'Conectado' : 'Sin conexión'}
          </span>
        </div>

        {/* Notifications */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="w-5 h-5 text-purple-500">🔔</div>
            <span className={`transition-colors duration-200 ${
              theme === 'dark' ? 'text-gray-300' : 'text-slate-700'
            }`}>
              Notificaciones
            </span>
          </div>
          <div className="flex items-center gap-2">
            {notificationPermission === 'granted' ? (
              <span className="text-green-500 text-sm font-medium">Habilitadas</span>
            ) : (
              <button
                onClick={handleEnableNotifications}
                className="text-blue-500 text-sm font-medium hover:text-blue-600"
              >
                Habilitar
              </button>
            )}
          </div>
        </div>

        {/* Updates */}
        {isUpdateAvailable && (
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <RefreshCw className="w-5 h-5 text-orange-500" />
              <span className={`transition-colors duration-200 ${
                theme === 'dark' ? 'text-gray-300' : 'text-slate-700'
              }`}>
                Actualización disponible
              </span>
            </div>
            <button className="text-orange-500 text-sm font-medium hover:text-orange-600">
              Actualizar
            </button>
          </div>
        )}
      </div>
    </div>
  );
}
