import { useState, useEffect } from 'react';
import { Card } from './ui/Card';
import { TooltipRoot, TooltipTrigger, TooltipContent } from './ui/Tooltip';
import { Shield, PiggyBank, TrendingUp, DollarSign, Calendar, ArrowUpRight, ArrowDownRight, Info, AlertCircle, Trophy, Umbrella } from 'lucide-react';
import { useTheme } from '../lib/ThemeContext';
import { format } from 'date-fns';
import { es } from 'date-fns/locale';
import { useEmergencyFund } from '../hooks/useEmergencyFund';
import { LoadingSpinner } from './LoadingSpinner';

export function FondoEmergenciaPage() {
  const { theme } = useTheme();
  const { config, movimientos, loading, error, updateConfig, addMovement, calculateEmergencyFundData } = useEmergencyFund();
  
  // Estado para la seccion activa (para dispositivos móviles)
  const [seccionActiva, setSeccionActiva] = useState<'baby1' | 'baby3' | 'movimientos' | 'indicadores'>('baby1');
  
  // Estado para el modal de agregar movimiento
  const [showModal, setShowModal] = useState(false);
  const [nuevoMovimiento, setNuevoMovimiento] = useState({
    tipo: 'aporte',
    monto: '',
    medio: 'transferencia',
    motivo: '',
  });
  
  // Calculate current emergency fund data
  const emergencyFundData = calculateEmergencyFundData();
  const {
    fondoInicial,
    fondoCompleto,
    totalFondo,
    metaFondoInicial,
    metaFondoCompleto,
    porcentajeFondoInicial,
    porcentajeFondoCompleto,
    mesesActuales
  } = emergencyFundData;
  
  // Determinar la etapa de Baby Step en la que se encuentra
  const determinarEtapa = () => {
    if (fondoInicial < metaFondoInicial) {
      return 'baby1';
    } else if (fondoCompleto < metaFondoCompleto) {
      return 'baby3-en-progreso';
    } else {
      return 'baby3-completo';
    }
  };
  
  const etapaActual = determinarEtapa();
  
  // Función para obtener mensaje según la etapa
  const getMensajeEtapa = () => {
    switch (etapaActual) {
      case 'baby1':
        if (fondoInicial < metaFondoInicial * 0.3) {
          return '🌱 Estás comenzando tu Baby Step 1. ¡Cada peso cuenta!';
        } else if (fondoInicial < metaFondoInicial * 0.7) {
          return `🔥 ¡Vas por buen camino! Ya acumulaste $${fondoInicial} de los $${metaFondoInicial}`;
        } else {
          return `✨ ¡Ya casi terminas el Baby Step 1! Te faltan solo $${metaFondoInicial - fondoInicial}`;
        }
      case 'baby3-en-progreso':
        if (mesesActuales < 1) {
          return '🏁 ¡Completaste el Baby Step 1! Ahora vamos por tu fondo real.';
        } else if (mesesActuales < 3) {
          return `📈 ¡Ya puedes sobrevivir ${mesesActuales.toFixed(1)} meses sin ingresos!`;
        } else {
          return `🚀 ¡Estás cerca! Tienes cobertura para ${mesesActuales.toFixed(1)} meses.`;
        }
      case 'baby3-completo':
        return '🛡️ ¡Fondo completo! Estás protegido ante imprevistos.';
      default:
        return '';
    }
  };
  
  // Función para agregar un nuevo movimiento
  const agregarMovimiento = async () => {
    if (!nuevoMovimiento.monto || parseFloat(nuevoMovimiento.monto) <= 0) {
      return;
    }
    
    const result = await addMovement({
      tipo: nuevoMovimiento.tipo as 'aporte' | 'retiro',
      monto: parseFloat(nuevoMovimiento.monto),
      medio: nuevoMovimiento.medio as 'transferencia' | 'efectivo' | 'otro',
      motivo: nuevoMovimiento.motivo || `${nuevoMovimiento.tipo === 'aporte' ? 'Aporte' : 'Retiro'} fondo de emergencia`
    });

    if (result.success) {
      // Reiniciar el formulario
      setNuevoMovimiento({
        tipo: 'aporte',
        monto: '',
        medio: 'transferencia',
        motivo: '',
      });
      
      setShowModal(false);
    }
  };
  
  // Handle configuration updates
  const handleConfigUpdate = async (updates: { monthly_expenses?: number; coverage_months?: number }) => {
    await updateConfig(updates);
  };

  // Determinar el color de la barra de progreso para el fondo completo
  const getBarColor = () => {
    if (mesesActuales < 1) return 'bg-red-500';
    if (mesesActuales < 3) return 'bg-yellow-500';
    return 'bg-green-500';
  };
  
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <LoadingSpinner size="lg" text="Cargando fondo de emergencia..." />
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-900/30 border border-red-800 text-red-300 p-6 rounded-lg">
        <p className="font-medium">Error al cargar fondo de emergencia:</p>
        <p className="text-sm">{error}</p>
      </div>
    );
  }

  return (
    <div className="space-y-8 pb-20">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6">
        <div className="flex items-center gap-2">
          <h1 className="text-3xl font-bold text-white">Fondo de Emergencia</h1>
          <TooltipRoot>
            <TooltipTrigger>
              <Info className="w-5 h-5 text-gray-400 hover:text-white cursor-help" />
            </TooltipTrigger>
            <TooltipContent className="max-w-sm">
              <p className="font-medium mb-1">Tu Escudo Financiero</p>
              <p>El fondo de emergencia te protege de imprevistos. Baby Step 1: $1,000 para empezar. Baby Step 3: 3-6 meses de gastos para estar completamente protegido.</p>
            </TooltipContent>
          </TooltipRoot>
        </div>
        <button
          onClick={() => setShowModal(true)}
          className="px-4 py-2 bg-blue-600 hover:bg-blue-500 rounded-lg text-white font-medium flex items-center space-x-2"
        >
          <PiggyBank className="w-5 h-5" />
          <span>Registrar Movimiento</span>
        </button>
      </div>
      
      {/* Panel de navegación en móvil */}
      <div className="md:hidden flex overflow-x-auto space-x-2 py-2">
        <button
          onClick={() => setSeccionActiva('baby1')}
          className={`px-4 py-2 rounded-full whitespace-nowrap text-sm font-medium ${
            seccionActiva === 'baby1' 
              ? 'bg-blue-600 text-white'
              : 'bg-[#21222d] text-gray-300'
          }`}
        >
          Baby Step 1
        </button>
        <button
          onClick={() => setSeccionActiva('baby3')}
          className={`px-4 py-2 rounded-full whitespace-nowrap text-sm font-medium ${
            seccionActiva === 'baby3' 
              ? 'bg-blue-600 text-white'
              : 'bg-[#21222d] text-gray-300'
          }`}
        >
          Fondo Completo
        </button>
        <button
          onClick={() => setSeccionActiva('movimientos')}
          className={`px-4 py-2 rounded-full whitespace-nowrap text-sm font-medium ${
            seccionActiva === 'movimientos' 
              ? 'bg-blue-600 text-white'
              : 'bg-[#21222d] text-gray-300'
          }`}
        >
          Movimientos
        </button>
        <button
          onClick={() => setSeccionActiva('indicadores')}
          className={`px-4 py-2 rounded-full whitespace-nowrap text-sm font-medium ${
            seccionActiva === 'indicadores' 
              ? 'bg-blue-600 text-white'
              : 'bg-[#21222d] text-gray-300'
          }`}
        >
          Estado
        </button>
      </div>
      
      {/* Contenedor principal de secciones */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* SECCIÓN 1: Baby Step 1 - Fondo Inicial */}
        <Card className={`${seccionActiva !== 'baby1' && 'hidden md:block'}`}>
          <div className="p-6">
            <div className="flex items-center mb-4">
              <Shield className="w-6 h-6 text-blue-500 mr-2" />
              <h2 className="text-xl font-semibold">Baby Step 1: Fondo Inicial Rápido</h2>
            </div>
            
            <div className="flex items-center mb-6">
              <div className="bg-[#2a2b38] rounded-xl p-4 flex-1">
                <div className="flex justify-between items-center mb-2">
                  <span className="text-gray-300">Meta:</span>
                  <span className="font-semibold">${metaFondoInicial.toLocaleString()}</span>
                </div>
                
                <div className="flex justify-between items-center mb-3">
                  <span className="text-gray-300">Ahorro actual:</span>
                  <div className="flex items-center">
                    <span className="font-semibold">${fondoInicial.toLocaleString()}</span>
                  </div>
                </div>
                
                <div className="mb-2">
                  <div className="w-full bg-gray-700 h-4 rounded-full overflow-hidden">
                    <div 
                      className={`h-full ${
                        porcentajeFondoInicial < 30
                          ? 'bg-red-500'
                          : porcentajeFondoInicial < 70
                            ? 'bg-yellow-500'
                            : 'bg-green-500'
                      }`}
                      style={{ 
                        width: `${porcentajeFondoInicial}%`,
                        transition: 'width 0.5s ease-in-out'
                      }}
                    ></div>
                  </div>
                  <div className="flex justify-between mt-1 text-xs">
                    <span className="text-gray-400">0%</span>
                    <span className="font-medium">{porcentajeFondoInicial}%</span>
                    <span className="text-gray-400">100%</span>
                  </div>
                </div>
                
                <div className="bg-[#1e1e2d] p-3 rounded-lg border border-[#2a2a3d] mt-4">
                  <p className="text-sm font-medium text-blue-300">
                    {porcentajeFondoInicial >= 60 
                      ? `🔥 ¡Primer escudo activado! Ya acumulaste $${fondoInicial} de los $${metaFondoInicial}`
                      : `🌱 Tu primer paso es conseguir un fondo inicial de $${metaFondoInicial} para emergencias pequeñas`}
                  </p>
                </div>
              </div>
            </div>
            
            <div className="text-sm text-gray-300 border-t border-gray-700 pt-4">
              <div className="flex items-start gap-2">
                <Info className="w-4 h-4 text-blue-400 mt-0.5 flex-shrink-0" />
                <p>
                  El <span className="font-semibold">Baby Step 1</span> es tu primer escudo de protección financiera según Dave Ramsey: 
                  un fondo de $1,000 para emergencias pequeñas mientras pagas tus deudas.
                </p>
              </div>
            </div>
          </div>
        </Card>
        
        {/* SECCIÓN 2: Baby Step 3 - Fondo Completo */}
        <Card className={`${seccionActiva !== 'baby3' && 'hidden md:block'}`}>
          <div className="p-6">
            <div className="flex items-center mb-4">
              <Umbrella className="w-6 h-6 text-green-400 mr-2" />
              <h2 className="text-xl font-semibold">Baby Step 3: Fondo Completo</h2>
            </div>
            
            <div className="bg-[#2a2b38] rounded-xl p-4 mb-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-1">
                    Gasto mensual promedio
                  </label>
                  <div className="flex items-center">
                    <div className="relative flex-1">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <span className="text-gray-500">$</span>
                      </div>
                      <input
                        type="number"
                        value={config?.monthly_expenses || 3000}
                        onChange={(e) => handleConfigUpdate({ monthly_expenses: Math.max(1, parseFloat(e.target.value) || 0) })}
                        className="w-full bg-[#21222d] border border-gray-700 rounded-lg p-2 pl-8"
                      />
                    </div>
                  </div>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-1">
                    Meses de cobertura deseados
                  </label>
                  <select
                    value={config?.coverage_months || 3}
                    onChange={(e) => handleConfigUpdate({ coverage_months: parseInt(e.target.value) })}
                    className="w-full bg-[#21222d] border border-gray-700 rounded-lg p-2"
                  >
                    <option value={3}>3 meses</option>
                    <option value={4}>4 meses</option>
                    <option value={5}>5 meses</option>
                    <option value={6}>6 meses</option>
                  </select>
                </div>
              </div>
              
              <div className="mb-4">
                <div className="flex justify-between items-center mb-2">
                  <span className="text-sm font-medium text-gray-300">Meta total:</span>
                  <span className="font-semibold">${metaFondoCompleto.toLocaleString()}</span>
                </div>
                
                <div className="flex justify-between items-center mb-3">
                  <span className="text-sm font-medium text-gray-300">Ahorro actual:</span>
                  <div className="flex items-center">
                    <span className="font-semibold">${totalFondo.toLocaleString()}</span>
                  </div>
                </div>
              </div>
              
              <div className="mb-2">
                <div className="w-full bg-gray-700 h-4 rounded-full overflow-hidden">
                  <div 
                    className={`h-full ${getBarColor()}`}
                    style={{ 
                      width: `${porcentajeFondoCompleto}%`,
                      transition: 'width 0.5s ease-in-out'
                    }}
                  ></div>
                </div>
                <div className="flex justify-between mt-1 text-xs">
                  <span className="text-gray-400">0%</span>
                  <span className="font-medium">{porcentajeFondoCompleto}%</span>
                  <span className="text-gray-400">100%</span>
                </div>
              </div>
              
              <div className="mt-4 p-3 bg-[#1e1e2d] rounded-lg border border-[#2a2a3d]">
                <div className="flex items-start gap-2">
                  <TrendingUp className="w-5 h-5 text-blue-400 flex-shrink-0 mt-0.5" />
                  <div>
                    <p className="text-blue-300 font-medium">
                      Te faltan ${(metaFondoCompleto - fondoCompleto).toLocaleString()} para alcanzar tu meta de {config?.coverage_months || 3} meses
                    </p>
                    {mesesActuales < mesesCobertura && (
                      <p className="text-xs text-gray-400 mt-1">
                        Te faltan ${(metaFondoCompleto - fondoCompleto).toLocaleString()} para alcanzar tu meta de {mesesCobertura} meses
                      </p>
                    )}
                  </div>
                </div>
              </div>
            </div>
            
            <div className="flex items-center space-x-2 text-sm">
              <div className="flex items-center">
                <div className="w-3 h-3 rounded-full bg-red-500 mr-1"></div>
                <span className="text-gray-300">Menos de 1 mes</span>
              </div>
              <div className="flex items-center">
                <div className="w-3 h-3 rounded-full bg-yellow-500 mr-1"></div>
                <span className="text-gray-300">1-3 meses</span>
              </div>
              <div className="flex items-center">
                <div className="w-3 h-3 rounded-full bg-green-500 mr-1"></div>
                <span className="text-gray-300">3-6 meses</span>
              </div>
            </div>
          </div>
        </Card>
        
        {/* SECCIÓN 4: Indicadores Visuales */}
        <Card className={`${seccionActiva !== 'indicadores' && 'hidden md:block'}`}>
          <div className="p-6">
            <div className="flex items-center mb-4">
              <Trophy className="w-6 h-6 text-yellow-500 mr-2" />
              <h2 className="text-xl font-semibold">Estado de tu Fondo de Emergencia</h2>
            </div>
            
            <div className="bg-[#2a2b38] rounded-xl p-6 mb-4">
              <div className="flex justify-between items-center mb-6">
                <span className="text-lg font-medium">Tu progreso total:</span>
                <span className="text-lg font-bold">
                  ${totalFondo.toLocaleString()}
                </span>
              </div>
              
              <div className="relative pt-1 mb-4">
                <div className="w-full h-2 bg-gray-700 rounded-full">
                  <div className="relative">
                    {/* Barra Baby Step 1 */}
                    <div 
                      className="absolute top-0 left-0 h-2 rounded-l-full bg-blue-500"
                      style={{ 
                        width: `${(Math.min(fondoInicial, metaFondoInicial) / (metaFondoInicial + metaFondoCompleto)) * 100}%`,
                        transition: 'width 0.5s ease-in-out'
                      }}
                    ></div>
                    
                    {/* Indicador de transición entre Baby Steps */}
                    {fondoInicial >= metaFondoInicial && (
                      <div 
                        className="absolute top-0 h-2 bg-white"
                        style={{ 
                          left: `${(metaFondoInicial / (metaFondoInicial + metaFondoCompleto)) * 100}%`,
                          width: '2px'
                        }}
                      ></div>
                    )}
                    
                    {/* Barra Baby Step 3 */}
                    {fondoInicial >= metaFondoInicial && (
                      <div 
                        className="absolute top-0 h-2 rounded-r-full bg-green-500"
                        style={{ 
                          left: `${(metaFondoInicial / (metaFondoInicial + metaFondoCompleto)) * 100}%`,
                          width: `${(Math.min(fondoCompleto, metaFondoCompleto) / (metaFondoInicial + metaFondoCompleto)) * 100}%`,
                          transition: 'width 0.5s ease-in-out'
                        }}
                      ></div>
                    )}
                  </div>
                </div>
                
                <div className="flex justify-between mt-2 text-xs text-gray-400">
                  <span>Baby Step 1</span>
                  <span>Baby Step 3</span>
                  <span>Completo</span>
                </div>
              </div>
              
              <div className="p-4 bg-[#1e1e2d] rounded-lg border border-[#2a2a3d] text-center">
                <p className="font-medium text-blue-300 text-lg">
                  {getMensajeEtapa()}
                </p>
                
                {etapaActual === 'baby1' && (
                  <p className="text-sm text-gray-300 mt-2">
                    👣 Estás completando el Baby Step 1
                  </p>
                )}
                
                {etapaActual === 'baby3-en-progreso' && (
                  <p className="text-sm text-gray-300 mt-2">
                    🧱 Construyendo tu fondo real de emergencia
                  </p>
                )}
                
                {etapaActual === 'baby3-completo' && (
                  <p className="text-sm text-gray-300 mt-2">
                    🛡️ Fondo completo, ¡listo para lo inesperado!
                  </p>
                )}
              </div>
            </div>
            
            <div className="space-y-3">
              <div className="flex items-start gap-2">
                <AlertCircle className="w-4 h-4 text-blue-400 mt-0.5 flex-shrink-0" />
                <p className="text-sm text-gray-300">
                  <span className="font-medium">Baby Step 1 ($1,000):</span> Tu primer escudo ante emergencias pequeñas mientras pagas tus deudas.
                </p>
              </div>
              
              <div className="flex items-start gap-2">
                <AlertCircle className="w-4 h-4 text-blue-400 mt-0.5 flex-shrink-0" />
                <p className="text-sm text-gray-300">
                  <span className="font-medium">Baby Step 3 (3-6 meses):</span> Tu fondo completo para vivir sin ingresos durante varios meses.
                </p>
              </div>
              
              <div className="flex items-start gap-2">
                <Info className="w-4 h-4 text-yellow-400 mt-0.5 flex-shrink-0" />
                <p className="text-sm text-gray-300">
                  Recuerda: El Baby Step 2 (pagar deudas) debe completarse entre el paso 1 y 3, pero no está relacionado con tu fondo de emergencia.
                </p>
              </div>
            </div>
          </div>
        </Card>
        
        {/* SECCIÓN 3: Registro de Aportes y Retiros */}
        <Card className={`lg:col-span-2 ${seccionActiva !== 'movimientos' && 'hidden md:block'}`}>
          <div className="p-6">
            <div className="flex items-center mb-4">
              <DollarSign className="w-6 h-6 text-green-400 mr-2" />
              <h2 className="text-xl font-semibold">Registro de Aportes y Retiros</h2>
            </div>
            
            <div className="overflow-hidden rounded-xl border border-gray-700">
              <div className="grid grid-cols-12 bg-[#2a2b38] p-3 border-b border-gray-700 text-sm font-medium text-gray-400">
                <div className="col-span-2 md:col-span-2">Fecha</div>
                <div className="col-span-2 md:col-span-1">Tipo</div>
                <div className="col-span-2 md:col-span-2">Monto</div>
                <div className="col-span-2 md:col-span-2">Medio</div>
                <div className="col-span-2 md:col-span-3">Motivo</div>
                <div className="col-span-2 md:col-span-2">Saldo Final</div>
              </div>
              
              <div className="max-h-[400px] overflow-y-auto bg-[#1e1f2c]">
                {movimientos.length > 0 ? (
                  movimientos.map((movimiento) => (
                    <div 
                      key={movimiento.id}
                      className="grid grid-cols-12 p-3 border-b border-gray-700 hover:bg-[#2a2b38] transition-colors"
                    >
                      <div className="col-span-2 md:col-span-2 text-gray-300">
                        {format(new Date(movimiento.date), 'dd MMM yyyy', { locale: es })}
                      </div>
                      <div className="col-span-2 md:col-span-1">
                        <span className={`inline-flex rounded-full px-2 py-1 text-xs font-medium ${
                          movimiento.type === 'expense' // Remember: expense = aporte for fund accounting
                            ? 'bg-green-900/30 text-green-400'
                            : 'bg-red-900/30 text-red-400'
                        }`}>
                          {movimiento.type === 'expense' ? 'Aporte' : 'Retiro'}
                        </span>
                      </div>
                      <div className={`col-span-2 md:col-span-2 font-medium ${
                        movimiento.type === 'expense' ? 'text-green-400' : 'text-red-400'
                      }`}>
                        {movimiento.type === 'expense' ? '+' : '-'}${movimiento.amount.toLocaleString()}
                      </div>
                      <div className="col-span-2 md:col-span-2 text-gray-300 capitalize">
                        {movimiento.medio || 'transferencia'}
                      </div>
                      <div className="col-span-2 md:col-span-3 text-gray-300 truncate">
                        {movimiento.description}
                      </div>
                      <div className="col-span-2 md:col-span-2 font-medium text-blue-300">
                        ${(movimiento.saldo_resultante || totalFondo).toLocaleString()}
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="p-8 text-center text-gray-500">
                    <DollarSign className="w-12 h-12 mx-auto mb-4 opacity-30" />
                    <p className="font-medium">No hay movimientos registrados</p>
                    <p className="text-sm mt-1">Agrega un aporte o retiro para comenzar</p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </Card>
      </div>
      
      {/* Modal para agregar movimientos */}
      {showModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-[#21222d] rounded-xl w-full max-w-md shadow-xl animate-fadeIn">
            <div className="p-6">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-semibold">Registrar Movimiento</h3>
                <button 
                  onClick={() => setShowModal(false)}
                  className="text-gray-500 hover:text-white"
                >
                  &times;
                </button>
              </div>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-1">
                    Tipo
                  </label>
                  <div className="grid grid-cols-2 gap-3">
                    <button
                      type="button"
                      className={`flex justify-center items-center space-x-1 rounded-lg p-3 font-medium transition-all ${
                        nuevoMovimiento.tipo === 'aporte' 
                          ? 'bg-green-600 text-white' 
                          : 'bg-green-900/20 border border-green-700 text-green-400 hover:bg-green-900/30'
                      }`}
                      onClick={() => setNuevoMovimiento({...nuevoMovimiento, tipo: 'aporte'})}
                    >
                      <ArrowUpRight className="w-4 h-4" />
                      <span>Aporte</span>
                    </button>
                    <button
                      type="button"
                      className={`flex justify-center items-center space-x-1 rounded-lg p-3 font-medium transition-all ${
                        nuevoMovimiento.tipo === 'retiro' 
                          ? 'bg-red-600 text-white' 
                          : 'bg-red-900/20 border border-red-700 text-red-400 hover:bg-red-900/30'
                      }`}
                      onClick={() => setNuevoMovimiento({...nuevoMovimiento, tipo: 'retiro'})}
                    >
                      <ArrowDownRight className="w-4 h-4" />
                      <span>Retiro</span>
                    </button>
                  </div>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-1">
                    Monto
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <span className="text-gray-500">$</span>
                    </div>
                    <input
                      type="number"
                      value={nuevoMovimiento.monto}
                      onChange={(e) => setNuevoMovimiento({...nuevoMovimiento, monto: e.target.value})}
                      className="w-full bg-[#2a2b38] border border-gray-700 rounded-lg p-2 pl-8 text-white"
                      placeholder="Ej. 500.00"
                    />
                  </div>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-1">
                    Medio
                  </label>
                  <select
                    value={nuevoMovimiento.medio}
                    onChange={(e) => setNuevoMovimiento({...nuevoMovimiento, medio: e.target.value})}
                    className="w-full bg-[#2a2b38] border border-gray-700 rounded-lg p-2 text-white"
                  >
                    <option value="transferencia">Transferencia</option>
                    <option value="efectivo">Efectivo</option>
                    <option value="otro">Otro</option>
                  </select>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-1">
                    Motivo o Comentario
                  </label>
                  <textarea
                    value={nuevoMovimiento.motivo}
                    onChange={(e) => setNuevoMovimiento({...nuevoMovimiento, motivo: e.target.value})}
                    className="w-full bg-[#2a2b38] border border-gray-700 rounded-lg p-2 text-white h-20"
                    placeholder="Ej. Ahorro de este mes"
                  ></textarea>
                </div>
              </div>
              
              <div className="mt-6 flex justify-end gap-2">
                <button
                  onClick={() => setShowModal(false)}
                  className="px-4 py-2 border border-gray-600 rounded-lg text-gray-300 hover:bg-gray-700"
                >
                  Cancelar
                </button>
                <button
                  onClick={agregarMovimiento}
                  className={`px-4 py-2 rounded-lg text-white ${
                    nuevoMovimiento.tipo === 'aporte'
                      ? 'bg-green-600 hover:bg-green-500'
                      : 'bg-red-600 hover:bg-red-500'
                  }`}
                  disabled={!nuevoMovimiento.monto || parseFloat(nuevoMovimiento.monto) <= 0}
                >
                  Guardar
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}