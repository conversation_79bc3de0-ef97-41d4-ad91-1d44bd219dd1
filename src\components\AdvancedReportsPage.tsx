import React, { useState, useMemo } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, TrendingUp, TrendingDown, Calendar, Download, Filter } from 'lucide-react';
import { useTheme } from '../lib/ThemeContext';
import { useTransactionContext } from '../lib/TransactionContext';
import { FadeIn, SlideIn } from './ui/LoadingStates';
import { InfoTooltip, HelpTooltip } from './ui/Tooltip';
import { format, startOfMonth, endOfMonth, subMonths, parseISO, isWithinInterval } from 'date-fns';
import { es } from 'date-fns/locale';

interface ReportFilters {
  dateRange: 'last3months' | 'last6months' | 'lastyear' | 'custom';
  startDate?: string;
  endDate?: string;
  categories: string[];
  transactionType: 'all' | 'income' | 'expense';
}

interface MonthlyData {
  month: string;
  income: number;
  expenses: number;
  balance: number;
}

interface CategoryData {
  category: string;
  amount: number;
  percentage: number;
  transactions: number;
}

export function AdvancedReportsPage() {
  const { theme } = useTheme();
  const { transactions } = useTransactionContext();
  
  const [activeReport, setActiveReport] = useState<'overview' | 'trends' | 'categories' | 'comparison'>('overview');
  const [filters, setFilters] = useState<ReportFilters>({
    dateRange: 'last6months',
    categories: [],
    transactionType: 'all'
  });

  // Get available categories
  const availableCategories = useMemo(() => {
    const categories = new Set(transactions.map(t => t.category));
    return Array.from(categories);
  }, [transactions]);

  // Filter transactions based on current filters
  const filteredTransactions = useMemo(() => {
    let filtered = transactions;

    // Date range filter
    const now = new Date();
    let startDate: Date;
    let endDate: Date = now;

    switch (filters.dateRange) {
      case 'last3months':
        startDate = subMonths(now, 3);
        break;
      case 'last6months':
        startDate = subMonths(now, 6);
        break;
      case 'lastyear':
        startDate = subMonths(now, 12);
        break;
      case 'custom':
        if (filters.startDate && filters.endDate) {
          startDate = parseISO(filters.startDate);
          endDate = parseISO(filters.endDate);
        } else {
          startDate = subMonths(now, 6);
        }
        break;
      default:
        startDate = subMonths(now, 6);
    }

    filtered = filtered.filter(t => {
      const transactionDate = parseISO(t.date);
      return isWithinInterval(transactionDate, { start: startDate, end: endDate });
    });

    // Transaction type filter
    if (filters.transactionType !== 'all') {
      filtered = filtered.filter(t => t.type === filters.transactionType);
    }

    // Category filter
    if (filters.categories.length > 0) {
      filtered = filtered.filter(t => filters.categories.includes(t.category));
    }

    return filtered;
  }, [transactions, filters]);

  // Calculate monthly data for trends
  const monthlyData = useMemo((): MonthlyData[] => {
    const monthlyMap = new Map<string, { income: number; expenses: number }>();

    filteredTransactions.forEach(transaction => {
      const monthKey = format(parseISO(transaction.date), 'yyyy-MM');
      
      if (!monthlyMap.has(monthKey)) {
        monthlyMap.set(monthKey, { income: 0, expenses: 0 });
      }

      const monthData = monthlyMap.get(monthKey)!;
      if (transaction.type === 'income') {
        monthData.income += transaction.amount;
      } else {
        monthData.expenses += transaction.amount;
      }
    });

    return Array.from(monthlyMap.entries())
      .map(([month, data]) => ({
        month: format(parseISO(month + '-01'), 'MMM yyyy', { locale: es }),
        income: data.income,
        expenses: data.expenses,
        balance: data.income - data.expenses
      }))
      .sort((a, b) => a.month.localeCompare(b.month));
  }, [filteredTransactions]);

  // Calculate category data
  const categoryData = useMemo((): CategoryData[] => {
    const categoryMap = new Map<string, { amount: number; transactions: number }>();
    const totalAmount = filteredTransactions.reduce((sum, t) => sum + t.amount, 0);

    filteredTransactions.forEach(transaction => {
      if (!categoryMap.has(transaction.category)) {
        categoryMap.set(transaction.category, { amount: 0, transactions: 0 });
      }

      const catData = categoryMap.get(transaction.category)!;
      catData.amount += transaction.amount;
      catData.transactions += 1;
    });

    return Array.from(categoryMap.entries())
      .map(([category, data]) => ({
        category,
        amount: data.amount,
        percentage: totalAmount > 0 ? (data.amount / totalAmount) * 100 : 0,
        transactions: data.transactions
      }))
      .sort((a, b) => b.amount - a.amount);
  }, [filteredTransactions]);

  // Calculate overview statistics
  const overviewStats = useMemo(() => {
    const totalIncome = filteredTransactions
      .filter(t => t.type === 'income')
      .reduce((sum, t) => sum + t.amount, 0);
    
    const totalExpenses = filteredTransactions
      .filter(t => t.type === 'expense')
      .reduce((sum, t) => sum + t.amount, 0);

    const balance = totalIncome - totalExpenses;
    const avgMonthlyIncome = monthlyData.length > 0 
      ? monthlyData.reduce((sum, m) => sum + m.income, 0) / monthlyData.length 
      : 0;
    const avgMonthlyExpenses = monthlyData.length > 0 
      ? monthlyData.reduce((sum, m) => sum + m.expenses, 0) / monthlyData.length 
      : 0;

    return {
      totalIncome,
      totalExpenses,
      balance,
      avgMonthlyIncome,
      avgMonthlyExpenses,
      totalTransactions: filteredTransactions.length,
      savingsRate: totalIncome > 0 ? ((totalIncome - totalExpenses) / totalIncome) * 100 : 0
    };
  }, [filteredTransactions, monthlyData]);

  const handleExportReport = () => {
    const reportData = {
      overview: overviewStats,
      monthly: monthlyData,
      categories: categoryData,
      filters,
      generatedAt: new Date().toISOString()
    };

    const blob = new Blob([JSON.stringify(reportData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `reporte-financiero-${format(new Date(), 'yyyy-MM-dd')}.json`;
    link.click();
    URL.revokeObjectURL(url);
  };

  const reportTabs = [
    { id: 'overview', name: 'Resumen', icon: <BarChart3 className="w-4 h-4" /> },
    { id: 'trends', name: 'Tendencias', icon: <LineChart className="w-4 h-4" /> },
    { id: 'categories', name: 'Categorías', icon: <PieChart className="w-4 h-4" /> },
    { id: 'comparison', name: 'Comparación', icon: <TrendingUp className="w-4 h-4" /> }
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div className="flex items-center gap-3">
          <div>
            <h1 className={`text-3xl font-bold transition-colors duration-200 ${
              theme === 'dark' ? 'text-white' : 'text-slate-900'
            }`}>
              Reportes Avanzados
            </h1>
            <p className={`text-sm mt-1 transition-colors duration-200 ${
              theme === 'dark' ? 'text-gray-400' : 'text-slate-500'
            }`}>
              Análisis detallado de tus finanzas con gráficos y tendencias
            </p>
          </div>
          <HelpTooltip content="Los reportes avanzados te permiten analizar tus patrones financieros con diferentes visualizaciones y filtros personalizables." />
        </div>
        
        <button
          onClick={handleExportReport}
          className="px-4 py-2 bg-green-500 hover:bg-green-600 text-white rounded-lg transition-colors duration-200 flex items-center gap-2"
        >
          <Download className="w-4 h-4" />
          Exportar Reporte
        </button>
      </div>

      {/* Filters */}
      <div className={`rounded-xl p-4 transition-colors duration-200 ${
        theme === 'dark' 
          ? 'bg-[#21222d] border border-[#2a2a3d]' 
          : 'bg-slate-50 border border-slate-200'
      }`}>
        <div className="flex items-center gap-2 mb-4">
          <Filter className="w-4 h-4" />
          <h3 className={`font-medium transition-colors duration-200 ${
            theme === 'dark' ? 'text-white' : 'text-slate-900'
          }`}>
            Filtros
          </h3>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {/* Date Range */}
          <div>
            <label className={`block text-sm font-medium mb-2 transition-colors duration-200 ${
              theme === 'dark' ? 'text-gray-300' : 'text-slate-700'
            }`}>
              Período
            </label>
            <select
              value={filters.dateRange}
              onChange={(e) => setFilters(prev => ({ ...prev, dateRange: e.target.value as any }))}
              className={`w-full px-3 py-2 rounded-lg border transition-colors duration-200 ${
                theme === 'dark'
                  ? 'bg-[#2a2b38] border-gray-600 text-white'
                  : 'bg-white border-slate-300 text-slate-900'
              }`}
            >
              <option value="last3months">Últimos 3 meses</option>
              <option value="last6months">Últimos 6 meses</option>
              <option value="lastyear">Último año</option>
              <option value="custom">Personalizado</option>
            </select>
          </div>

          {/* Transaction Type */}
          <div>
            <label className={`block text-sm font-medium mb-2 transition-colors duration-200 ${
              theme === 'dark' ? 'text-gray-300' : 'text-slate-700'
            }`}>
              Tipo
            </label>
            <select
              value={filters.transactionType}
              onChange={(e) => setFilters(prev => ({ ...prev, transactionType: e.target.value as any }))}
              className={`w-full px-3 py-2 rounded-lg border transition-colors duration-200 ${
                theme === 'dark'
                  ? 'bg-[#2a2b38] border-gray-600 text-white'
                  : 'bg-white border-slate-300 text-slate-900'
              }`}
            >
              <option value="all">Todos</option>
              <option value="income">Ingresos</option>
              <option value="expense">Gastos</option>
            </select>
          </div>

          {/* Custom Date Range */}
          {filters.dateRange === 'custom' && (
            <>
              <div>
                <label className={`block text-sm font-medium mb-2 transition-colors duration-200 ${
                  theme === 'dark' ? 'text-gray-300' : 'text-slate-700'
                }`}>
                  Fecha Inicio
                </label>
                <input
                  type="date"
                  value={filters.startDate || ''}
                  onChange={(e) => setFilters(prev => ({ ...prev, startDate: e.target.value }))}
                  className={`w-full px-3 py-2 rounded-lg border transition-colors duration-200 ${
                    theme === 'dark'
                      ? 'bg-[#2a2b38] border-gray-600 text-white'
                      : 'bg-white border-slate-300 text-slate-900'
                  }`}
                />
              </div>

              <div>
                <label className={`block text-sm font-medium mb-2 transition-colors duration-200 ${
                  theme === 'dark' ? 'text-gray-300' : 'text-slate-700'
                }`}>
                  Fecha Fin
                </label>
                <input
                  type="date"
                  value={filters.endDate || ''}
                  onChange={(e) => setFilters(prev => ({ ...prev, endDate: e.target.value }))}
                  className={`w-full px-3 py-2 rounded-lg border transition-colors duration-200 ${
                    theme === 'dark'
                      ? 'bg-[#2a2b38] border-gray-600 text-white'
                      : 'bg-white border-slate-300 text-slate-900'
                  }`}
                />
              </div>
            </>
          )}
        </div>
      </div>

      {/* Report Tabs */}
      <div className="flex space-x-1 p-1 bg-gray-100 dark:bg-gray-800 rounded-lg">
        {reportTabs.map((tab) => (
          <button
            key={tab.id}
            onClick={() => setActiveReport(tab.id as any)}
            className={`flex-1 flex items-center justify-center gap-2 py-2 px-4 rounded-md text-sm font-medium transition-colors duration-200 ${
              activeReport === tab.id
                ? 'bg-white dark:bg-gray-700 text-blue-600 dark:text-blue-400 shadow-sm'
                : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200'
            }`}
          >
            {tab.icon}
            {tab.name}
          </button>
        ))}
      </div>

      {/* Report Content */}
      <div className="space-y-6">
        {activeReport === 'overview' && (
          <FadeIn>
            <div className="space-y-6">
              {/* Overview Statistics */}
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <InfoTooltip title="Ingresos Totales" description="Suma de todos los ingresos en el período seleccionado">
                  <div className={`rounded-xl p-4 transition-colors duration-200 ${
                    theme === 'dark'
                      ? 'bg-[#21222d] border border-[#2a2a3d]'
                      : 'bg-slate-50 border border-slate-200'
                  }`}>
                    <div className="flex items-center">
                      <div className="p-2 bg-green-500 rounded-lg">
                        <TrendingUp className="w-5 h-5 text-white" />
                      </div>
                      <div className="ml-3">
                        <p className={`text-sm transition-colors duration-200 ${
                          theme === 'dark' ? 'text-gray-400' : 'text-slate-500'
                        }`}>
                          Ingresos Totales
                        </p>
                        <p className="text-lg font-bold text-green-500">
                          ${overviewStats.totalIncome.toLocaleString()}
                        </p>
                      </div>
                    </div>
                  </div>
                </InfoTooltip>

                <div className={`rounded-xl p-4 transition-colors duration-200 ${
                  theme === 'dark'
                    ? 'bg-[#21222d] border border-[#2a2a3d]'
                    : 'bg-slate-50 border border-slate-200'
                }`}>
                  <div className="flex items-center">
                    <div className="p-2 bg-red-500 rounded-lg">
                      <TrendingDown className="w-5 h-5 text-white" />
                    </div>
                    <div className="ml-3">
                      <p className={`text-sm transition-colors duration-200 ${
                        theme === 'dark' ? 'text-gray-400' : 'text-slate-500'
                      }`}>
                        Gastos Totales
                      </p>
                      <p className="text-lg font-bold text-red-500">
                        ${overviewStats.totalExpenses.toLocaleString()}
                      </p>
                    </div>
                  </div>
                </div>

                <div className={`rounded-xl p-4 transition-colors duration-200 ${
                  theme === 'dark'
                    ? 'bg-[#21222d] border border-[#2a2a3d]'
                    : 'bg-slate-50 border border-slate-200'
                }`}>
                  <div className="flex items-center">
                    <div className={`p-2 rounded-lg ${overviewStats.balance >= 0 ? 'bg-blue-500' : 'bg-orange-500'}`}>
                      <BarChart3 className="w-5 h-5 text-white" />
                    </div>
                    <div className="ml-3">
                      <p className={`text-sm transition-colors duration-200 ${
                        theme === 'dark' ? 'text-gray-400' : 'text-slate-500'
                      }`}>
                        Balance
                      </p>
                      <p className={`text-lg font-bold ${
                        overviewStats.balance >= 0 ? 'text-blue-500' : 'text-orange-500'
                      }`}>
                        ${overviewStats.balance.toLocaleString()}
                      </p>
                    </div>
                  </div>
                </div>

                <div className={`rounded-xl p-4 transition-colors duration-200 ${
                  theme === 'dark'
                    ? 'bg-[#21222d] border border-[#2a2a3d]'
                    : 'bg-slate-50 border border-slate-200'
                }`}>
                  <div className="flex items-center">
                    <div className="p-2 bg-purple-500 rounded-lg">
                      <Calendar className="w-5 h-5 text-white" />
                    </div>
                    <div className="ml-3">
                      <p className={`text-sm transition-colors duration-200 ${
                        theme === 'dark' ? 'text-gray-400' : 'text-slate-500'
                      }`}>
                        Tasa de Ahorro
                      </p>
                      <p className="text-lg font-bold text-purple-500">
                        {overviewStats.savingsRate.toFixed(1)}%
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Key Insights */}
              <div className={`rounded-xl p-6 transition-colors duration-200 ${
                theme === 'dark'
                  ? 'bg-[#21222d] border border-[#2a2a3d]'
                  : 'bg-slate-50 border border-slate-200'
              }`}>
                <h3 className={`text-lg font-semibold mb-4 transition-colors duration-200 ${
                  theme === 'dark' ? 'text-white' : 'text-slate-900'
                }`}>
                  Insights Clave
                </h3>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className={`p-4 rounded-lg transition-colors duration-200 ${
                    theme === 'dark'
                      ? 'bg-[#2a2b38] border border-gray-600'
                      : 'bg-white border border-slate-200'
                  }`}>
                    <h4 className={`font-medium mb-2 transition-colors duration-200 ${
                      theme === 'dark' ? 'text-white' : 'text-slate-900'
                    }`}>
                      Promedio Mensual
                    </h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className={theme === 'dark' ? 'text-gray-400' : 'text-slate-600'}>Ingresos:</span>
                        <span className="text-green-500 font-medium">${overviewStats.avgMonthlyIncome.toLocaleString()}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className={theme === 'dark' ? 'text-gray-400' : 'text-slate-600'}>Gastos:</span>
                        <span className="text-red-500 font-medium">${overviewStats.avgMonthlyExpenses.toLocaleString()}</span>
                      </div>
                    </div>
                  </div>

                  <div className={`p-4 rounded-lg transition-colors duration-200 ${
                    theme === 'dark'
                      ? 'bg-[#2a2b38] border border-gray-600'
                      : 'bg-white border border-slate-200'
                  }`}>
                    <h4 className={`font-medium mb-2 transition-colors duration-200 ${
                      theme === 'dark' ? 'text-white' : 'text-slate-900'
                    }`}>
                      Actividad
                    </h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className={theme === 'dark' ? 'text-gray-400' : 'text-slate-600'}>Total transacciones:</span>
                        <span className="text-blue-500 font-medium">{overviewStats.totalTransactions}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className={theme === 'dark' ? 'text-gray-400' : 'text-slate-600'}>Promedio por mes:</span>
                        <span className="text-blue-500 font-medium">
                          {monthlyData.length > 0 ? Math.round(overviewStats.totalTransactions / monthlyData.length) : 0}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </FadeIn>
        )}

        {activeReport === 'trends' && (
          <FadeIn>
            <div className="space-y-6">
              <div className={`rounded-xl p-6 transition-colors duration-200 ${
                theme === 'dark'
                  ? 'bg-[#21222d] border border-[#2a2a3d]'
                  : 'bg-slate-50 border border-slate-200'
              }`}>
                <h3 className={`text-lg font-semibold mb-4 transition-colors duration-200 ${
                  theme === 'dark' ? 'text-white' : 'text-slate-900'
                }`}>
                  Tendencias Mensuales
                </h3>

                {monthlyData.length > 0 ? (
                  <div className="space-y-4">
                    {monthlyData.map((month, index) => (
                      <SlideIn key={month.month} direction="left" delay={index * 100}>
                        <div className={`p-4 rounded-lg transition-colors duration-200 ${
                          theme === 'dark'
                            ? 'bg-[#2a2b38] border border-gray-600'
                            : 'bg-white border border-slate-200'
                        }`}>
                          <div className="flex justify-between items-center mb-2">
                            <h4 className={`font-medium transition-colors duration-200 ${
                              theme === 'dark' ? 'text-white' : 'text-slate-900'
                            }`}>
                              {month.month}
                            </h4>
                            <span className={`text-sm font-medium ${
                              month.balance >= 0 ? 'text-green-500' : 'text-red-500'
                            }`}>
                              Balance: ${month.balance.toLocaleString()}
                            </span>
                          </div>

                          <div className="grid grid-cols-2 gap-4 text-sm">
                            <div>
                              <span className={theme === 'dark' ? 'text-gray-400' : 'text-slate-600'}>Ingresos: </span>
                              <span className="text-green-500 font-medium">${month.income.toLocaleString()}</span>
                            </div>
                            <div>
                              <span className={theme === 'dark' ? 'text-gray-400' : 'text-slate-600'}>Gastos: </span>
                              <span className="text-red-500 font-medium">${month.expenses.toLocaleString()}</span>
                            </div>
                          </div>
                        </div>
                      </SlideIn>
                    ))}
                  </div>
                ) : (
                  <div className={`text-center py-8 transition-colors duration-200 ${
                    theme === 'dark' ? 'text-gray-500' : 'text-slate-500'
                  }`}>
                    <LineChart className="w-12 h-12 mx-auto mb-4 opacity-50" />
                    <p>No hay datos suficientes para mostrar tendencias</p>
                  </div>
                )}
              </div>
            </div>
          </FadeIn>
        )}

        {activeReport === 'categories' && (
          <FadeIn>
            <div className="space-y-6">
              <div className={`rounded-xl p-6 transition-colors duration-200 ${
                theme === 'dark'
                  ? 'bg-[#21222d] border border-[#2a2a3d]'
                  : 'bg-slate-50 border border-slate-200'
              }`}>
                <h3 className={`text-lg font-semibold mb-4 transition-colors duration-200 ${
                  theme === 'dark' ? 'text-white' : 'text-slate-900'
                }`}>
                  Análisis por Categorías
                </h3>

                {categoryData.length > 0 ? (
                  <div className="space-y-3">
                    {categoryData.map((category, index) => (
                      <SlideIn key={category.category} direction="right" delay={index * 50}>
                        <div className={`p-4 rounded-lg transition-colors duration-200 ${
                          theme === 'dark'
                            ? 'bg-[#2a2b38] border border-gray-600'
                            : 'bg-white border border-slate-200'
                        }`}>
                          <div className="flex justify-between items-center mb-2">
                            <h4 className={`font-medium transition-colors duration-200 ${
                              theme === 'dark' ? 'text-white' : 'text-slate-900'
                            }`}>
                              {category.category}
                            </h4>
                            <div className="text-right">
                              <div className="text-lg font-bold text-blue-500">
                                ${category.amount.toLocaleString()}
                              </div>
                              <div className={`text-sm transition-colors duration-200 ${
                                theme === 'dark' ? 'text-gray-400' : 'text-slate-600'
                              }`}>
                                {category.percentage.toFixed(1)}%
                              </div>
                            </div>
                          </div>

                          <div className="flex justify-between items-center text-sm">
                            <span className={theme === 'dark' ? 'text-gray-400' : 'text-slate-600'}>
                              {category.transactions} transacciones
                            </span>
                            <div className={`w-full max-w-xs ml-4 bg-gray-200 dark:bg-gray-700 rounded-full h-2`}>
                              <div
                                className="bg-blue-500 h-2 rounded-full transition-all duration-500"
                                style={{ width: `${category.percentage}%` }}
                              />
                            </div>
                          </div>
                        </div>
                      </SlideIn>
                    ))}
                  </div>
                ) : (
                  <div className={`text-center py-8 transition-colors duration-200 ${
                    theme === 'dark' ? 'text-gray-500' : 'text-slate-500'
                  }`}>
                    <PieChart className="w-12 h-12 mx-auto mb-4 opacity-50" />
                    <p>No hay datos de categorías para mostrar</p>
                  </div>
                )}
              </div>
            </div>
          </FadeIn>
        )}

        {activeReport === 'comparison' && (
          <FadeIn>
            <div className="space-y-6">
              <div className={`rounded-xl p-6 transition-colors duration-200 ${
                theme === 'dark'
                  ? 'bg-[#21222d] border border-[#2a2a3d]'
                  : 'bg-slate-50 border border-slate-200'
              }`}>
                <h3 className={`text-lg font-semibold mb-4 transition-colors duration-200 ${
                  theme === 'dark' ? 'text-white' : 'text-slate-900'
                }`}>
                  Comparación de Períodos
                </h3>

                <div className={`text-center py-8 transition-colors duration-200 ${
                  theme === 'dark' ? 'text-gray-500' : 'text-slate-500'
                }`}>
                  <TrendingUp className="w-12 h-12 mx-auto mb-4 opacity-50" />
                  <p className="font-medium">Comparación de Períodos</p>
                  <p className="text-sm mt-1">Esta funcionalidad estará disponible próximamente</p>
                </div>
              </div>
            </div>
          </FadeIn>
        )}
      </div>
    </div>
  );
}
