import React, { Component, ReactNode } from 'react';
import { Alert<PERSON>riangle, RefreshCw, Home } from 'lucide-react';
import { Link } from 'react-router-dom';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
  errorInfo?: React.ErrorInfo;
}

export class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('ErrorBoundary caught an error:', error, errorInfo);
    this.setState({
      error,
      errorInfo
    });
  }

  handleRefresh = () => {
    window.location.reload();
  };

  handleReset = () => {
    this.setState({ hasError: false, error: undefined, errorInfo: undefined });
  };

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback;
      }

      return (
        <div className="min-h-screen bg-[#171821] flex items-center justify-center p-4">
          <div className="bg-[#21222d] rounded-xl p-8 max-w-md w-full text-center border border-[#2a2a3d]">
            <div className="flex justify-center mb-4">
              <div className="w-16 h-16 bg-red-500/20 rounded-full flex items-center justify-center">
                <AlertTriangle className="w-8 h-8 text-red-400" />
              </div>
            </div>
            
            <h2 className="text-xl font-bold text-white mb-2">
              ¡Oops! Algo salió mal
            </h2>
            
            <p className="text-gray-300 mb-6">
              Ha ocurrido un error inesperado. No te preocupes, tus datos están seguros.
            </p>

            {process.env.NODE_ENV === 'development' && this.state.error && (
              <div className="mb-6 p-3 bg-red-900/30 border border-red-800 rounded-lg text-left">
                <p className="text-red-300 text-sm font-medium mb-2">Error Details:</p>
                <p className="text-red-300 text-xs font-mono break-all">
                  {this.state.error.message}
                </p>
              </div>
            )}

            <div className="space-y-3">
              <button
                onClick={this.handleReset}
                className="w-full bg-blue-600 hover:bg-blue-500 text-white rounded-lg p-3 font-medium flex items-center justify-center transition-colors"
              >
                <RefreshCw className="w-4 h-4 mr-2" />
                Intentar de nuevo
              </button>
              
              <button
                onClick={this.handleRefresh}
                className="w-full bg-gray-700 hover:bg-gray-600 text-white rounded-lg p-3 font-medium flex items-center justify-center transition-colors"
              >
                <RefreshCw className="w-4 h-4 mr-2" />
                Recargar página
              </button>
              
              <Link
                to="/"
                className="w-full bg-[#f9769d] hover:bg-[#f98bab] text-white rounded-lg p-3 font-medium flex items-center justify-center transition-colors"
              >
                <Home className="w-4 h-4 mr-2" />
                Ir al inicio
              </Link>
            </div>
            
            <p className="text-gray-400 text-xs mt-6">
              Si el problema persiste, puedes contactar al soporte técnico.
            </p>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

// Hook for functional components error handling
export function useErrorHandler() {
  const [error, setError] = React.useState<Error | null>(null);

  const resetError = () => setError(null);

  const captureError = React.useCallback((error: Error | string) => {
    const errorObj = typeof error === 'string' ? new Error(error) : error;
    setError(errorObj);
    console.error('Error captured:', errorObj);
  }, []);

  React.useEffect(() => {
    if (error) {
      throw error; // This will be caught by ErrorBoundary
    }
  }, [error]);

  return { captureError, resetError, error };
}