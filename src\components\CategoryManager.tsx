import React, { useState, useEffect } from 'react';
import { Plus, Edit, Trash2, Save, X, Tag } from 'lucide-react';
import { useTheme } from '../lib/ThemeContext';
import { useToast } from './Toast';

export interface CustomCategory {
  id: string;
  name: string;
  type: 'income' | 'expense';
  icon: string;
  color: string;
  user_id: string;
}

interface CategoryManagerProps {
  isOpen: boolean;
  onClose: () => void;
  onCategoryChange: () => void;
}

const defaultIcons = ['💰', '💸', '🏠', '🍎', '🚗', '🎬', '📱', '🩺', '📚', '🛍️', '📺', '💳', '🏦', '🧰', '🎯', '📈', '₿', '💹'];
const defaultColors = ['#f9769d', '#96a7ff', '#0aefff', '#b66dff', '#ff6b6b', '#4ecdc4', '#45b7d1', '#f39c12', '#e74c3c', '#9b59b6', '#1abc9c', '#34495e'];

export function CategoryManager({ isOpen, onClose, onCategoryChange }: CategoryManagerProps) {
  const { theme } = useTheme();
  const { showSuccess, showError } = useToast();
  
  const [categories, setCategories] = useState<CustomCategory[]>([]);
  const [loading, setLoading] = useState(false);
  const [editingCategory, setEditingCategory] = useState<CustomCategory | null>(null);
  const [showAddForm, setShowAddForm] = useState(false);
  
  const [formData, setFormData] = useState({
    name: '',
    type: 'expense' as 'income' | 'expense',
    icon: '💸',
    color: '#f9769d'
  });

  // Load categories from localStorage (in a real app, this would be from Supabase)
  useEffect(() => {
    if (isOpen) {
      loadCategories();
    }
  }, [isOpen]);

  const loadCategories = () => {
    try {
      const savedCategories = localStorage.getItem('customCategories');
      if (savedCategories) {
        setCategories(JSON.parse(savedCategories));
      }
    } catch (error) {
      console.error('Error loading categories:', error);
      showError('Error', 'No se pudieron cargar las categorías');
    }
  };

  const saveCategories = (newCategories: CustomCategory[]) => {
    try {
      localStorage.setItem('customCategories', JSON.stringify(newCategories));
      setCategories(newCategories);
      onCategoryChange();
    } catch (error) {
      console.error('Error saving categories:', error);
      showError('Error', 'No se pudieron guardar las categorías');
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.name.trim()) {
      showError('Error', 'El nombre de la categoría es requerido');
      return;
    }

    if (editingCategory) {
      // Update existing category
      const updatedCategories = categories.map(cat =>
        cat.id === editingCategory.id
          ? { ...cat, ...formData }
          : cat
      );
      saveCategories(updatedCategories);
      showSuccess('Categoría actualizada', 'La categoría se actualizó correctamente');
      setEditingCategory(null);
    } else {
      // Add new category
      const newCategory: CustomCategory = {
        id: Date.now().toString(),
        ...formData,
        user_id: 'current-user' // In a real app, this would be the actual user ID
      };
      saveCategories([...categories, newCategory]);
      showSuccess('Categoría creada', 'La nueva categoría se creó correctamente');
      setShowAddForm(false);
    }

    // Reset form
    setFormData({
      name: '',
      type: 'expense',
      icon: '💸',
      color: '#f9769d'
    });
  };

  const handleEdit = (category: CustomCategory) => {
    setEditingCategory(category);
    setFormData({
      name: category.name,
      type: category.type,
      icon: category.icon,
      color: category.color
    });
    setShowAddForm(true);
  };

  const handleDelete = (categoryId: string) => {
    if (window.confirm('¿Estás seguro de que quieres eliminar esta categoría?')) {
      const updatedCategories = categories.filter(cat => cat.id !== categoryId);
      saveCategories(updatedCategories);
      showSuccess('Categoría eliminada', 'La categoría se eliminó correctamente');
    }
  };

  const handleCancel = () => {
    setShowAddForm(false);
    setEditingCategory(null);
    setFormData({
      name: '',
      type: 'expense',
      icon: '💸',
      color: '#f9769d'
    });
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className={`rounded-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto transition-colors duration-200 ${
        theme === 'dark' 
          ? 'bg-[#21222d] border border-[#2a2a3d]' 
          : 'bg-white border border-slate-200 shadow-xl'
      }`}>
        {/* Header */}
        <div className="flex justify-between items-center p-6 border-b border-gray-600">
          <h2 className={`text-xl font-bold transition-colors duration-200 ${
            theme === 'dark' ? 'text-white' : 'text-slate-900'
          }`}>
            Gestionar Categorías
          </h2>
          <button
            onClick={onClose}
            className={`p-1 rounded-lg transition-colors duration-200 ${
              theme === 'dark'
                ? 'text-gray-400 hover:text-white hover:bg-gray-700'
                : 'text-slate-400 hover:text-slate-600 hover:bg-slate-100'
            }`}
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          {/* Add Category Button */}
          {!showAddForm && (
            <button
              onClick={() => setShowAddForm(true)}
              className="w-full mb-6 py-3 px-4 rounded-lg border-2 border-dashed border-gray-600 text-gray-400 hover:border-blue-500 hover:text-blue-500 transition-colors duration-200 flex items-center justify-center gap-2"
            >
              <Plus className="w-5 h-5" />
              Agregar Nueva Categoría
            </button>
          )}

          {/* Add/Edit Form */}
          {showAddForm && (
            <form onSubmit={handleSubmit} className={`mb-6 p-4 rounded-lg border transition-colors duration-200 ${
              theme === 'dark' 
                ? 'bg-[#2a2b38] border-gray-600' 
                : 'bg-slate-50 border-slate-200'
            }`}>
              <h3 className={`text-lg font-semibold mb-4 transition-colors duration-200 ${
                theme === 'dark' ? 'text-white' : 'text-slate-900'
              }`}>
                {editingCategory ? 'Editar Categoría' : 'Nueva Categoría'}
              </h3>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Name */}
                <div>
                  <label className={`block text-sm font-medium mb-2 transition-colors duration-200 ${
                    theme === 'dark' ? 'text-gray-300' : 'text-slate-700'
                  }`}>
                    Nombre
                  </label>
                  <input
                    type="text"
                    value={formData.name}
                    onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                    className={`w-full px-3 py-2 rounded-lg border transition-colors duration-200 ${
                      theme === 'dark'
                        ? 'bg-[#21222d] border-gray-600 text-white placeholder-gray-400'
                        : 'bg-white border-slate-300 text-slate-900 placeholder-slate-400'
                    }`}
                    placeholder="Ej. Mascotas"
                    required
                  />
                </div>

                {/* Type */}
                <div>
                  <label className={`block text-sm font-medium mb-2 transition-colors duration-200 ${
                    theme === 'dark' ? 'text-gray-300' : 'text-slate-700'
                  }`}>
                    Tipo
                  </label>
                  <select
                    value={formData.type}
                    onChange={(e) => setFormData(prev => ({ ...prev, type: e.target.value as 'income' | 'expense' }))}
                    className={`w-full px-3 py-2 rounded-lg border transition-colors duration-200 ${
                      theme === 'dark'
                        ? 'bg-[#21222d] border-gray-600 text-white'
                        : 'bg-white border-slate-300 text-slate-900'
                    }`}
                  >
                    <option value="expense">Gasto</option>
                    <option value="income">Ingreso</option>
                  </select>
                </div>

                {/* Icon */}
                <div>
                  <label className={`block text-sm font-medium mb-2 transition-colors duration-200 ${
                    theme === 'dark' ? 'text-gray-300' : 'text-slate-700'
                  }`}>
                    Icono
                  </label>
                  <div className="grid grid-cols-6 gap-2">
                    {defaultIcons.map(icon => (
                      <button
                        key={icon}
                        type="button"
                        onClick={() => setFormData(prev => ({ ...prev, icon }))}
                        className={`p-2 rounded-lg border transition-colors duration-200 ${
                          formData.icon === icon
                            ? 'border-blue-500 bg-blue-500/20'
                            : theme === 'dark'
                              ? 'border-gray-600 hover:border-gray-500'
                              : 'border-slate-300 hover:border-slate-400'
                        }`}
                      >
                        {icon}
                      </button>
                    ))}
                  </div>
                </div>

                {/* Color */}
                <div>
                  <label className={`block text-sm font-medium mb-2 transition-colors duration-200 ${
                    theme === 'dark' ? 'text-gray-300' : 'text-slate-700'
                  }`}>
                    Color
                  </label>
                  <div className="grid grid-cols-6 gap-2">
                    {defaultColors.map(color => (
                      <button
                        key={color}
                        type="button"
                        onClick={() => setFormData(prev => ({ ...prev, color }))}
                        className={`w-8 h-8 rounded-lg border-2 transition-all duration-200 ${
                          formData.color === color
                            ? 'border-white scale-110'
                            : 'border-gray-600 hover:scale-105'
                        }`}
                        style={{ backgroundColor: color }}
                      />
                    ))}
                  </div>
                </div>
              </div>

              {/* Form Actions */}
              <div className="flex gap-3 mt-6">
                <button
                  type="button"
                  onClick={handleCancel}
                  className={`flex-1 py-2 px-4 rounded-lg border transition-colors duration-200 ${
                    theme === 'dark'
                      ? 'border-gray-600 text-gray-300 hover:bg-gray-700'
                      : 'border-slate-300 text-slate-700 hover:bg-slate-50'
                  }`}
                >
                  Cancelar
                </button>
                <button
                  type="submit"
                  className="flex-1 py-2 px-4 rounded-lg bg-blue-500 hover:bg-blue-600 text-white transition-colors duration-200 flex items-center justify-center gap-2"
                >
                  <Save className="w-4 h-4" />
                  {editingCategory ? 'Actualizar' : 'Crear'}
                </button>
              </div>
            </form>
          )}

          {/* Categories List */}
          <div className="space-y-3">
            <h3 className={`text-lg font-semibold transition-colors duration-200 ${
              theme === 'dark' ? 'text-white' : 'text-slate-900'
            }`}>
              Categorías Personalizadas ({categories.length})
            </h3>

            {categories.length === 0 ? (
              <div className={`text-center py-8 transition-colors duration-200 ${
                theme === 'dark' ? 'text-gray-500' : 'text-slate-500'
              }`}>
                <Tag className="w-12 h-12 mx-auto mb-4 opacity-50" />
                <p className="font-medium">No hay categorías personalizadas</p>
                <p className="text-sm mt-1">Crea tu primera categoría personalizada</p>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                {categories.map(category => (
                  <div
                    key={category.id}
                    className={`p-4 rounded-lg border transition-colors duration-200 ${
                      theme === 'dark'
                        ? 'bg-[#2a2b38] border-gray-600'
                        : 'bg-slate-50 border-slate-200'
                    }`}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div
                          className="w-10 h-10 rounded-lg flex items-center justify-center text-white font-medium"
                          style={{ backgroundColor: category.color }}
                        >
                          {category.icon}
                        </div>
                        <div>
                          <h4 className={`font-medium transition-colors duration-200 ${
                            theme === 'dark' ? 'text-white' : 'text-slate-900'
                          }`}>
                            {category.name}
                          </h4>
                          <p className={`text-sm transition-colors duration-200 ${
                            theme === 'dark' ? 'text-gray-400' : 'text-slate-500'
                          }`}>
                            {category.type === 'income' ? 'Ingreso' : 'Gasto'}
                          </p>
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-2">
                        <button
                          onClick={() => handleEdit(category)}
                          className={`p-1 rounded transition-colors duration-200 ${
                            theme === 'dark'
                              ? 'text-gray-400 hover:text-blue-400 hover:bg-blue-900/20'
                              : 'text-slate-400 hover:text-blue-600 hover:bg-blue-50'
                          }`}
                          title="Editar categoría"
                        >
                          <Edit className="w-4 h-4" />
                        </button>
                        
                        <button
                          onClick={() => handleDelete(category.id)}
                          className={`p-1 rounded transition-colors duration-200 ${
                            theme === 'dark'
                              ? 'text-gray-400 hover:text-red-400 hover:bg-red-900/20'
                              : 'text-slate-400 hover:text-red-600 hover:bg-red-50'
                          }`}
                          title="Eliminar categoría"
                        >
                          <Trash2 className="w-4 h-4" />
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
