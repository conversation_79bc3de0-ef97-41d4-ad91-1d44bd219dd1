import { useState, useEffect, useCallback } from 'react';
import { useTransactionContext } from '../lib/TransactionContext';

interface OfflineAction {
  id: string;
  type: 'CREATE' | 'UPDATE' | 'DELETE';
  entity: 'transaction' | 'budget' | 'goal' | 'category';
  data: any;
  timestamp: string;
  synced: boolean;
}

interface OfflineState {
  isOnline: boolean;
  pendingActions: OfflineAction[];
  lastSyncTime: string | null;
  syncInProgress: boolean;
}

export function useOfflineSync() {
  const [state, setState] = useState<OfflineState>({
    isOnline: navigator.onLine,
    pendingActions: [],
    lastSyncTime: null,
    syncInProgress: false
  });

  // Load pending actions from localStorage
  useEffect(() => {
    const saved = localStorage.getItem('offlinePendingActions');
    if (saved) {
      const pendingActions = JSON.parse(saved);
      setState(prev => ({ ...prev, pendingActions }));
    }

    const lastSync = localStorage.getItem('lastSyncTime');
    if (lastSync) {
      setState(prev => ({ ...prev, lastSyncTime: lastSync }));
    }
  }, []);

  // Save pending actions to localStorage
  const savePendingActions = useCallback((actions: OfflineAction[]) => {
    localStorage.setItem('offlinePendingActions', JSON.stringify(actions));
    setState(prev => ({ ...prev, pendingActions: actions }));
  }, []);

  // Add action to pending queue
  const addPendingAction = useCallback((action: Omit<OfflineAction, 'id' | 'timestamp' | 'synced'>) => {
    const newAction: OfflineAction = {
      ...action,
      id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
      timestamp: new Date().toISOString(),
      synced: false
    };

    const updatedActions = [...state.pendingActions, newAction];
    savePendingActions(updatedActions);

    // If online, try to sync immediately
    if (state.isOnline) {
      syncPendingActions();
    }
  }, [state.pendingActions, state.isOnline]);

  // Sync pending actions when online
  const syncPendingActions = useCallback(async () => {
    if (!state.isOnline || state.syncInProgress || state.pendingActions.length === 0) {
      return;
    }

    setState(prev => ({ ...prev, syncInProgress: true }));

    try {
      const unsyncedActions = state.pendingActions.filter(action => !action.synced);
      
      for (const action of unsyncedActions) {
        try {
          // Simulate API call - in real app, this would be actual API calls
          await simulateApiCall(action);
          
          // Mark as synced
          const updatedActions = state.pendingActions.map(a => 
            a.id === action.id ? { ...a, synced: true } : a
          );
          savePendingActions(updatedActions);
        } catch (error) {
          console.error('Failed to sync action:', action.id, error);
          // Keep action in queue for retry
        }
      }

      // Remove synced actions older than 24 hours
      const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
      const filteredActions = state.pendingActions.filter(action => 
        !action.synced || new Date(action.timestamp) > oneDayAgo
      );
      
      if (filteredActions.length !== state.pendingActions.length) {
        savePendingActions(filteredActions);
      }

      // Update last sync time
      const now = new Date().toISOString();
      localStorage.setItem('lastSyncTime', now);
      setState(prev => ({ ...prev, lastSyncTime: now }));

    } finally {
      setState(prev => ({ ...prev, syncInProgress: false }));
    }
  }, [state.isOnline, state.syncInProgress, state.pendingActions, savePendingActions]);

  // Simulate API call for demo purposes
  const simulateApiCall = async (action: OfflineAction): Promise<void> => {
    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 500 + Math.random() * 1000));
    
    // Simulate occasional failures
    if (Math.random() < 0.1) {
      throw new Error('Network error');
    }
    
    console.log('Synced action:', action.type, action.entity, action.id);
  };

  // Handle online/offline events
  useEffect(() => {
    const handleOnline = () => {
      setState(prev => ({ ...prev, isOnline: true }));
      // Sync when coming back online
      setTimeout(syncPendingActions, 1000);
    };

    const handleOffline = () => {
      setState(prev => ({ ...prev, isOnline: false }));
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, [syncPendingActions]);

  // Auto-sync every 5 minutes when online
  useEffect(() => {
    if (!state.isOnline) return;

    const interval = setInterval(() => {
      syncPendingActions();
    }, 5 * 60 * 1000); // 5 minutes

    return () => clearInterval(interval);
  }, [state.isOnline, syncPendingActions]);

  // Get offline status summary
  const getOfflineStatus = useCallback(() => {
    const unsyncedCount = state.pendingActions.filter(a => !a.synced).length;
    const lastSyncDate = state.lastSyncTime ? new Date(state.lastSyncTime) : null;
    
    return {
      isOnline: state.isOnline,
      unsyncedCount,
      lastSyncDate,
      syncInProgress: state.syncInProgress,
      canSync: state.isOnline && unsyncedCount > 0 && !state.syncInProgress
    };
  }, [state]);

  // Manual sync trigger
  const manualSync = useCallback(async () => {
    if (state.isOnline && !state.syncInProgress) {
      await syncPendingActions();
    }
  }, [state.isOnline, state.syncInProgress, syncPendingActions]);

  // Clear all pending actions (for testing/reset)
  const clearPendingActions = useCallback(() => {
    localStorage.removeItem('offlinePendingActions');
    setState(prev => ({ ...prev, pendingActions: [] }));
  }, []);

  // Get actions by type
  const getActionsByType = useCallback((type: OfflineAction['type']) => {
    return state.pendingActions.filter(action => action.type === type);
  }, [state.pendingActions]);

  // Get actions by entity
  const getActionsByEntity = useCallback((entity: OfflineAction['entity']) => {
    return state.pendingActions.filter(action => action.entity === entity);
  }, [state.pendingActions]);

  // Check if specific item has pending changes
  const hasPendingChanges = useCallback((entity: OfflineAction['entity'], itemId: string) => {
    return state.pendingActions.some(action => 
      action.entity === entity && 
      action.data.id === itemId && 
      !action.synced
    );
  }, [state.pendingActions]);

  // Get conflict resolution data
  const getConflicts = useCallback(() => {
    // Group actions by entity and ID to detect conflicts
    const conflicts: Array<{
      entity: OfflineAction['entity'];
      itemId: string;
      actions: OfflineAction[];
    }> = [];

    const groupedActions = state.pendingActions.reduce((acc, action) => {
      const key = `${action.entity}-${action.data.id}`;
      if (!acc[key]) acc[key] = [];
      acc[key].push(action);
      return acc;
    }, {} as Record<string, OfflineAction[]>);

    Object.entries(groupedActions).forEach(([key, actions]) => {
      if (actions.length > 1) {
        const [entity, itemId] = key.split('-');
        conflicts.push({
          entity: entity as OfflineAction['entity'],
          itemId,
          actions: actions.sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime())
        });
      }
    });

    return conflicts;
  }, [state.pendingActions]);

  return {
    ...state,
    addPendingAction,
    syncPendingActions,
    manualSync,
    clearPendingActions,
    getOfflineStatus,
    getActionsByType,
    getActionsByEntity,
    hasPendingChanges,
    getConflicts
  };
}
