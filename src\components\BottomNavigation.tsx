import React, { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { Home, DollarSign, TrendingUp, PieChart, Plus } from 'lucide-react';
import { useTheme } from '../lib/ThemeContext';
import { AddTransactionButton } from './AddTransactionButton';

export function BottomNavigation() {
  const { theme } = useTheme();
  const location = useLocation();
  const [showAddTransaction, setShowAddTransaction] = useState(false);

  const navItems = [
    {
      path: '/',
      icon: Home,
      label: 'Inicio',
      color: 'text-blue-500'
    },
    {
      path: '/transactions',
      icon: DollarSign,
      label: 'Transacciones',
      color: 'text-green-500'
    },
    {
      path: '/budgets',
      icon: TrendingUp,
      label: 'Presupuestos',
      color: 'text-purple-500'
    },
    {
      path: '/patrimonio',
      icon: <PERSON><PERSON><PERSON>,
      label: 'Patrimonio',
      color: 'text-orange-500'
    }
  ];

  const isActive = (path: string) => {
    if (path === '/') {
      return location.pathname === '/';
    }
    return location.pathname.startsWith(path);
  };

  return (
    <div className={`fixed bottom-0 left-0 right-0 z-50 md:hidden transition-colors duration-200 ${
      theme === 'dark'
        ? 'bg-[#21222d]/95 border-t border-[#2a2a3d]'
        : 'bg-white/95 border-t border-slate-200'
    } backdrop-blur-sm`}>
      <div className="flex items-center justify-around py-2 px-4">
        {navItems.map((item) => {
          const Icon = item.icon;
          const active = isActive(item.path);
          
          return (
            <Link
              key={item.path}
              to={item.path}
              className={`flex flex-col items-center justify-center py-2 px-3 rounded-lg transition-all duration-200 ${
                active
                  ? theme === 'dark'
                    ? 'bg-[#2a2b38] text-white'
                    : 'bg-slate-100 text-slate-900'
                  : theme === 'dark'
                    ? 'text-gray-400 hover:text-gray-200'
                    : 'text-slate-500 hover:text-slate-700'
              }`}
            >
              <Icon 
                className={`w-5 h-5 mb-1 transition-colors duration-200 ${
                  active ? item.color : ''
                }`} 
              />
              <span className="text-xs font-medium">{item.label}</span>
            </Link>
          );
        })}
        
        {/* Add Transaction FAB */}
        <div className="relative">
          <button
            onClick={() => setShowAddTransaction(true)}
            className={`w-12 h-12 rounded-full flex items-center justify-center shadow-lg transition-all duration-200 ${
              theme === 'dark'
                ? 'bg-[#f9769d] hover:bg-[#f85a8a] text-white'
                : 'bg-[#f9769d] hover:bg-[#f85a8a] text-white'
            } hover:scale-110 active:scale-95`}
            title="Agregar transacción"
          >
            <Plus className="w-6 h-6" />
          </button>
        </div>
      </div>

      {/* Safe area for devices with home indicator */}
      <div className="h-safe-area-inset-bottom" />

      {/* Add Transaction Modal */}
      {showAddTransaction && (
        <div className="fixed inset-0 z-50">
          <AddTransactionButton
            isNewMonth={false}
            onClose={() => setShowAddTransaction(false)}
            isMobileModal={true}
          />
        </div>
      )}
    </div>
  );
}
