import React, { useState, useMemo } from 'react';
import { Plus, Edit, Trash2, AlertTriangle, CheckCircle, Target, TrendingUp, TrendingDown } from 'lucide-react';
import { useTheme } from '../lib/ThemeContext';
import { useTransactionContext } from '../lib/TransactionContext';
import { useCustomCategories } from '../hooks/useCustomCategories';
import { useToast } from './Toast';
import { getCurrentMonth } from '../lib/calculations';

interface Budget {
  id: string;
  category: string;
  limit: number;
  month: string;
  type: 'income' | 'expense';
  alertThreshold: number; // Percentage (e.g., 80 for 80%)
}

interface BudgetProgress {
  budget: Budget;
  spent: number;
  remaining: number;
  percentage: number;
  status: 'safe' | 'warning' | 'exceeded';
}

export function BudgetPage() {
  const { theme } = useTheme();
  const { transactions } = useTransactionContext();
  const { getCategoriesForType, getCategoryInfo } = useCustomCategories();
  const { showSuccess, showError } = useToast();
  
  const [budgets, setBudgets] = useState<Budget[]>(() => {
    const saved = localStorage.getItem('budgets');
    return saved ? JSON.parse(saved) : [];
  });
  
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingBudget, setEditingBudget] = useState<Budget | null>(null);
  const [selectedMonth, setSelectedMonth] = useState(getCurrentMonth());
  
  const [formData, setFormData] = useState({
    category: '',
    limit: '',
    type: 'expense' as 'income' | 'expense',
    alertThreshold: '80'
  });

  // Save budgets to localStorage
  const saveBudgets = (newBudgets: Budget[]) => {
    localStorage.setItem('budgets', JSON.stringify(newBudgets));
    setBudgets(newBudgets);
  };

  // Calculate budget progress
  const budgetProgress = useMemo((): BudgetProgress[] => {
    const currentMonthBudgets = budgets.filter(b => b.month === selectedMonth);
    
    return currentMonthBudgets.map(budget => {
      const categoryTransactions = transactions.filter(t => 
        t.category === budget.category && 
        t.type === budget.type &&
        t.date.startsWith(new Date().getFullYear().toString()) // Current year
      );
      
      const spent = categoryTransactions.reduce((sum, t) => sum + t.amount, 0);
      const remaining = budget.limit - spent;
      const percentage = budget.limit > 0 ? (spent / budget.limit) * 100 : 0;
      
      let status: 'safe' | 'warning' | 'exceeded' = 'safe';
      if (percentage >= 100) {
        status = 'exceeded';
      } else if (percentage >= budget.alertThreshold) {
        status = 'warning';
      }
      
      return {
        budget,
        spent,
        remaining,
        percentage,
        status
      };
    });
  }, [budgets, transactions, selectedMonth]);

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.category || !formData.limit) {
      showError('Error', 'Todos los campos son requeridos');
      return;
    }

    const limit = parseFloat(formData.limit);
    if (limit <= 0) {
      showError('Error', 'El límite debe ser mayor a 0');
      return;
    }

    const alertThreshold = parseFloat(formData.alertThreshold);
    if (alertThreshold < 0 || alertThreshold > 100) {
      showError('Error', 'El umbral de alerta debe estar entre 0 y 100');
      return;
    }

    if (editingBudget) {
      // Update existing budget
      const updatedBudgets = budgets.map(b =>
        b.id === editingBudget.id
          ? { ...b, category: formData.category, limit, alertThreshold }
          : b
      );
      saveBudgets(updatedBudgets);
      showSuccess('Presupuesto actualizado', 'El presupuesto se actualizó correctamente');
      setEditingBudget(null);
    } else {
      // Check if budget already exists for this category and month
      const exists = budgets.some(b => 
        b.category === formData.category && 
        b.month === selectedMonth && 
        b.type === formData.type
      );
      
      if (exists) {
        showError('Error', 'Ya existe un presupuesto para esta categoría en este mes');
        return;
      }

      // Add new budget
      const newBudget: Budget = {
        id: Date.now().toString(),
        category: formData.category,
        limit,
        month: selectedMonth,
        type: formData.type,
        alertThreshold
      };
      
      saveBudgets([...budgets, newBudget]);
      showSuccess('Presupuesto creado', 'El nuevo presupuesto se creó correctamente');
    }

    // Reset form
    setFormData({
      category: '',
      limit: '',
      type: 'expense',
      alertThreshold: '80'
    });
    setShowAddForm(false);
  };

  const handleEdit = (budget: Budget) => {
    setEditingBudget(budget);
    setFormData({
      category: budget.category,
      limit: budget.limit.toString(),
      type: budget.type,
      alertThreshold: budget.alertThreshold.toString()
    });
    setShowAddForm(true);
  };

  const handleDelete = (budgetId: string) => {
    if (window.confirm('¿Estás seguro de que quieres eliminar este presupuesto?')) {
      const updatedBudgets = budgets.filter(b => b.id !== budgetId);
      saveBudgets(updatedBudgets);
      showSuccess('Presupuesto eliminado', 'El presupuesto se eliminó correctamente');
    }
  };

  const handleCancel = () => {
    setShowAddForm(false);
    setEditingBudget(null);
    setFormData({
      category: '',
      limit: '',
      type: 'expense',
      alertThreshold: '80'
    });
  };

  // Calculate overall statistics
  const overallStats = useMemo(() => {
    const totalBudget = budgetProgress.reduce((sum, bp) => sum + bp.budget.limit, 0);
    const totalSpent = budgetProgress.reduce((sum, bp) => sum + bp.spent, 0);
    const exceededCount = budgetProgress.filter(bp => bp.status === 'exceeded').length;
    const warningCount = budgetProgress.filter(bp => bp.status === 'warning').length;
    
    return {
      totalBudget,
      totalSpent,
      totalRemaining: totalBudget - totalSpent,
      exceededCount,
      warningCount,
      safeCount: budgetProgress.length - exceededCount - warningCount
    };
  }, [budgetProgress]);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className={`text-3xl font-bold transition-colors duration-200 ${
            theme === 'dark' ? 'text-white' : 'text-slate-900'
          }`}>
            Presupuestos
          </h1>
          <p className={`text-sm mt-1 transition-colors duration-200 ${
            theme === 'dark' ? 'text-gray-400' : 'text-slate-500'
          }`}>
            Gestiona tus límites de gasto por categoría
          </p>
        </div>
        
        <div className="flex gap-3">
          <select
            value={selectedMonth}
            onChange={(e) => setSelectedMonth(e.target.value)}
            className={`px-3 py-2 rounded-lg border transition-colors duration-200 ${
              theme === 'dark'
                ? 'bg-[#2a2b38] border-gray-600 text-white'
                : 'bg-white border-slate-300 text-slate-900'
            }`}
          >
            <option value="Enero">Enero</option>
            <option value="Febrero">Febrero</option>
            <option value="Marzo">Marzo</option>
            <option value="Abril">Abril</option>
            <option value="Mayo">Mayo</option>
            <option value="Junio">Junio</option>
            <option value="Julio">Julio</option>
            <option value="Agosto">Agosto</option>
            <option value="Septiembre">Septiembre</option>
            <option value="Octubre">Octubre</option>
            <option value="Noviembre">Noviembre</option>
            <option value="Diciembre">Diciembre</option>
          </select>
          
          <button
            onClick={() => setShowAddForm(true)}
            className="px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg transition-colors duration-200 flex items-center gap-2"
          >
            <Plus className="w-4 h-4" />
            Nuevo Presupuesto
          </button>
        </div>
      </div>

      {/* Overall Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className={`rounded-xl p-4 transition-colors duration-200 ${
          theme === 'dark' 
            ? 'bg-[#21222d] border border-[#2a2a3d]' 
            : 'bg-slate-50 border border-slate-200'
        }`}>
          <div className="flex items-center">
            <div className="p-2 bg-blue-500 rounded-lg">
              <Target className="w-5 h-5 text-white" />
            </div>
            <div className="ml-3">
              <p className={`text-sm transition-colors duration-200 ${
                theme === 'dark' ? 'text-gray-400' : 'text-slate-500'
              }`}>
                Presupuesto Total
              </p>
              <p className={`text-lg font-bold text-blue-500`}>
                ${overallStats.totalBudget.toLocaleString()}
              </p>
            </div>
          </div>
        </div>

        <div className={`rounded-xl p-4 transition-colors duration-200 ${
          theme === 'dark' 
            ? 'bg-[#21222d] border border-[#2a2a3d]' 
            : 'bg-slate-50 border border-slate-200'
        }`}>
          <div className="flex items-center">
            <div className="p-2 bg-red-500 rounded-lg">
              <TrendingDown className="w-5 h-5 text-white" />
            </div>
            <div className="ml-3">
              <p className={`text-sm transition-colors duration-200 ${
                theme === 'dark' ? 'text-gray-400' : 'text-slate-500'
              }`}>
                Total Gastado
              </p>
              <p className={`text-lg font-bold text-red-500`}>
                ${overallStats.totalSpent.toLocaleString()}
              </p>
            </div>
          </div>
        </div>

        <div className={`rounded-xl p-4 transition-colors duration-200 ${
          theme === 'dark' 
            ? 'bg-[#21222d] border border-[#2a2a3d]' 
            : 'bg-slate-50 border border-slate-200'
        }`}>
          <div className="flex items-center">
            <div className={`p-2 rounded-lg ${overallStats.totalRemaining >= 0 ? 'bg-green-500' : 'bg-orange-500'}`}>
              <TrendingUp className="w-5 h-5 text-white" />
            </div>
            <div className="ml-3">
              <p className={`text-sm transition-colors duration-200 ${
                theme === 'dark' ? 'text-gray-400' : 'text-slate-500'
              }`}>
                Disponible
              </p>
              <p className={`text-lg font-bold ${
                overallStats.totalRemaining >= 0 ? 'text-green-500' : 'text-orange-500'
              }`}>
                ${overallStats.totalRemaining.toLocaleString()}
              </p>
            </div>
          </div>
        </div>

        <div className={`rounded-xl p-4 transition-colors duration-200 ${
          theme === 'dark' 
            ? 'bg-[#21222d] border border-[#2a2a3d]' 
            : 'bg-slate-50 border border-slate-200'
        }`}>
          <div className="flex items-center">
            <div className={`p-2 rounded-lg ${
              overallStats.exceededCount > 0 ? 'bg-red-500' : 
              overallStats.warningCount > 0 ? 'bg-yellow-500' : 'bg-green-500'
            }`}>
              {overallStats.exceededCount > 0 ? (
                <AlertTriangle className="w-5 h-5 text-white" />
              ) : (
                <CheckCircle className="w-5 h-5 text-white" />
              )}
            </div>
            <div className="ml-3">
              <p className={`text-sm transition-colors duration-200 ${
                theme === 'dark' ? 'text-gray-400' : 'text-slate-500'
              }`}>
                Estado
              </p>
              <p className={`text-lg font-bold ${
                overallStats.exceededCount > 0 ? 'text-red-500' : 
                overallStats.warningCount > 0 ? 'text-yellow-500' : 'text-green-500'
              }`}>
                {overallStats.exceededCount > 0 ? `${overallStats.exceededCount} Excedidos` :
                 overallStats.warningCount > 0 ? `${overallStats.warningCount} En Alerta` : 'Todo Bien'}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Add/Edit Budget Form */}
      {showAddForm && (
        <div className={`rounded-xl p-6 transition-colors duration-200 ${
          theme === 'dark'
            ? 'bg-[#21222d] border border-[#2a2a3d]'
            : 'bg-slate-50 border border-slate-200'
        }`}>
          <h3 className={`text-lg font-semibold mb-4 transition-colors duration-200 ${
            theme === 'dark' ? 'text-white' : 'text-slate-900'
          }`}>
            {editingBudget ? 'Editar Presupuesto' : 'Nuevo Presupuesto'}
          </h3>

          <form onSubmit={handleSubmit} className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {/* Type */}
            <div>
              <label className={`block text-sm font-medium mb-2 transition-colors duration-200 ${
                theme === 'dark' ? 'text-gray-300' : 'text-slate-700'
              }`}>
                Tipo
              </label>
              <select
                value={formData.type}
                onChange={(e) => setFormData(prev => ({ ...prev, type: e.target.value as 'income' | 'expense', category: '' }))}
                className={`w-full px-3 py-2 rounded-lg border transition-colors duration-200 ${
                  theme === 'dark'
                    ? 'bg-[#2a2b38] border-gray-600 text-white'
                    : 'bg-white border-slate-300 text-slate-900'
                }`}
                disabled={!!editingBudget}
              >
                <option value="expense">Gasto</option>
                <option value="income">Ingreso</option>
              </select>
            </div>

            {/* Category */}
            <div>
              <label className={`block text-sm font-medium mb-2 transition-colors duration-200 ${
                theme === 'dark' ? 'text-gray-300' : 'text-slate-700'
              }`}>
                Categoría
              </label>
              <select
                value={formData.category}
                onChange={(e) => setFormData(prev => ({ ...prev, category: e.target.value }))}
                className={`w-full px-3 py-2 rounded-lg border transition-colors duration-200 ${
                  theme === 'dark'
                    ? 'bg-[#2a2b38] border-gray-600 text-white'
                    : 'bg-white border-slate-300 text-slate-900'
                }`}
                required
                disabled={!!editingBudget}
              >
                <option value="">Seleccionar categoría</option>
                {getCategoriesForType(formData.type).map(cat => (
                  <option key={cat.key} value={cat.key}>
                    {cat.icon} {cat.name}
                  </option>
                ))}
              </select>
            </div>

            {/* Limit */}
            <div>
              <label className={`block text-sm font-medium mb-2 transition-colors duration-200 ${
                theme === 'dark' ? 'text-gray-300' : 'text-slate-700'
              }`}>
                Límite ($)
              </label>
              <input
                type="number"
                step="0.01"
                value={formData.limit}
                onChange={(e) => setFormData(prev => ({ ...prev, limit: e.target.value }))}
                className={`w-full px-3 py-2 rounded-lg border transition-colors duration-200 ${
                  theme === 'dark'
                    ? 'bg-[#2a2b38] border-gray-600 text-white placeholder-gray-400'
                    : 'bg-white border-slate-300 text-slate-900 placeholder-slate-400'
                }`}
                placeholder="0.00"
                required
              />
            </div>

            {/* Alert Threshold */}
            <div>
              <label className={`block text-sm font-medium mb-2 transition-colors duration-200 ${
                theme === 'dark' ? 'text-gray-300' : 'text-slate-700'
              }`}>
                Alerta (%)
              </label>
              <input
                type="number"
                min="0"
                max="100"
                value={formData.alertThreshold}
                onChange={(e) => setFormData(prev => ({ ...prev, alertThreshold: e.target.value }))}
                className={`w-full px-3 py-2 rounded-lg border transition-colors duration-200 ${
                  theme === 'dark'
                    ? 'bg-[#2a2b38] border-gray-600 text-white placeholder-gray-400'
                    : 'bg-white border-slate-300 text-slate-900 placeholder-slate-400'
                }`}
                placeholder="80"
                required
              />
            </div>

            {/* Form Actions */}
            <div className="md:col-span-4 flex gap-3 pt-4">
              <button
                type="button"
                onClick={handleCancel}
                className={`flex-1 py-2 px-4 rounded-lg border transition-colors duration-200 ${
                  theme === 'dark'
                    ? 'border-gray-600 text-gray-300 hover:bg-gray-700'
                    : 'border-slate-300 text-slate-700 hover:bg-slate-50'
                }`}
              >
                Cancelar
              </button>
              <button
                type="submit"
                className="flex-1 py-2 px-4 rounded-lg bg-blue-500 hover:bg-blue-600 text-white transition-colors duration-200"
              >
                {editingBudget ? 'Actualizar' : 'Crear'} Presupuesto
              </button>
            </div>
          </form>
        </div>
      )}

      {/* Budget Progress List */}
      <div className={`rounded-xl transition-colors duration-200 ${
        theme === 'dark'
          ? 'bg-[#21222d] border border-[#2a2a3d]'
          : 'bg-slate-50 border border-slate-200'
      }`}>
        <div className="p-4 border-b border-gray-600">
          <h2 className={`text-lg font-semibold transition-colors duration-200 ${
            theme === 'dark' ? 'text-white' : 'text-slate-900'
          }`}>
            Presupuestos de {selectedMonth} ({budgetProgress.length})
          </h2>
        </div>

        {budgetProgress.length === 0 ? (
          <div className={`text-center py-12 transition-colors duration-200 ${
            theme === 'dark' ? 'text-gray-500' : 'text-slate-500'
          }`}>
            <Target className="w-12 h-12 mx-auto mb-4 opacity-50" />
            <p className="font-medium">No hay presupuestos para este mes</p>
            <p className="text-sm mt-1">Crea tu primer presupuesto para comenzar a controlar tus gastos</p>
          </div>
        ) : (
          <div className="divide-y divide-gray-600">
            {budgetProgress.map((progress) => {
              const categoryInfo = getCategoryInfo(progress.budget.category);

              return (
                <div key={progress.budget.id} className={`p-4 transition-colors duration-200 ${
                  theme === 'dark' ? 'hover:bg-[#2a2b38]' : 'hover:bg-slate-100'
                }`}>
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center gap-3">
                      <div className={`w-10 h-10 rounded-lg flex items-center justify-center text-white font-medium ${
                        categoryInfo?.color ? '' : 'bg-gray-500'
                      }`} style={categoryInfo?.color ? { backgroundColor: categoryInfo.color } : {}}>
                        {categoryInfo?.icon || '📊'}
                      </div>
                      <div>
                        <h3 className={`font-medium transition-colors duration-200 ${
                          theme === 'dark' ? 'text-white' : 'text-slate-900'
                        }`}>
                          {categoryInfo?.name || progress.budget.category}
                        </h3>
                        <p className={`text-sm transition-colors duration-200 ${
                          theme === 'dark' ? 'text-gray-400' : 'text-slate-500'
                        }`}>
                          Límite: ${progress.budget.limit.toLocaleString()}
                        </p>
                      </div>
                    </div>

                    <div className="flex items-center gap-4">
                      <div className="text-right">
                        <p className={`text-lg font-bold ${
                          progress.status === 'exceeded' ? 'text-red-500' :
                          progress.status === 'warning' ? 'text-yellow-500' : 'text-green-500'
                        }`}>
                          ${progress.spent.toLocaleString()}
                        </p>
                        <p className={`text-sm transition-colors duration-200 ${
                          theme === 'dark' ? 'text-gray-400' : 'text-slate-500'
                        }`}>
                          {progress.percentage.toFixed(1)}% usado
                        </p>
                      </div>

                      <div className="flex items-center gap-2">
                        <button
                          onClick={() => handleEdit(progress.budget)}
                          className={`p-1 rounded transition-colors duration-200 ${
                            theme === 'dark'
                              ? 'text-gray-400 hover:text-blue-400 hover:bg-blue-900/20'
                              : 'text-slate-400 hover:text-blue-600 hover:bg-blue-50'
                          }`}
                          title="Editar presupuesto"
                        >
                          <Edit className="w-4 h-4" />
                        </button>

                        <button
                          onClick={() => handleDelete(progress.budget.id)}
                          className={`p-1 rounded transition-colors duration-200 ${
                            theme === 'dark'
                              ? 'text-gray-400 hover:text-red-400 hover:bg-red-900/20'
                              : 'text-slate-400 hover:text-red-600 hover:bg-red-50'
                          }`}
                          title="Eliminar presupuesto"
                        >
                          <Trash2 className="w-4 h-4" />
                        </button>
                      </div>
                    </div>
                  </div>

                  {/* Progress Bar */}
                  <div className="w-full bg-gray-700 rounded-full h-2">
                    <div
                      className={`h-2 rounded-full transition-all duration-300 ${
                        progress.status === 'exceeded' ? 'bg-red-500' :
                        progress.status === 'warning' ? 'bg-yellow-500' : 'bg-green-500'
                      }`}
                      style={{ width: `${Math.min(progress.percentage, 100)}%` }}
                    />
                  </div>

                  <div className="flex justify-between items-center mt-2">
                    <span className={`text-sm transition-colors duration-200 ${
                      theme === 'dark' ? 'text-gray-400' : 'text-slate-500'
                    }`}>
                      Restante: ${progress.remaining.toLocaleString()}
                    </span>

                    {progress.status === 'exceeded' && (
                      <span className="text-sm text-red-500 flex items-center gap-1">
                        <AlertTriangle className="w-3 h-3" />
                        Presupuesto excedido
                      </span>
                    )}

                    {progress.status === 'warning' && (
                      <span className="text-sm text-yellow-500 flex items-center gap-1">
                        <AlertTriangle className="w-3 h-3" />
                        Cerca del límite
                      </span>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </div>
    </div>
  );
}
