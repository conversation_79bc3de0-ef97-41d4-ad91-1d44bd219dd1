import { useState } from 'react';
import { Card } from './ui/Card';
import { VisionGeneralPanel } from './VisionGeneralPanel';
import { EstimadoRealChart } from './EstimadoRealChart';
import { DetalleFinanciero } from './DetalleFinanciero';
import { AddTransactionButton } from './AddTransactionButton';
import { Mes } from '../lib/types';
import { getCurrentMonth } from '../lib/calculations';
import { Search, BarChart3, LineChart, ListChecks } from 'lucide-react';
import { Info } from 'lucide-react';
import { MultiuserBanner } from './MultiuserBanner';
import { DashboardStats } from './DashboardStats';
import { TransactionHistory } from './TransactionHistory';
import { TooltipRoot, TooltipTrigger, TooltipContent } from './ui/Tooltip';
import { useTheme } from '../lib/ThemeContext';

export function Dashboard() {
  const [selectedMonth, setSelectedMonth] = useState<Mes>(getCurrentMonth());
  const isNewMonth = selectedMonth === 'Enero'; // Example condition for new month
  const { theme } = useTheme();

  return (
    <div className="pb-20">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6">
        <div className="flex items-center gap-2">
          <h1 className={`text-3xl font-bold transition-colors duration-200 ${
            theme === 'dark' ? 'text-white' : 'text-slate-900'
          }`}>Dashboard</h1>
          <TooltipRoot delayDuration={0}>
            <TooltipTrigger asChild>
              <Info className={`w-5 h-5 cursor-help transition-colors duration-200 ${
                theme === 'dark'
                  ? 'text-gray-400 hover:text-white'
                  : 'text-gray-500 hover:text-gray-700'
              }`} />
            </TooltipTrigger>
            <TooltipContent className={`max-w-sm p-3 rounded-lg shadow-lg ${
              theme === 'dark'
                ? 'bg-gray-800 text-white'
                : 'bg-white text-slate-900 border border-slate-200'
            }`}>
              <div>
                <p className="font-medium mb-1">Panel Principal</p>
                <p className="text-sm">Aquí puedes ver un resumen de tus finanzas: ingresos, gastos, y cómo va tu presupuesto. Usa el botón azul de la esquina inferior derecha para agregar nuevas transacciones.</p>
              </div>
            </TooltipContent>
          </TooltipRoot>
        </div>
        <div className={`rounded-full px-4 py-2 flex items-center w-full md:w-auto max-w-xs transition-colors duration-200 ${
          theme === 'dark'
            ? 'bg-[#21222d]'
            : 'bg-slate-100 border border-slate-200'
        }`}>
          <Search className="w-4 h-4 text-gray-500 mr-2" />
          <input
            type="text"
            placeholder="Search"
            className={`bg-transparent border-none outline-none w-full transition-colors duration-200 ${
              theme === 'dark'
                ? 'text-white placeholder-gray-500'
                : 'text-slate-900 placeholder-slate-400'
            }`}
          />
        </div>
      </div>
      
      {/* Main layout: Content left, Transactions right */}
      <div className="grid grid-cols-1 xl:grid-cols-4 gap-8">
        {/* Main content area */}
        <div className="xl:col-span-3 space-y-8">
          <DashboardStats 
            selectedMonth={selectedMonth}
            onMonthChange={setSelectedMonth}
          />
          
          <MultiuserBanner />

          <VisionGeneralPanel mes={selectedMonth} />
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <Card>
              <div className="flex items-center justify-between mb-2 px-6 pt-6">
                <div className="flex items-center">
                <BarChart3 className="w-5 h-5 text-[#f9769d] mr-2" />
                <h2 className="text-xl font-semibold">Estimado vs Real</h2>
                </div>
                <TooltipRoot delayDuration={0}>
                  <TooltipTrigger asChild>
                    <Info className="w-4 h-4 text-gray-400 hover:text-white cursor-help" />
                  </TooltipTrigger>
                  <TooltipContent className="max-w-sm p-3 bg-gray-800 text-white rounded-lg shadow-lg">
                    <p className="text-sm">Este gráfico compara lo que habías planeado gastar (Estimado) vs lo que realmente gastas (Real) en cada categoría. Te ayuda a ver si estás cumpliendo tu presupuesto.</p>
                  </TooltipContent>
                </TooltipRoot>
              </div>
              <EstimadoRealChart mes={selectedMonth} />
            </Card>
            
            <Card>
              <div className="flex items-center justify-between mb-2 px-6 pt-6">
                <div className="flex items-center">
                <LineChart className="w-5 h-5 text-[#96a7ff] mr-2" />
                <h2 className="text-xl font-semibold">Detalle Financiero</h2>
                </div>
                <TooltipRoot delayDuration={0}>
                  <TooltipTrigger asChild>
                    <Info className="w-4 h-4 text-gray-400 hover:text-white cursor-help" />
                  </TooltipTrigger>
                  <TooltipContent className="max-w-sm p-3 bg-gray-800 text-white rounded-lg shadow-lg">
                    <p className="text-sm">Aquí puedes ver el desglose detallado de cada categoría de tu presupuesto: ingresos, gastos esenciales, discrecionales, deudas, ahorros e inversiones.</p>
                  </TooltipContent>
                </TooltipRoot>
              </div>
              <DetalleFinanciero mes={selectedMonth} />
            </Card>
          </div>
        </div>
        
        {/* Right sidebar for transactions */}
        <div className="xl:col-span-1">
          <div className="sticky top-24">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center">
                <ListChecks className="w-6 w-6 text-[#f9769d] mr-2" />
                <h2 className="text-xl font-bold">Movimientos</h2>
              </div>
              <TooltipRoot delayDuration={0}>
                <TooltipTrigger asChild>
                  <Info className="w-5 h-5 text-gray-400 hover:text-white cursor-help" />
                </TooltipTrigger>
                <TooltipContent className="max-w-sm p-3 bg-gray-800 text-white rounded-lg shadow-lg">
                  <p className="text-sm">Panel de control de tus transacciones en tiempo real. Mantén el control visual de todos tus ingresos y gastos.</p>
                </TooltipContent>
              </TooltipRoot>
            </div>
            <TransactionHistory limit={8} />
          </div>
        </div>
      </div>
      
      <AddTransactionButton isNewMonth={isNewMonth} />
    </div>
  );
}