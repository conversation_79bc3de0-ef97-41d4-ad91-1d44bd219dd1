import { useState, useEffect } from 'react';
import { Card } from './ui/Card';
import { 
  CreditCard, ChevronDown, ChevronUp, DollarSign, Calendar, Percent, 
  AlertCircle, Award, TrendingUp, Umbrella, Landmark, LineChart,
  Zap, HelpCircle, Heart, RefreshCw, ArrowRight, Info
} from 'lucide-react';
import { useTheme } from '../lib/ThemeContext';
import { Link } from 'react-router-dom';
import { InfoTooltip } from './ui/Tooltip';
import { 
  BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, 
  ResponsiveContainer, LineChart as RechartsLineChart, Line
} from 'recharts';

export function CalculadoraFIREPage() {
  const { theme } = useTheme();
  
  // Expandir/Colapsar secciones
  const [seccionesExpandidas, setSeccionesExpandidas] = useState({
    parametros: true,
    avance: true,
    simulacion: true,
    vidaRica: true
  });
  
  // SECCIÓN 1 - PARÁMETROS DE RETIRO
  const [gastoMensualRetiro, setGastoMensualRetiro] = useState(2000);
  const [anosRetiro, setAnosRetiro] = useState(30);
  const [usarRegla4Porciento, setUsarRegla4Porciento] = useState(true);
  const [inflacionAnual, setInflacionAnual] = useState(3);
  const [tasaRetornoRetiro, setTasaRetornoRetiro] = useState(5);
  
  // SECCIÓN 2 - AVANCE HACIA LA META
  const [ahorroActual, setAhorroActual] = useState(110000);
  const [aporteMensual, setAporteMensual] = useState(600);
  const [tasaRetornoAnual, setTasaRetornoAnual] = useState(7);
  
  // SECCIÓN 3 - SIMULACIÓN DINÁMICA
  const [escenarios, setEscenarios] = useState([
    { aporteMensual: 300, años: 0, porcentajeCubierto: 0, recomendacion: '' },
    { aporteMensual: 600, años: 0, porcentajeCubierto: 0, recomendacion: '' },
    { aporteMensual: 1000, años: 0, porcentajeCubierto: 0, recomendacion: '' },
    { aporteMensual: 1500, años: 0, porcentajeCubierto: 0, recomendacion: '' }
  ]);
  
  // Datos calculados
  const [capitalNecesario, setCapitalNecesario] = useState(0);
  const [porcentajeAlcanzado, setPorcentajeAlcanzado] = useState(0);
  const [anosParaRetiro, setAnosParaRetiro] = useState(0);
  const [edadEstimadaRetiro, setEdadEstimadaRetiro] = useState(0);
  
  // Datos adicionales (pueden venir de otros componentes en una app real)
  const [edadActual, setEdadActual] = useState(35);
  const [esParteDeLaVisionRica, setEsParteDeLaVisionRica] = useState(true);
  
  // Datos para gráfico
  const [datosProyeccion, setDatosProyeccion] = useState([]);
  
  // Función para alternar el estado de expansión de una sección
  const toggleSeccion = (seccion) => {
    setSeccionesExpandidas({
      ...seccionesExpandidas,
      [seccion]: !seccionesExpandidas[seccion]
    });
  };
  
  // Calcular capital necesario según la estrategia elegida
  useEffect(() => {
    let capital = 0;
    
    if (usarRegla4Porciento) {
      // Regla del 4%: 25 veces el gasto anual
      capital = gastoMensualRetiro * 12 * 25;
    } else {
      // Cálculo más tradicional considerando inflación y retorno
      const tasaRetornoReal = (1 + tasaRetornoRetiro / 100) / (1 + inflacionAnual / 100) - 1;
      const gastoAnual = gastoMensualRetiro * 12;
      
      // Usar fórmula de valor presente de una anualidad
      if (Math.abs(tasaRetornoReal) > 0.0001) { // Evitar división por cero
        capital = gastoAnual * (1 - Math.pow(1 + tasaRetornoReal, -anosRetiro)) / tasaRetornoReal;
      } else {
        capital = gastoAnual * anosRetiro;
      }
    }
    
    setCapitalNecesario(capital);
  }, [gastoMensualRetiro, anosRetiro, usarRegla4Porciento, tasaRetornoRetiro, inflacionAnual]);
  
  // Calcular porcentaje alcanzado y años estimados
  useEffect(() => {
    if (capitalNecesario > 0) {
      // Porcentaje alcanzado
      const porcentaje = (ahorroActual / capitalNecesario) * 100;
      setPorcentajeAlcanzado(porcentaje);
      
      // Años estimados para alcanzar la meta
      let saldoActual = ahorroActual;
      let años = 0;
      const tasaMensual = tasaRetornoAnual / 100 / 12;
      
      if (aporteMensual > 0 || tasaRetornoAnual > 0) {
        while (saldoActual < capitalNecesario && años < 100) {
          saldoActual = saldoActual * (1 + tasaMensual) + aporteMensual;
          años += 1/12; // Incremento mensual
        }
        
        setAnosParaRetiro(años);
        setEdadEstimadaRetiro(edadActual + años);
      } else {
        setAnosParaRetiro(999); // Valor alto para indicar "nunca"
        setEdadEstimadaRetiro(999);
      }
    }
  }, [capitalNecesario, ahorroActual, aporteMensual, tasaRetornoAnual, edadActual]);
  
  // Calcular escenarios
  useEffect(() => {
    if (capitalNecesario > 0) {
      const nuevosEscenarios = escenarios.map(escenario => {
        let saldoActual = ahorroActual;
        let años = 0;
        const tasaMensual = tasaRetornoAnual / 100 / 12;
        
        if (escenario.aporteMensual > 0 || tasaRetornoAnual > 0) {
          while (saldoActual < capitalNecesario && años < 100) {
            saldoActual = saldoActual * (1 + tasaMensual) + escenario.aporteMensual;
            años += 1/12; // Incremento mensual
          }
        } else {
          años = 999; // Valor alto para indicar "nunca"
        }
        
        // Determinar porcentaje cubierto (si no llega al 100% en 100 años)
        const porcentajeCubierto = Math.min(100, (años >= 100 ? ahorroActual / capitalNecesario * 100 : 100));
        
        // Determinar recomendación
        let recomendacion = '';
        if (años >= 100) {
          recomendacion = 'Sin aportes no hay libertad';
        } else if (años <= 10) {
          recomendacion = '¡Camino exprés a FIRE!';
        } else if (años <= 15) {
          recomendacion = '¡Excelente avance!';
        } else {
          recomendacion = 'Bien, mantén el ritmo';
        }
        
        return {
          ...escenario,
          años,
          porcentajeCubierto,
          recomendacion
        };
      });
      
      setEscenarios(nuevosEscenarios);
    }
  }, [capitalNecesario, ahorroActual, tasaRetornoAnual]);
  
  // Generar datos para el gráfico
  useEffect(() => {
    const años = Math.min(40, Math.ceil(anosParaRetiro) + 5);
    const datos = [];
    
    let saldoActual = ahorroActual;
    const tasaMensual = tasaRetornoAnual / 100 / 12;
    const aportesAcumulados = [ahorroActual];
    let aporteAcumulado = ahorroActual;
    
    for (let i = 0; i <= años * 12; i++) {
      if (i % 12 === 0) { // Solo graficar años completos
        const año = i / 12;
        datos.push({
          año,
          saldo: Math.round(saldoActual),
          meta: capitalNecesario,
          aportes: Math.round(aporteAcumulado)
        });
      }
      
      saldoActual = saldoActual * (1 + tasaMensual) + aporteMensual;
      aporteAcumulado += aporteMensual;
    }
    
    setDatosProyeccion(datos);
  }, [ahorroActual, aporteMensual, tasaRetornoAnual, capitalNecesario, anosParaRetiro]);
  
  // Formatear números con separadores de miles
  const formatearNumero = (numero) => {
    return new Intl.NumberFormat('es-ES').format(Math.round(numero));
  };
  
  return (
    <div className="space-y-8 pb-20">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6">
        <div className="flex items-center gap-2">
          <h1 className="text-3xl font-bold text-white">Calculadora FIRE</h1>
          <InfoTooltip
            title="Independencia Financiera"
            description="FIRE = Financial Independence, Retire Early. Calcula cuánto necesitas ahorrar para poder vivir de tus inversiones y retirarte temprano."
          >
            <Info className="w-5 h-5 text-gray-400 hover:text-white cursor-help" />
          </InfoTooltip>
        </div>
        <div className="flex flex-wrap gap-2">
          <div className="bg-[#2a2b38] rounded-lg p-2 text-sm text-gray-300 flex items-center">
            <span className="font-medium mr-1">FIRE:</span> Financial Independence, Retire Early
          </div>
          <InfoTooltip
            title="¿Qué es FIRE?"
            description="El movimiento FIRE busca alcanzar la independencia financiera y retirarse temprano, generalmente siguiendo la 'regla del 4%': puedes retirar el 4% de tu capital anualmente sin agotarlo."
          >
            <div className="bg-[#2a2b38] rounded-lg p-2 text-sm text-blue-400 flex items-center cursor-help">
              <Info className="w-4 h-4 mr-1" />
              <span className="font-medium">¿Qué es FIRE?</span>
            </div>
          </InfoTooltip>
        </div>
      </div>

      {/* SECCIÓN 1 - PARÁMETROS DE RETIRO */}
      <Card>
        <div className="p-6">
          <div className="flex justify-between items-center mb-4">
            <div className="flex items-center">
              <Umbrella className="h-6 w-6 text-[#f9769d] mr-2" />
              <h2 className="text-xl font-semibold">Parámetros de Retiro</h2>
            </div>
            <button 
              onClick={() => toggleSeccion('parametros')}
              className="text-gray-400 hover:text-white"
            >
              {seccionesExpandidas.parametros ? <ChevronUp /> : <ChevronDown />}
            </button>
          </div>
          
          {seccionesExpandidas.parametros && (
            <div className="animate-fadeIn">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-400 mb-2">
                      Gasto mensual deseado al retirarme
                    </label>
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <DollarSign className="h-5 w-5 text-gray-400" />
                      </div>
                      <input
                        type="number"
                        min="0"
                        value={gastoMensualRetiro}
                        onChange={(e) => setGastoMensualRetiro(Math.max(0, Number(e.target.value)))}
                        className="w-full bg-[#21222d] border border-gray-700 rounded-lg p-2 pl-10 focus:border-blue-500 focus:ring-blue-500"
                      />
                    </div>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-400 mb-2">
                      Años de retiro planificados
                    </label>
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <Calendar className="h-5 w-5 text-gray-400" />
                      </div>
                      <input
                        type="number"
                        min="1"
                        max="100"
                        value={anosRetiro}
                        onChange={(e) => setAnosRetiro(Math.max(1, Math.min(100, Number(e.target.value))))}
                        className="w-full bg-[#21222d] border border-gray-700 rounded-lg p-2 pl-10 focus:border-blue-500 focus:ring-blue-500"
                      />
                    </div>
                  </div>
                  
                  <div>
                    <div className="flex items-center mb-2">
                      <label className="flex items-center text-sm font-medium text-gray-400">
                        <input
                          type="checkbox"
                          checked={usarRegla4Porciento}
                          onChange={() => setUsarRegla4Porciento(!usarRegla4Porciento)}
                          className="mr-2 rounded bg-[#21222d] border-gray-700 text-blue-500 focus:ring-blue-500"
                        />
                        Usar regla del 4% (simplificada)
                      </label>
                      <InfoTooltip
                        title="Regla del 4%"
                        description="La regla del 4% establece que puedes retirar el 4% de tu capital el primer año, y ajustar por inflación en años subsiguientes, sin agotar tu capital en 30 años."
                      >
                        <HelpCircle className="h-4 w-4 text-gray-400 ml-1 cursor-help" />
                      </InfoTooltip>
                    </div>
                  </div>
                </div>
                
                {!usarRegla4Porciento && (
                  <div className="space-y-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-400 mb-2">
                        Inflación anual estimada (%)
                      </label>
                      <div className="relative">
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                          <Percent className="h-5 w-5 text-gray-400" />
                        </div>
                        <input
                          type="number"
                          min="0"
                          max="20"
                          step="0.1"
                          value={inflacionAnual}
                          onChange={(e) => setInflacionAnual(Math.max(0, Math.min(20, Number(e.target.value))))}
                          className="w-full bg-[#21222d] border border-gray-700 rounded-lg p-2 pl-10 focus:border-blue-500 focus:ring-blue-500"
                        />
                      </div>
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-400 mb-2">
                        Tasa de retorno durante retiro (%)
                      </label>
                      <div className="relative">
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                          <TrendingUp className="h-5 w-5 text-gray-400" />
                        </div>
                        <input
                          type="number"
                          min="0"
                          max="20"
                          step="0.1"
                          value={tasaRetornoRetiro}
                          onChange={(e) => setTasaRetornoRetiro(Math.max(0, Math.min(20, Number(e.target.value))))}
                          className="w-full bg-[#21222d] border border-gray-700 rounded-lg p-2 pl-10 focus:border-blue-500 focus:ring-blue-500"
                        />
                      </div>
                    </div>
                  </div>
                )}
                
                {usarRegla4Porciento && (
                  <div className="bg-[#2a2b38] rounded-xl p-5 flex flex-col justify-center">
                    <div className="text-center mb-4">
                      <div className="text-4xl font-bold text-blue-400">
                        ${formatearNumero(capitalNecesario)}
                      </div>
                      <div className="text-sm text-gray-400 mt-1">
                        Capital necesario para retirarte
                      </div>
                    </div>
                    
                    <div className="text-center">
                      <p className="text-gray-300">
                        Necesitarás <span className="font-bold text-white">${formatearNumero(capitalNecesario)}</span> para retirarte y vivir durante <span className="font-bold text-white">{anosRetiro} años</span> con <span className="font-bold text-white">${formatearNumero(gastoMensualRetiro)}</span> al mes.
                      </p>
                    </div>
                  </div>
                )}
                
                {!usarRegla4Porciento && (
                  <div className="col-span-full bg-[#2a2b38] rounded-xl p-5">
                    <div className="text-center mb-4">
                      <div className="text-4xl font-bold text-blue-400">
                        ${formatearNumero(capitalNecesario)}
                      </div>
                      <div className="text-sm text-gray-400 mt-1">
                        Capital necesario para retirarte
                      </div>
                    </div>
                    
                    <div className="text-center">
                      <p className="text-gray-300">
                        Considerando una inflación del <span className="font-bold text-white">{inflacionAnual}%</span> y un retorno del <span className="font-bold text-white">{tasaRetornoRetiro}%</span> durante el retiro, necesitarás <span className="font-bold text-white">${formatearNumero(capitalNecesario)}</span> para vivir <span className="font-bold text-white">{anosRetiro} años</span> con <span className="font-bold text-white">${formatearNumero(gastoMensualRetiro)}</span> mensuales.
                      </p>
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </Card>
      
      {/* SECCIÓN 2 - AVANCE HACIA LA META */}
      <Card>
        <div className="p-6">
          <div className="flex justify-between items-center mb-4">
            <div className="flex items-center">
              <LineChart className="h-6 w-6 text-[#96a7ff] mr-2" />
              <h2 className="text-xl font-semibold">Avance Hacia La Meta</h2>
            </div>
            <button 
              onClick={() => toggleSeccion('avance')}
              className="text-gray-400 hover:text-white"
            >
              {seccionesExpandidas.avance ? <ChevronUp /> : <ChevronDown />}
            </button>
          </div>
          
          {seccionesExpandidas.avance && (
            <div className="animate-fadeIn">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="md:col-span-1 space-y-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-400 mb-2">
                      Ahorro/inversión actual
                    </label>
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <Landmark className="h-5 w-5 text-gray-400" />
                      </div>
                      <input
                        type="number"
                        min="0"
                        value={ahorroActual}
                        onChange={(e) => setAhorroActual(Math.max(0, Number(e.target.value)))}
                        className="w-full bg-[#21222d] border border-gray-700 rounded-lg p-2 pl-10 focus:border-blue-500 focus:ring-blue-500"
                      />
                    </div>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-400 mb-2">
                      Aporte mensual estimado
                    </label>
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <DollarSign className="h-5 w-5 text-gray-400" />
                      </div>
                      <input
                        type="number"
                        min="0"
                        value={aporteMensual}
                        onChange={(e) => setAporteMensual(Math.max(0, Number(e.target.value)))}
                        className="w-full bg-[#21222d] border border-gray-700 rounded-lg p-2 pl-10 focus:border-blue-500 focus:ring-blue-500"
                      />
                    </div>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-400 mb-2">
                      Tasa de retorno anual estimada (%)
                    </label>
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <Percent className="h-5 w-5 text-gray-400" />
                      </div>
                      <input
                        type="number"
                        min="0"
                        max="20"
                        step="0.1"
                        value={tasaRetornoAnual}
                        onChange={(e) => setTasaRetornoAnual(Math.max(0, Math.min(20, Number(e.target.value))))}
                        className="w-full bg-[#21222d] border border-gray-700 rounded-lg p-2 pl-10 focus:border-blue-500 focus:ring-blue-500"
                      />
                    </div>
                  </div>
                </div>
                
                <div className="md:col-span-2">
                  <div className="bg-[#2a2b38] rounded-xl p-5 h-full flex flex-col justify-between">
                    <div className="flex flex-col md:flex-row justify-between mb-6">
                      <div className="text-center mb-4 md:mb-0">
                        <div className="text-sm text-gray-400 mb-1">Meta FIRE</div>
                        <div className="text-2xl font-bold">${formatearNumero(capitalNecesario)}</div>
                      </div>
                      
                      <div className="text-center mb-4 md:mb-0">
                        <div className="text-sm text-gray-400 mb-1">Ahorrado</div>
                        <div className="text-2xl font-bold">${formatearNumero(ahorroActual)}</div>
                      </div>
                      
                      <div className="text-center">
                        <div className="text-sm text-gray-400 mb-1">Falta</div>
                        <div className="text-2xl font-bold">${formatearNumero(Math.max(0, capitalNecesario - ahorroActual))}</div>
                      </div>
                    </div>
                    
                    {/* Barra de progreso visual */}
                    <div>
                      <div className="relative pt-1">
                        <div className="flex mb-2 items-center justify-between">
                          <div>
                            <span className="text-xs font-semibold inline-block py-1 px-2 uppercase rounded-full bg-blue-900 text-blue-300">
                              Progreso
                            </span>
                          </div>
                          <div className="text-right">
                            <span className="text-xs font-semibold inline-block text-blue-300">
                              {porcentajeAlcanzado.toFixed(1)}%
                            </span>
                          </div>
                        </div>
                        <div className="overflow-hidden h-4 mb-4 text-xs flex rounded-full bg-gray-700">
                          <div style={{ width: `${Math.min(100, porcentajeAlcanzado)}%` }} className="shadow-none flex flex-col text-center whitespace-nowrap text-white justify-center bg-gradient-to-r from-blue-400 to-[#f9769d]"></div>
                        </div>
                      </div>
                    </div>
                    
                    <div className="text-center mt-2">
                      {anosParaRetiro >= 100 ? (
                        <div className="p-3 bg-red-900/30 border border-red-800 rounded-lg">
                          <div className="flex items-center justify-center">
                            <AlertCircle className="h-5 w-5 text-red-400 mr-2" />
                            <p className="text-red-300 font-medium">
                              Al ritmo actual, no alcanzarás tu meta en un tiempo razonable.
                            </p>
                          </div>
                        </div>
                      ) : (
                        <div className="p-3 bg-blue-900/30 border border-blue-800 rounded-lg">
                          <p className="text-blue-300 font-medium">
                            Llevas un <span className="font-bold">{porcentajeAlcanzado.toFixed(1)}%</span> de tu meta. 
                            Al ritmo actual, podrías lograrla en <span className="font-bold">{anosParaRetiro < 1 ? (anosParaRetiro * 12).toFixed(0) + ' meses' : anosParaRetiro.toFixed(1) + ' años'}</span>.
                          </p>
                          <p className="text-sm text-blue-300 mt-1">
                            Edad estimada al retirarte: <span className="font-bold">{Math.round(edadEstimadaRetiro)} años</span>
                          </p>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="mt-6 bg-[#2a2b38] rounded-xl p-5">
                <h3 className="text-lg font-semibold mb-4">Proyección de tu patrimonio</h3>
                <div className="h-[300px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <RechartsLineChart
                      data={datosProyeccion}
                      margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" stroke="rgba(255,255,255,0.1)" />
                      <XAxis 
                        dataKey="año" 
                        tick={{ fill: '#ffffff' }}
                        label={{ value: 'Años', position: 'insideBottomRight', offset: -5, fill: '#ffffff' }}
                      />
                      <YAxis 
                        tick={{ fill: '#ffffff' }}
                        tickFormatter={(value) => `$${value / 1000}k`}
                      />
                      <Tooltip 
                        formatter={(value) => [`$${formatearNumero(value)}`, '']}
                        labelFormatter={(value) => `Año ${value}`}
                        contentStyle={{ backgroundColor: '#2a2b38', border: '1px solid #3f415a' }}
                      />
                      <Line 
                        type="monotone" 
                        dataKey="saldo" 
                        stroke="#96a7ff" 
                        strokeWidth={2}
                        name="Patrimonio"
                        dot={false}
                        activeDot={{ r: 8 }}
                      />
                      <Line 
                        type="monotone" 
                        dataKey="aportes" 
                        stroke="#22c55e" 
                        strokeWidth={2}
                        name="Aportes acumulados"
                        dot={false}
                        strokeDasharray="5 5"
                      />
                      <Line 
                        type="monotone" 
                        dataKey="meta" 
                        stroke="#f9769d" 
                        strokeWidth={2}
                        name="Meta FIRE"
                        dot={false}
                      />
                    </RechartsLineChart>
                  </ResponsiveContainer>
                </div>
                <div className="mt-4 grid grid-cols-3 gap-2 text-sm text-center">
                  <div className="flex items-center justify-center">
                    <div className="w-3 h-3 rounded-full bg-[#96a7ff] mr-1"></div>
                    <span>Patrimonio total</span>
                  </div>
                  <div className="flex items-center justify-center">
                    <div className="w-3 h-3 rounded-full bg-[#22c55e] mr-1"></div>
                    <span>Aportes acumulados</span>
                  </div>
                  <div className="flex items-center justify-center">
                    <div className="w-3 h-3 rounded-full bg-[#f9769d] mr-1"></div>
                    <span>Meta FIRE</span>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </Card>
      
      {/* SECCIÓN 3 - SIMULACIÓN DINÁMICA */}
      <Card>
        <div className="p-6">
          <div className="flex justify-between items-center mb-4">
            <div className="flex items-center">
              <RefreshCw className="h-6 w-6 text-green-400 mr-2" />
              <h2 className="text-xl font-semibold">Simulación Dinámica</h2>
            </div>
            <button 
              onClick={() => toggleSeccion('simulacion')}
              className="text-gray-400 hover:text-white"
            >
              {seccionesExpandidas.simulacion ? <ChevronUp /> : <ChevronDown />}
            </button>
          </div>
          
          {seccionesExpandidas.simulacion && (
            <div className="animate-fadeIn">
              <div className="overflow-x-auto">
                <table className="w-full min-w-[600px]">
                  <thead>
                    <tr className="bg-[#2a2b38] border-b border-gray-700">
                      <th className="py-3 px-4 text-left">Aporte mensual</th>
                      <th className="py-3 px-4 text-left">Años estimados</th>
                      <th className="py-3 px-4 text-left">% de vida cubierta</th>
                      <th className="py-3 px-4 text-left">Recomendación</th>
                    </tr>
                  </thead>
                  <tbody>
                    {escenarios.map((escenario, index) => (
                      <tr 
                        key={index} 
                        className={`border-b border-gray-700 hover:bg-[#2a2b38] transition-colors ${
                          escenario.aporteMensual === aporteMensual ? 'bg-[#2a2b38]' : ''
                        }`}
                      >
                        <td className="py-3 px-4">
                          <div className="flex items-center">
                            <div 
                              className={`w-2 h-10 rounded-l mr-3 ${
                                escenario.aporteMensual === aporteMensual ? 'bg-blue-500' : 'bg-transparent'
                              }`}
                            ></div>
                            ${formatearNumero(escenario.aporteMensual)}
                          </div>
                        </td>
                        <td className="py-3 px-4">
                          {escenario.años >= 100 ? (
                            <span className="text-red-400">No alcanzable</span>
                          ) : escenario.años < 1 ? (
                            <span>{Math.round(escenario.años * 12)} meses</span>
                          ) : (
                            <span>{escenario.años.toFixed(1)} años</span>
                          )}
                        </td>
                        <td className="py-3 px-4">
                          <div className="flex items-center">
                            <div className="w-16 bg-gray-700 h-2 rounded-full overflow-hidden mr-2">
                              <div 
                                className={`h-full ${
                                  escenario.porcentajeCubierto >= 75 ? "bg-green-500" : 
                                  escenario.porcentajeCubierto >= 50 ? "bg-yellow-500" : 
                                  "bg-red-500"
                                }`}
                                style={{ width: `${Math.min(100, escenario.porcentajeCubierto)}%` }}
                              ></div>
                            </div>
                            {escenario.porcentajeCubierto.toFixed(0)}%
                          </div>
                        </td>
                        <td className="py-3 px-4">
                          <span className={`inline-flex rounded-full px-2 py-1 text-xs font-medium ${
                            escenario.años >= 100 
                              ? 'bg-red-900/30 text-red-400' 
                              : escenario.años <= 10
                                ? 'bg-green-900/30 text-green-400'
                                : 'bg-blue-900/30 text-blue-400'
                          }`}>
                            {escenario.recomendacion}
                          </span>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
              
              <div className="mt-6 p-4 bg-[#2a2b38] rounded-xl">
                <h3 className="text-lg font-semibold mb-3">¿Qué pasaría si...?</h3>
                
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="bg-[#21222d] p-4 rounded-lg border border-gray-700">
                    <div className="flex items-center justify-between mb-3">
                      <span className="font-medium">Incrementas tu aporte +20%</span>
                      <ArrowRight className="h-5 w-5 text-green-400" />
                    </div>
                    <div>
                      <div className="flex justify-between mb-1">
                        <span>Aporte actual:</span>
                        <span>${formatearNumero(aporteMensual)}</span>
                      </div>
                      <div className="flex justify-between mb-1">
                        <span>Aporte 20% mayor:</span>
                        <span className="font-medium text-green-400">${formatearNumero(aporteMensual * 1.2)}</span>
                      </div>
                      <div className="flex justify-between mt-3">
                        <span>Años para FIRE:</span>
                        <span className="font-medium text-green-400">
                          {(() => {
                            let saldoActual = ahorroActual;
                            let años = 0;
                            const tasaMensual = tasaRetornoAnual / 100 / 12;
                            const aporteIncrementado = aporteMensual * 1.2;
                            
                            while (saldoActual < capitalNecesario && años < 100) {
                              saldoActual = saldoActual * (1 + tasaMensual) + aporteIncrementado;
                              años += 1/12;
                            }
                            
                            return años >= 100 ? 'No alcanzable' : años < 1 ? `${Math.round(años * 12)} meses` : `${años.toFixed(1)} años`;
                          })()}
                        </span>
                      </div>
                    </div>
                  </div>
                  
                  <div className="bg-[#21222d] p-4 rounded-lg border border-gray-700">
                    <div className="flex items-center justify-between mb-3">
                      <span className="font-medium">Tu inversión rinde +1%</span>
                      <ArrowRight className="h-5 w-5 text-blue-400" />
                    </div>
                    <div>
                      <div className="flex justify-between mb-1">
                        <span>Rendimiento actual:</span>
                        <span>{tasaRetornoAnual}%</span>
                      </div>
                      <div className="flex justify-between mb-1">
                        <span>Rendimiento mejorado:</span>
                        <span className="font-medium text-blue-400">{tasaRetornoAnual + 1}%</span>
                      </div>
                      <div className="flex justify-between mt-3">
                        <span>Años para FIRE:</span>
                        <span className="font-medium text-blue-400">
                          {(() => {
                            let saldoActual = ahorroActual;
                            let años = 0;
                            const tasaMensual = (tasaRetornoAnual + 1) / 100 / 12;
                            
                            while (saldoActual < capitalNecesario && años < 100) {
                              saldoActual = saldoActual * (1 + tasaMensual) + aporteMensual;
                              años += 1/12;
                            }
                            
                            return años >= 100 ? 'No alcanzable' : años < 1 ? `${Math.round(años * 12)} meses` : `${años.toFixed(1)} años`;
                          })()}
                        </span>
                      </div>
                    </div>
                  </div>
                  
                  <div className="bg-[#21222d] p-4 rounded-lg border border-gray-700">
                    <div className="flex items-center justify-between mb-3">
                      <span className="font-medium">Reduces gastos en retiro 10%</span>
                      <ArrowRight className="h-5 w-5 text-yellow-400" />
                    </div>
                    <div>
                      <div className="flex justify-between mb-1">
                        <span>Gasto mensual actual:</span>
                        <span>${formatearNumero(gastoMensualRetiro)}</span>
                      </div>
                      <div className="flex justify-between mb-1">
                        <span>Gasto 10% menor:</span>
                        <span className="font-medium text-yellow-400">${formatearNumero(gastoMensualRetiro * 0.9)}</span>
                      </div>
                      <div className="flex justify-between mt-3">
                        <span>Capital necesario:</span>
                        <span className="font-medium text-yellow-400">
                          ${formatearNumero(usarRegla4Porciento ? gastoMensualRetiro * 0.9 * 12 * 25 : capitalNecesario * 0.9)}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </Card>
      
      {/* SECCIÓN 4 - VINCULACIÓN CON VIDA RICA */}
      <Card>
        <div className="p-6">
          <div className="flex justify-between items-center mb-4">
            <div className="flex items-center">
              <Heart className="h-6 w-6 text-red-400 mr-2" />
              <h2 className="text-xl font-semibold">Vinculación con Vida Rica</h2>
            </div>
            <button 
              onClick={() => toggleSeccion('vidaRica')}
              className="text-gray-400 hover:text-white"
            >
              {seccionesExpandidas.vidaRica ? <ChevronUp /> : <ChevronDown />}
            </button>
          </div>
          
          {seccionesExpandidas.vidaRica && (
            <div className="animate-fadeIn">
              {esParteDeLaVisionRica ? (
                <div className="bg-[#2a2b38] rounded-xl p-6">
                  <div className="flex items-center mb-4">
                    <Umbrella className="h-10 w-10 text-[#f9769d] mr-3" />
                    <div>
                      <h3 className="text-lg font-semibold mb-1">Retiro anticipado forma parte de tu Vida Rica</h3>
                      <div className="text-sm text-gray-300">
                        Has señalado el retiro anticipado como una de tus metas de vida.
                      </div>
                    </div>
                  </div>
                  
                  <div className="p-4 bg-[#1e1e2d] rounded-lg border border-[#2a2a3d] mb-6">
                    <p className="text-[#96a7ff] font-medium text-center text-lg italic">
                      🏖️ Cada dólar que ahorras hoy, compra libertad para tu futuro Carlos.
                    </p>
                  </div>
                  
                  <div className="flex flex-col md:flex-row justify-between gap-4">
                    <div className="flex-1 p-4 bg-[#21222d] rounded-lg">
                      <h4 className="font-medium mb-2">Tu camino FIRE:</h4>
                      <div className="flex justify-between mb-2">
                        <span>Meta FIRE:</span>
                        <span>${formatearNumero(capitalNecesario)}</span>
                      </div>
                      <div className="flex justify-between mb-2">
                        <span>Progreso actual:</span>
                        <span>{porcentajeAlcanzado.toFixed(1)}%</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Años estimados:</span>
                        <span>{anosParaRetiro >= 100 ? 'No determinado' : anosParaRetiro.toFixed(1)}</span>
                      </div>
                    </div>
                    
                    <div className="flex-1 p-4 bg-[#21222d] rounded-lg">
                      <h4 className="font-medium mb-2">Sincronización con Vida Rica:</h4>
                      <p className="text-sm">
                        Tu progreso FIRE se sincroniza automáticamente con tu panel de Vida Rica, contribuyendo a tu porcentaje general de alineación.
                      </p>
                      <Link 
                        to="/vida-rica" 
                        className="mt-3 inline-flex items-center text-[#f9769d] hover:text-[#f98bab] transition-colors"
                      >
                        <span>Ver impacto en mi Vida Rica</span>
                        <ArrowRight className="h-4 w-4 ml-1" />
                      </Link>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="bg-[#2a2b38] rounded-xl p-6">
                  <div className="flex items-center mb-4">
                    <AlertCircle className="h-10 w-10 text-yellow-400 mr-3" />
                    <div>
                      <h3 className="text-lg font-semibold mb-1">Retiro anticipado no forma parte de tu Vida Rica actualmente</h3>
                      <div className="text-sm text-gray-300">
                        No has señalado el retiro anticipado como una de tus metas de vida.
                      </div>
                    </div>
                  </div>
                  
                  <div className="p-4 bg-[#1e1e2d] rounded-lg border border-[#2a2a3d] mb-6">
                    <div className="flex items-start">
                      <Info className="h-5 w-5 mr-2 text-blue-400 flex-shrink-0 mt-0.5" />
                      <p className="text-gray-300">
                        Si el retiro anticipado o la libertad financiera son importantes para ti, considera agregarlos a tu definición de Vida Rica. Esto te ayudará a mantener el foco y alinear tus finanzas con tus valores.
                      </p>
                    </div>
                  </div>
                  
                  <div className="text-center">
                    <Link 
                      to="/vida-rica" 
                      className="inline-flex items-center px-4 py-2 bg-[#f9769d] hover:bg-[#f98bab] text-white rounded-lg font-medium"
                    >
                      <Heart className="h-4 w-4 mr-2" />
                      Actualizar mi Vida Rica
                    </Link>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </Card>
    </div>
  );
}