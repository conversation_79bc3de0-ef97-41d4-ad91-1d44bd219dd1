import React, { useState } from 'react';
import { Settings, Database, Download, Upload, Trash2, RefreshCw, Shield, Bell, Palette, Globe } from 'lucide-react';
import { useTheme } from '../lib/ThemeContext';
import { useToast } from './Toast';
import { FadeIn, SlideIn } from './ui/LoadingStates';
import { PWAStatus } from './PWAPrompt';
import { NotificationSettings } from './NotificationSettings';
import { CategoryManager } from './CategoryManager';
import { useFileUpload } from '../hooks/useFileUpload';

export function AdvancedSettingsPage() {
  const { theme, toggleTheme } = useTheme();
  const { showSuccess, showError, showWarning } = useToast();
  const { getStorageUsage, clearAllFiles, exportFilesData } = useFileUpload();
  
  const [showNotificationSettings, setShowNotificationSettings] = useState(false);
  const [showCategoryManager, setShowCategoryManager] = useState(false);
  const [isExporting, setIsExporting] = useState(false);
  const [isImporting, setIsImporting] = useState(false);
  const [isClearing, setIsClearing] = useState(false);

  // Get storage usage
  const storageUsage = getStorageUsage();

  // Export all data
  const handleExportData = async () => {
    setIsExporting(true);
    try {
      const data = {
        transactions: JSON.parse(localStorage.getItem('transactions') || '[]'),
        budgets: JSON.parse(localStorage.getItem('budgets') || '[]'),
        savingsGoals: JSON.parse(localStorage.getItem('savingsGoals') || '[]'),
        recurringTransactions: JSON.parse(localStorage.getItem('recurringTransactions') || '[]'),
        customCategories: JSON.parse(localStorage.getItem('customCategories') || '[]'),
        settings: {
          theme: localStorage.getItem('theme'),
          notificationSettings: JSON.parse(localStorage.getItem('notificationSettings') || '{}')
        },
        exportedAt: new Date().toISOString(),
        version: '1.0.0'
      };

      const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `financetracker-backup-${new Date().toISOString().split('T')[0]}.json`;
      link.click();
      URL.revokeObjectURL(url);

      showSuccess('Datos exportados', 'Se ha creado una copia de seguridad de todos tus datos');
    } catch (error) {
      showError('Error al exportar', 'No se pudieron exportar los datos');
    } finally {
      setIsExporting(false);
    }
  };

  // Import data
  const handleImportData = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setIsImporting(true);
    try {
      const text = await file.text();
      const data = JSON.parse(text);

      // Validate data structure
      if (!data.version || !data.exportedAt) {
        throw new Error('Archivo de respaldo inválido');
      }

      // Confirm import
      if (!window.confirm('¿Estás seguro de que quieres importar estos datos? Esto sobrescribirá todos los datos actuales.')) {
        return;
      }

      // Import data
      if (data.transactions) localStorage.setItem('transactions', JSON.stringify(data.transactions));
      if (data.budgets) localStorage.setItem('budgets', JSON.stringify(data.budgets));
      if (data.savingsGoals) localStorage.setItem('savingsGoals', JSON.stringify(data.savingsGoals));
      if (data.recurringTransactions) localStorage.setItem('recurringTransactions', JSON.stringify(data.recurringTransactions));
      if (data.customCategories) localStorage.setItem('customCategories', JSON.stringify(data.customCategories));
      if (data.settings?.theme) localStorage.setItem('theme', data.settings.theme);
      if (data.settings?.notificationSettings) localStorage.setItem('notificationSettings', JSON.stringify(data.settings.notificationSettings));

      showSuccess('Datos importados', 'Se han restaurado todos los datos correctamente');
      
      // Reload page to apply changes
      setTimeout(() => window.location.reload(), 1000);
    } catch (error) {
      showError('Error al importar', 'No se pudieron importar los datos. Verifica que el archivo sea válido.');
    } finally {
      setIsImporting(false);
      event.target.value = '';
    }
  };

  // Clear all data
  const handleClearAllData = async () => {
    if (!window.confirm('¿Estás seguro de que quieres eliminar TODOS los datos? Esta acción no se puede deshacer.')) {
      return;
    }

    if (!window.confirm('Esta acción eliminará permanentemente todas las transacciones, presupuestos, metas y configuraciones. ¿Continuar?')) {
      return;
    }

    setIsClearing(true);
    try {
      // Clear all localStorage data
      const keysToRemove = [
        'transactions',
        'budgets',
        'savingsGoals',
        'recurringTransactions',
        'customCategories',
        'uploadedFiles',
        'notifications',
        'notificationSettings'
      ];

      keysToRemove.forEach(key => localStorage.removeItem(key));
      
      // Clear uploaded files
      clearAllFiles();

      showWarning('Datos eliminados', 'Se han eliminado todos los datos de la aplicación');
      
      // Reload page
      setTimeout(() => window.location.reload(), 1000);
    } catch (error) {
      showError('Error', 'No se pudieron eliminar todos los datos');
    } finally {
      setIsClearing(false);
    }
  };

  const settingSections = [
    {
      title: 'Apariencia',
      icon: <Palette className="w-5 h-5 text-purple-500" />,
      items: [
        {
          label: 'Tema',
          description: 'Cambiar entre tema claro y oscuro',
          action: (
            <button
              onClick={toggleTheme}
              className={`px-3 py-1 rounded-lg text-sm font-medium transition-colors duration-200 ${
                theme === 'dark'
                  ? 'bg-yellow-500 text-yellow-900 hover:bg-yellow-400'
                  : 'bg-gray-800 text-white hover:bg-gray-700'
              }`}
            >
              {theme === 'dark' ? '☀️ Claro' : '🌙 Oscuro'}
            </button>
          )
        }
      ]
    },
    {
      title: 'Notificaciones',
      icon: <Bell className="w-5 h-5 text-blue-500" />,
      items: [
        {
          label: 'Configurar notificaciones',
          description: 'Gestionar alertas de presupuestos, metas y recordatorios',
          action: (
            <button
              onClick={() => setShowNotificationSettings(true)}
              className="px-3 py-1 bg-blue-500 hover:bg-blue-600 text-white rounded-lg text-sm font-medium transition-colors duration-200"
            >
              Configurar
            </button>
          )
        }
      ]
    },
    {
      title: 'Categorías',
      icon: <Settings className="w-5 h-5 text-green-500" />,
      items: [
        {
          label: 'Gestionar categorías',
          description: 'Crear, editar y organizar categorías personalizadas',
          action: (
            <button
              onClick={() => setShowCategoryManager(true)}
              className="px-3 py-1 bg-green-500 hover:bg-green-600 text-white rounded-lg text-sm font-medium transition-colors duration-200"
            >
              Gestionar
            </button>
          )
        }
      ]
    },
    {
      title: 'Datos y Respaldo',
      icon: <Database className="w-5 h-5 text-orange-500" />,
      items: [
        {
          label: 'Exportar datos',
          description: 'Crear una copia de seguridad de todos tus datos',
          action: (
            <button
              onClick={handleExportData}
              disabled={isExporting}
              className="px-3 py-1 bg-orange-500 hover:bg-orange-600 text-white rounded-lg text-sm font-medium transition-colors duration-200 disabled:opacity-50"
            >
              {isExporting ? (
                <div className="flex items-center gap-1">
                  <RefreshCw className="w-3 h-3 animate-spin" />
                  Exportando...
                </div>
              ) : (
                <div className="flex items-center gap-1">
                  <Download className="w-3 h-3" />
                  Exportar
                </div>
              )}
            </button>
          )
        },
        {
          label: 'Importar datos',
          description: 'Restaurar datos desde una copia de seguridad',
          action: (
            <div>
              <input
                type="file"
                accept=".json"
                onChange={handleImportData}
                className="hidden"
                id="import-file"
                disabled={isImporting}
              />
              <label
                htmlFor="import-file"
                className={`px-3 py-1 bg-blue-500 hover:bg-blue-600 text-white rounded-lg text-sm font-medium transition-colors duration-200 cursor-pointer inline-flex items-center gap-1 ${
                  isImporting ? 'opacity-50 cursor-not-allowed' : ''
                }`}
              >
                {isImporting ? (
                  <>
                    <RefreshCw className="w-3 h-3 animate-spin" />
                    Importando...
                  </>
                ) : (
                  <>
                    <Upload className="w-3 h-3" />
                    Importar
                  </>
                )}
              </label>
            </div>
          )
        }
      ]
    },
    {
      title: 'Almacenamiento',
      icon: <Database className="w-5 h-5 text-gray-500" />,
      items: [
        {
          label: 'Archivos adjuntos',
          description: `${storageUsage.fileCount} archivos (${storageUsage.formattedSize})`,
          action: (
            <button
              onClick={exportFilesData}
              className="px-3 py-1 bg-gray-500 hover:bg-gray-600 text-white rounded-lg text-sm font-medium transition-colors duration-200"
            >
              Exportar lista
            </button>
          )
        }
      ]
    },
    {
      title: 'Zona de Peligro',
      icon: <Shield className="w-5 h-5 text-red-500" />,
      items: [
        {
          label: 'Eliminar todos los datos',
          description: 'Eliminar permanentemente toda la información almacenada',
          action: (
            <button
              onClick={handleClearAllData}
              disabled={isClearing}
              className="px-3 py-1 bg-red-500 hover:bg-red-600 text-white rounded-lg text-sm font-medium transition-colors duration-200 disabled:opacity-50"
            >
              {isClearing ? (
                <div className="flex items-center gap-1">
                  <RefreshCw className="w-3 h-3 animate-spin" />
                  Eliminando...
                </div>
              ) : (
                <div className="flex items-center gap-1">
                  <Trash2 className="w-3 h-3" />
                  Eliminar Todo
                </div>
              )}
            </button>
          )
        }
      ]
    }
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-3">
        <div>
          <h1 className={`text-3xl font-bold transition-colors duration-200 ${
            theme === 'dark' ? 'text-white' : 'text-slate-900'
          }`}>
            Configuración Avanzada
          </h1>
          <p className={`text-sm mt-1 transition-colors duration-200 ${
            theme === 'dark' ? 'text-gray-400' : 'text-slate-500'
          }`}>
            Personaliza la aplicación y gestiona tus datos
          </p>
        </div>
      </div>

      {/* PWA Status */}
      <PWAStatus />

      {/* Settings Sections */}
      <div className="space-y-6">
        {settingSections.map((section, sectionIndex) => (
          <SlideIn key={section.title} direction="up" delay={sectionIndex * 100}>
            <div className={`rounded-xl p-6 transition-colors duration-200 ${
              theme === 'dark' 
                ? 'bg-[#21222d] border border-[#2a2a3d]' 
                : 'bg-slate-50 border border-slate-200'
            }`}>
              <div className="flex items-center gap-3 mb-4">
                {section.icon}
                <h3 className={`text-lg font-semibold transition-colors duration-200 ${
                  theme === 'dark' ? 'text-white' : 'text-slate-900'
                }`}>
                  {section.title}
                </h3>
              </div>
              
              <div className="space-y-4">
                {section.items.map((item, itemIndex) => (
                  <div key={itemIndex} className="flex items-center justify-between">
                    <div>
                      <h4 className={`font-medium transition-colors duration-200 ${
                        theme === 'dark' ? 'text-white' : 'text-slate-900'
                      }`}>
                        {item.label}
                      </h4>
                      <p className={`text-sm transition-colors duration-200 ${
                        theme === 'dark' ? 'text-gray-400' : 'text-slate-600'
                      }`}>
                        {item.description}
                      </p>
                    </div>
                    <div className="ml-4">
                      {item.action}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </SlideIn>
        ))}
      </div>

      {/* Modals */}
      <NotificationSettings
        isOpen={showNotificationSettings}
        onClose={() => setShowNotificationSettings(false)}
      />

      <CategoryManager
        isOpen={showCategoryManager}
        onClose={() => setShowCategoryManager(false)}
      />
    </div>
  );
}
