import React from 'react';
import { format } from 'date-fns';
import { es } from 'date-fns/locale';
import { ExternalLink } from 'lucide-react';
import { useTheme } from '../lib/ThemeContext';
import { Link } from 'react-router-dom';
import { useTransactionContext } from '../lib/TransactionContext';
import { LoadingSpinner, SkeletonTable } from './LoadingSpinner';
import { useToast } from './Toast';
import { getCurrentMonth } from '../lib/calculations';

// Function to get appropriate background color for transaction icon
const getIconBackground = (category: string, type: 'income' | 'expense'): string => {
  const colorMap = {
    salario: 'bg-pink-500',
    freelance: 'bg-purple-500',
    alimentacion: 'bg-green-500',
    entretenimiento: 'bg-amber-500',
    transporte: 'bg-red-500',
    vivienda: 'bg-blue-500',
    servicios: 'bg-indigo-500',
    income: 'bg-emerald-500',
    expense: 'bg-red-500'
  };

  return colorMap[category] || (type === 'income' ? colorMap.income : colorMap.expense);
};

interface TransactionHistoryProps {
  limit?: number;
}

export function TransactionHistory({ limit = 5 }: TransactionHistoryProps) {
  const { theme } = useTheme();
  const { transactions, loading, error } = useTransactionContext();
  const { showError } = useToast();
  
  // Get current month and year for date range display
  const currentMonth = getCurrentMonth();
  const currentDate = new Date();
  const startOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);
  const formattedStartDate = format(startOfMonth, 'd', { locale: es });
  const formattedEndDate = format(currentDate, 'd MMM, yyyy', { locale: es });
  
  // Filter and limit transactions
  const displayTransactions = transactions.slice(0, limit);

  // Show error toast when error changes
  React.useEffect(() => {
    if (error) {
      showError('Error de carga', error);
    }
  }, [error, showError]);

  return (
    <div className="bg-[#21222d] rounded-xl p-4 border border-[#2a2a3d] h-fit">
      <div className="flex justify-between items-center mb-4">
        <div>
          <h3 className="text-lg font-bold">Últimos movimientos</h3>
          <p className="text-gray-400 text-xs">
            Datos del {formattedStartDate}-{formattedEndDate}
          </p>
        </div>
        <Link 
          to="/transactions" 
          className="text-[#96a7ff] hover:text-[#b8c2ff] text-xs font-medium inline-flex items-center"
        >
          Ver Todo
          <ExternalLink className="w-3 h-3 ml-1" />
        </Link>
      </div>
      
      <div className="overflow-hidden max-h-[600px] overflow-y-auto">
        {loading && (
          <SkeletonTable rows={limit} />
        )}
        
        {error && (
          <div className="bg-red-900/30 border border-red-800 text-red-300 p-3 rounded-lg mb-4">
            <p className="text-sm">Error: {error}</p>
          </div>
        )}
        
        {!loading && !error && (
        <div className="space-y-3">
            {displayTransactions.map((transaction) => (
              <div 
                key={transaction.id}
                className="flex items-center justify-between p-3 rounded-lg hover:bg-[#2a2b38] transition-colors border border-gray-700"
              >
                <div className="flex items-center flex-1 min-w-0">
                  <div className="flex items-center">
                    <div className={`w-8 h-8 rounded-full ${getIconBackground(transaction.category, transaction.type)} flex items-center justify-center text-white font-medium mr-3 flex-shrink-0`}>
                      {transaction.icon}
                    </div>
                    <div className="min-w-0 flex-1">
                      <p className="font-medium text-sm truncate">{transaction.description}</p>
                      <div className="flex items-center space-x-2 mt-1">
                        <span className="text-xs text-gray-400">
                          {format(new Date(transaction.date), 'MMM dd', { locale: es })}
                        </span>
                        <span className={`inline-flex rounded-full px-2 py-0.5 text-xs font-medium ${
                          transaction.type === 'income'
                            ? 'bg-green-900/30 text-green-400'
                            : 'bg-red-900/30 text-red-400'
                        }`}>
                          {transaction.type === 'income' ? 'Ingreso' : 'Gasto'}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
                <div className={`text-right font-medium ml-3 flex-shrink-0 ${
                  transaction.type === 'income' ? 'text-green-400' : 'text-red-400'
                }`}>
                  <span className="text-sm font-bold">
                    {transaction.type === 'income' ? '+' : '-'}${Math.round(transaction.amount).toLocaleString()}
                  </span>
                </div>
              </div>
            ))}
        </div>
        )}
        
        {!loading && !error && displayTransactions.length === 0 && (
          <div className="text-center py-8 text-gray-500">
            <p className="font-medium">No hay transacciones registradas</p>
            <p className="text-sm mt-1">Agrega tu primera transacción para comenzar</p>
          </div>
        )}
      </div>
    </div>
  );
}