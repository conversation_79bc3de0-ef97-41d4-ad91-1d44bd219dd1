import { useState, useEffect } from 'react';
import { Card } from './ui/Card';
import { 
  Wallet, ChevronDown, ChevronUp, DollarSign, Sparkles, TrendingUp, 
  BarChart2, PlusCircle, Calendar, ArrowUpRight, ArrowDownRight, Edit2,
  ExternalLink, Trash2, Info, ArrowRight, CheckCircle2, XCircle, Clock,
  Filter, Smartphone, Home, Monitor, BarChartHorizontal, 
  CircleDollarSign, LineChart
} from 'lucide-react';
import { useTheme } from '../lib/ThemeContext';
import { Link } from 'react-router-dom';
import { TooltipRoot, TooltipTrigger, TooltipContent } from './ui/Tooltip';

// Define types for our data
interface FuenteIngresoPasivo {
  id: string;
  nombre: string;
  tipo: TipoIngresoPasivo;
  ingresoMensual: number;
  variacion: number; // Positive or negative percentage
  notas: string;
  icono: JSX.Element;
  activo: boolean;
}

type TipoIngresoPasivo = 
  | 'producto_digital'
  | 'alquiler'
  | 'afiliado'
  | 'inversion'
  | 'monetizacion'
  | 'royalty'
  | 'negocio_automatizado';

interface ExperimentoIngresoPasivo {
  id: string;
  accion: string;
  estado: 'pendiente' | 'completado' | 'fallido' | 'en_progreso';
  resultado: number;
  notas: string;
}

export function IngresosPasivosPage() {
  const { theme } = useTheme();
  
  // Expandir/Colapsar secciones
  const [seccionesExpandidas, setSeccionesExpandidas] = useState({
    resumen: true,
    fuentes: true,
    crecimiento: true,
    experimentos: true
  });
  
  // SECCIÓN 1 - RESUMEN GENERAL
  const [totalIngresosMes, setTotalIngresosMes] = useState(0);
  const [totalIngresosAnio, setTotalIngresosAnio] = useState(2160); // 9 months of example data
  const [comparacionMesAnterior, setComparacionMesAnterior] = useState(15); // percentage
  const [metaMensual, setMetaMensual] = useState(500);
  const [editandoMeta, setEditandoMeta] = useState(false);
  const [nuevaMetaInput, setNuevaMetaInput] = useState('500');
  
  // SECCIÓN 2 - FUENTES DE INGRESO PASIVO
  const [fuentesIngreso, setFuentesIngreso] = useState<FuenteIngresoPasivo[]>([
    {
      id: '1',
      nombre: 'App automatizada',
      tipo: 'producto_digital',
      ingresoMensual: 150,
      variacion: 15,
      notas: 'Subió en Meta Ads',
      icono: <Smartphone className="w-5 h-5" />,
      activo: true
    },
    {
      id: '2',
      nombre: 'Airbnb ocasional',
      tipo: 'alquiler',
      ingresoMensual: 90,
      variacion: 0,
      notas: '',
      icono: <Home className="w-5 h-5" />,
      activo: true
    },
    {
      id: '3',
      nombre: 'Comisión afiliado N8N',
      tipo: 'afiliado',
      ingresoMensual: 60,
      variacion: -10,
      notas: 'Menos clics este mes',
      icono: <ExternalLink className="w-5 h-5" />,
      activo: true
    },
    {
      id: '4',
      nombre: 'Inversión ETF',
      tipo: 'inversion',
      ingresoMensual: 40,
      variacion: 5,
      notas: 'Reinvierte',
      icono: <TrendingUp className="w-5 h-5" />,
      activo: true
    },
    {
      id: '5',
      nombre: 'YouTube (contenido antiguo)',
      tipo: 'monetizacion',
      ingresoMensual: 0,
      variacion: 0,
      notas: 'En pausa',
      icono: <Monitor className="w-5 h-5" />,
      activo: false
    }
  ]);
  
  const [ordenarPor, setOrdenarPor] = useState<'monto' | 'tipo'>('monto');
  const [mostrarSoloActivos, setMostrarSoloActivos] = useState(true);
  const [editandoFuente, setEditandoFuente] = useState<string | null>(null);
  const [nuevaFuente, setNuevaFuente] = useState(false);
  const [formularioFuente, setFormularioFuente] = useState<Partial<FuenteIngresoPasivo>>({
    nombre: '',
    tipo: 'producto_digital',
    ingresoMensual: 0,
    variacion: 0,
    notas: '',
    activo: true
  });
  
  // SECCIÓN 3 - CRECIMIENTO Y OBJETIVO FIRE
  const [ingresoIdeal, setIngresoIdeal] = useState(2000);
  const [editandoIngresoIdeal, setEditandoIngresoIdeal] = useState(false);
  const [nuevoIngresoIdealInput, setNuevoIngresoIdealInput] = useState('2000');
  
  // SECCIÓN 4 - ACCIONES Y EXPERIMENTOS
  const [experimentos, setExperimentos] = useState<ExperimentoIngresoPasivo[]>([
    {
      id: '1',
      accion: 'Subir nuevo video YouTube',
      estado: 'completado',
      resultado: 10,
      notas: 'Tema: automatización'
    },
    {
      id: '2',
      accion: 'Probar afiliación con Notion',
      estado: 'fallido',
      resultado: 0,
      notas: 'Requiere más tráfico'
    },
    {
      id: '3',
      accion: 'Publicar 2 posts de venta de app',
      estado: 'en_progreso',
      resultado: 0,
      notas: 'En progreso'
    }
  ]);
  const [editandoExperimento, setEditandoExperimento] = useState<string | null>(null);
  const [nuevoExperimento, setNuevoExperimento] = useState(false);
  const [formularioExperimento, setFormularioExperimento] = useState<Partial<ExperimentoIngresoPasivo>>({
    accion: '',
    estado: 'pendiente',
    resultado: 0,
    notas: ''
  });
  
  // FUNCIONES AUXILIARES
  
  // Función para alternar el estado de expansión de una sección
  const toggleSeccion = (seccion: string) => {
    setSeccionesExpandidas({
      ...seccionesExpandidas,
      [seccion]: !seccionesExpandidas[seccion]
    });
  };
  
  // Calcular el total de ingresos pasivos mensuales
  useEffect(() => {
    const total = fuentesIngreso
      .filter(fuente => fuente.activo)
      .reduce((sum, fuente) => sum + fuente.ingresoMensual, 0);
    
    setTotalIngresosMes(total);
  }, [fuentesIngreso]);
  
  // Iconos por tipo de ingreso pasivo
  const iconosPorTipo = {
    producto_digital: <Smartphone className="w-full h-full" />,
    alquiler: <Home className="w-full h-full" />,
    afiliado: <ExternalLink className="w-full h-full" />,
    inversion: <TrendingUp className="w-full h-full" />,
    monetizacion: <Monitor className="w-full h-full" />,
    royalty: <DollarSign className="w-full h-full" />,
    negocio_automatizado: <Sparkles className="w-full h-full" />
  };
  
  // Nombres de los tipos de ingreso pasivo
  const nombresTipos = {
    producto_digital: 'Producto Digital',
    alquiler: 'Alquiler / Propiedad',
    afiliado: 'Afiliado / SaaS',
    inversion: 'Inversión / Retorno Financiero',
    monetizacion: 'Monetización de Contenido',
    royalty: 'Royalties / Derechos',
    negocio_automatizado: 'Negocio Automatizado'
  };
  
  // Ordenar fuentes de ingreso
  const fuentesOrdenadas = [...fuentesIngreso]
    .filter(fuente => mostrarSoloActivos ? fuente.activo : true)
    .sort((a, b) => {
      if (ordenarPor === 'monto') {
        return b.ingresoMensual - a.ingresoMensual;
      } else {
        // Ordenar por tipo
        return a.tipo.localeCompare(b.tipo);
      }
    });
  
  // Calcular progreso hacia el ingreso ideal
  const porcentajeIngresoIdeal = Math.min(100, (totalIngresosMes / ingresoIdeal) * 100);
  
  // Calcular meses para alcanzar el objetivo
  const calcularMesesParaObjetivo = (incrementoMensual: number) => {
    if (incrementoMensual <= 0) return Infinity;
    
    const diferencia = ingresoIdeal - totalIngresosMes;
    if (diferencia <= 0) return 0;
    
    return Math.ceil(diferencia / incrementoMensual);
  };
  
  // Meses estimados para alcanzar el objetivo con $100 de incremento mensual
  const mesesParaObjetivo = calcularMesesParaObjetivo(100);
  
  // Función para agregar o editar una fuente de ingreso
  const guardarFuente = () => {
    if (!formularioFuente.nombre || formularioFuente.ingresoMensual === undefined) {
      return; // Validación básica
    }
    
    const icono = iconosPorTipo[formularioFuente.tipo || 'producto_digital'];
    
    if (editandoFuente) {
      // Editar fuente existente
      setFuentesIngreso(fuentesIngreso.map(fuente => 
        fuente.id === editandoFuente
          ? { ...fuente, ...formularioFuente, icono } as FuenteIngresoPasivo
          : fuente
      ));
    } else {
      // Agregar nueva fuente
      const nuevaFuenteCompleta: FuenteIngresoPasivo = {
        id: Date.now().toString(),
        nombre: formularioFuente.nombre || '',
        tipo: formularioFuente.tipo || 'producto_digital',
        ingresoMensual: formularioFuente.ingresoMensual || 0,
        variacion: formularioFuente.variacion || 0,
        notas: formularioFuente.notas || '',
        icono,
        activo: formularioFuente.activo !== undefined ? formularioFuente.activo : true
      };
      
      setFuentesIngreso([...fuentesIngreso, nuevaFuenteCompleta]);
    }
    
    // Limpiar formulario y cerrar
    setFormularioFuente({
      nombre: '',
      tipo: 'producto_digital',
      ingresoMensual: 0,
      variacion: 0,
      notas: '',
      activo: true
    });
    setEditandoFuente(null);
    setNuevaFuente(false);
  };
  
  // Función para eliminar una fuente de ingreso
  const eliminarFuente = (id: string) => {
    if (confirm('¿Estás seguro de eliminar esta fuente de ingreso?')) {
      setFuentesIngreso(fuentesIngreso.filter(fuente => fuente.id !== id));
    }
  };
  
  // Función para guardar un experimento
  const guardarExperimento = () => {
    if (!formularioExperimento.accion) {
      return; // Validación básica
    }
    
    if (editandoExperimento) {
      // Editar experimento existente
      setExperimentos(experimentos.map(exp => 
        exp.id === editandoExperimento
          ? { ...exp, ...formularioExperimento } as ExperimentoIngresoPasivo
          : exp
      ));
    } else {
      // Agregar nuevo experimento
      const nuevoExperimentoCompleto: ExperimentoIngresoPasivo = {
        id: Date.now().toString(),
        accion: formularioExperimento.accion || '',
        estado: formularioExperimento.estado || 'pendiente',
        resultado: formularioExperimento.resultado || 0,
        notas: formularioExperimento.notas || ''
      };
      
      setExperimentos([...experimentos, nuevoExperimentoCompleto]);
    }
    
    // Limpiar formulario y cerrar
    setFormularioExperimento({
      accion: '',
      estado: 'pendiente',
      resultado: 0,
      notas: ''
    });
    setEditandoExperimento(null);
    setNuevoExperimento(false);
  };
  
  // Función para eliminar un experimento
  const eliminarExperimento = (id: string) => {
    if (confirm('¿Estás seguro de eliminar este experimento?')) {
      setExperimentos(experimentos.filter(exp => exp.id !== id));
    }
  };
  
  // Formateador de números
  const formatearNumero = (numero: number) => {
    return new Intl.NumberFormat('es-ES').format(Math.round(numero));
  };
  
  // Emoji motivador según el porcentaje de la meta
  const getEmojiMotivador = () => {
    const porcentaje = (totalIngresosMes / metaMensual) * 100;
    if (porcentaje >= 100) return "🚀";
    if (porcentaje >= 75) return "💪";
    if (porcentaje >= 50) return "👍";
    if (porcentaje >= 25) return "🌱";
    return "⏳";
  };
  
  // Comprobar si se ha alcanzado la libertad financiera (100% de la meta)
  const libertadFinancieraAlcanzada = totalIngresosMes >= ingresoIdeal;
  
  return (
    <div className="space-y-8 pb-20">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6">
        <h1 className="text-3xl font-bold text-white">Ingresos Pasivos</h1>
        <div className="flex items-center bg-[#2a2b38] rounded-lg py-2 px-4">
          <Sparkles className="h-5 w-5 text-yellow-500 mr-2" />
          <span className="text-sm text-gray-300">El dinero trabaja para ti, no al revés</span>
        </div>
      </div>
      
      {/* SECCIÓN 1 - RESUMEN GENERAL */}
      <Card>
        <div className="p-6">
          <div className="flex justify-between items-center mb-4">
            <div className="flex items-center">
              <BarChart2 className="h-6 w-6 text-green-400 mr-2" />
              <h2 className="text-xl font-semibold">Resumen General</h2>
            </div>
            <button 
              onClick={() => toggleSeccion('resumen')}
              className="text-gray-400 hover:text-white"
            >
              {seccionesExpandidas.resumen ? <ChevronUp /> : <ChevronDown />}
            </button>
          </div>
          
          {seccionesExpandidas.resumen && (
            <div className="animate-fadeIn">
              <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                <div className="bg-[#2a2b38] rounded-xl p-5 flex flex-col">
                  <div className="text-sm text-gray-400 mb-1">Ingresos Pasivos Este Mes</div>
                  <div className="text-3xl font-bold text-green-400">${formatearNumero(totalIngresosMes)}</div>
                  <div className="flex items-center mt-3">
                    {comparacionMesAnterior > 0 ? (
                      <>
                        <ArrowUpRight className="h-4 w-4 text-green-400 mr-1" />
                        <span className="text-green-400 text-sm">+{comparacionMesAnterior}% vs. mes anterior</span>
                      </>
                    ) : comparacionMesAnterior < 0 ? (
                      <>
                        <ArrowDownRight className="h-4 w-4 text-red-400 mr-1" />
                        <span className="text-red-400 text-sm">{comparacionMesAnterior}% vs. mes anterior</span>
                      </>
                    ) : (
                      <span className="text-gray-400 text-sm">Sin cambios vs. mes anterior</span>
                    )}
                  </div>
                </div>
                
                <div className="bg-[#2a2b38] rounded-xl p-5 flex flex-col">
                  <div className="text-sm text-gray-400 mb-1">Acumulado Anual</div>
                  <div className="text-3xl font-bold text-blue-400">${formatearNumero(totalIngresosAnio)}</div>
                  <div className="flex items-center mt-3">
                    <Calendar className="h-4 w-4 text-gray-400 mr-1" />
                    <span className="text-gray-400 text-sm">Año en curso (9 meses)</span>
                  </div>
                </div>
                
                <div className="bg-[#2a2b38] rounded-xl p-5 flex flex-col md:col-span-2">
                  <div className="flex justify-between items-center mb-3">
                    <div className="text-sm text-gray-400">Meta Mensual</div>
                    {!editandoMeta ? (
                      <button
                        onClick={() => {
                          setEditandoMeta(true);
                          setNuevaMetaInput(metaMensual.toString());
                        }}
                        className="text-gray-400 hover:text-white"
                      >
                        <Edit2 className="h-4 w-4" />
                      </button>
                    ) : (
                      <div className="flex gap-2">
                        <button
                          onClick={() => {
                            if (nuevaMetaInput && !isNaN(Number(nuevaMetaInput))) {
                              setMetaMensual(Math.max(0, Number(nuevaMetaInput)));
                            }
                            setEditandoMeta(false);
                          }}
                          className="text-green-400 hover:text-green-300"
                        >
                          <CheckCircle2 className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => setEditandoMeta(false)}
                          className="text-red-400 hover:text-red-300"
                        >
                          <XCircle className="h-4 w-4" />
                        </button>
                      </div>
                    )}
                  </div>
                  
                  {editandoMeta ? (
                    <div className="mb-3">
                      <div className="relative">
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                          <DollarSign className="h-5 w-5 text-gray-400" />
                        </div>
                        <input
                          type="number"
                          min="0"
                          value={nuevaMetaInput}
                          onChange={(e) => setNuevaMetaInput(e.target.value)}
                          className="w-full bg-[#21222d] border border-gray-700 rounded-lg p-2 pl-10 focus:border-blue-500 focus:ring-blue-500"
                        />
                      </div>
                    </div>
                  ) : (
                    <div className="mb-3">
                      <div className="flex justify-between items-center">
                        <div className="text-base">Meta: <span className="font-semibold">${formatearNumero(metaMensual)}</span></div>
                        <div className="text-base">Actual: <span className="font-semibold">${formatearNumero(totalIngresosMes)}</span> ({Math.round((totalIngresosMes / metaMensual) * 100)}%)</div>
                      </div>
                    </div>
                  )}
                  
                  <div className="relative flex-1">
                    <div className="w-full bg-gray-700 h-4 rounded-full overflow-hidden">
                      <div 
                        className={`h-full ${
                          totalIngresosMes >= metaMensual
                            ? 'bg-gradient-to-r from-green-500 to-green-400'
                            : 'bg-gradient-to-r from-blue-500 to-blue-400'
                        }`}
                        style={{ 
                          width: `${Math.min(100, (totalIngresosMes / metaMensual) * 100)}%`,
                          transition: 'width 0.5s ease-in-out'
                        }}
                      ></div>
                    </div>
                    <div className="mt-3 flex justify-center items-center gap-3">
                      <div className="text-2xl">{getEmojiMotivador()}</div>
                      <div className="text-gray-300">
                        {totalIngresosMes >= metaMensual * 1.5
                          ? "¡Estás pulverizando tu meta! ¿Es momento de establecer un nuevo reto?"
                          : totalIngresosMes >= metaMensual
                            ? "¡Meta alcanzada! ¡Tu ingreso pasivo está creciendo!"
                            : totalIngresosMes >= metaMensual * 0.75
                              ? "¡Estás muy cerca de tu meta mensual! Sigue así."
                              : totalIngresosMes >= metaMensual * 0.5
                                ? "Buen progreso. Vas por la mitad del camino."
                                : totalIngresosMes >= metaMensual * 0.25
                                  ? "Has comenzado. Revisa qué fuentes puedes potenciar."
                                  : "El camino comienza. Cada paso cuenta."}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </Card>
      
      {/* SECCIÓN 2 - FUENTES DE INGRESO PASIVO */}
      <Card>
        <div className="p-6">
          <div className="flex justify-between items-center mb-4">
            <div className="flex items-center">
              <CircleDollarSign className="h-6 w-6 text-yellow-400 mr-2" />
              <h2 className="text-xl font-semibold">Fuentes de Ingreso Pasivo</h2>
            </div>
            <div className="flex items-center gap-2">
              <button 
                onClick={() => toggleSeccion('fuentes')}
                className="text-gray-400 hover:text-white"
              >
                {seccionesExpandidas.fuentes ? <ChevronUp /> : <ChevronDown />}
              </button>
            </div>
          </div>
          
          {seccionesExpandidas.fuentes && (
            <div className="animate-fadeIn">
              <div className="flex flex-wrap justify-between items-center mb-4 gap-2">
                <div className="flex items-center gap-3">
                  <button
                    onClick={() => setOrdenarPor('monto')}
                    className={`inline-flex items-center gap-1 py-1 px-3 rounded-full text-sm ${
                      ordenarPor === 'monto' 
                        ? 'bg-blue-500 text-white' 
                        : 'bg-[#21222d] text-gray-300 hover:bg-[#2a2b38]'
                    }`}
                  >
                    <BarChartHorizontal className="w-3 h-3" />
                    <span>Por monto</span>
                  </button>
                  
                  <button
                    onClick={() => setOrdenarPor('tipo')}
                    className={`inline-flex items-center gap-1 py-1 px-3 rounded-full text-sm ${
                      ordenarPor === 'tipo' 
                        ? 'bg-blue-500 text-white' 
                        : 'bg-[#21222d] text-gray-300 hover:bg-[#2a2b38]'
                    }`}
                  >
                    <Filter className="w-3 h-3" />
                    <span>Por tipo</span>
                  </button>
                  
                  <button
                    onClick={() => setMostrarSoloActivos(!mostrarSoloActivos)}
                    className={`inline-flex items-center gap-1 py-1 px-3 rounded-full text-sm ${
                      mostrarSoloActivos 
                        ? 'bg-green-500 text-white' 
                        : 'bg-[#21222d] text-gray-300 hover:bg-[#2a2b38]'
                    }`}
                  >
                    {mostrarSoloActivos ? (
                      <>
                        <CheckCircle2 className="w-3 h-3" />
                        <span>Solo activos</span>
                      </>
                    ) : (
                      <>
                        <Filter className="w-3 h-3" />
                        <span>Todos</span>
                      </>
                    )}
                  </button>
                </div>
                
                <button
                  onClick={() => {
                    setNuevaFuente(true);
                    setFormularioFuente({
                      nombre: '',
                      tipo: 'producto_digital',
                      ingresoMensual: 0,
                      variacion: 0,
                      notas: '',
                      activo: true
                    });
                  }}
                  className="inline-flex items-center gap-1 py-2 px-3 rounded-lg text-sm bg-[#f9769d] hover:bg-[#f98bab] text-white font-medium"
                >
                  <PlusCircle className="w-4 h-4" />
                  <span>Agregar Nueva Fuente</span>
                </button>
              </div>
              
              {/* Formulario para nueva/editar fuente de ingreso */}
              {(nuevaFuente || editandoFuente) && (
                <div className="mb-6 bg-[#2a2b38] rounded-xl p-5 animate-fadeIn">
                  <div className="flex justify-between items-center mb-4">
                    <h3 className="text-lg font-semibold">
                      {editandoFuente ? 'Editar Fuente de Ingreso' : 'Nueva Fuente de Ingreso'}
                    </h3>
                    <button
                      onClick={() => {
                        setNuevaFuente(false);
                        setEditandoFuente(null);
                      }}
                      className="text-gray-400 hover:text-white"
                    >
                      <XCircle className="w-5 h-5" />
                    </button>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-400 mb-1">
                        Nombre de la Fuente
                      </label>
                      <input
                        type="text"
                        value={formularioFuente.nombre || ''}
                        onChange={(e) => setFormularioFuente({...formularioFuente, nombre: e.target.value})}
                        className="w-full bg-[#21222d] border border-gray-700 rounded-lg p-2 focus:border-blue-500 focus:ring-blue-500"
                        placeholder="Ej. Curso Digital, Alquiler, etc."
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-400 mb-1">
                        Tipo de Ingreso
                      </label>
                      <select
                        value={formularioFuente.tipo || 'producto_digital'}
                        onChange={(e) => setFormularioFuente({...formularioFuente, tipo: e.target.value as TipoIngresoPasivo})}
                        className="w-full bg-[#21222d] border border-gray-700 rounded-lg p-2 focus:border-blue-500 focus:ring-blue-500"
                      >
                        {Object.entries(nombresTipos).map(([key, value]) => (
                          <option key={key} value={key}>{value}</option>
                        ))}
                      </select>
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-400 mb-1">
                        Ingreso Mensual ($)
                      </label>
                      <div className="relative">
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                          <DollarSign className="h-5 w-5 text-gray-400" />
                        </div>
                        <input
                          type="number"
                          min="0"
                          step="0.01"
                          value={formularioFuente.ingresoMensual || 0}
                          onChange={(e) => setFormularioFuente({...formularioFuente, ingresoMensual: Number(e.target.value)})}
                          className="w-full bg-[#21222d] border border-gray-700 rounded-lg p-2 pl-10 focus:border-blue-500 focus:ring-blue-500"
                        />
                      </div>
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-400 mb-1">
                        Variación (%)
                      </label>
                      <div className="relative">
                        <input
                          type="number"
                          value={formularioFuente.variacion || 0}
                          onChange={(e) => setFormularioFuente({...formularioFuente, variacion: Number(e.target.value)})}
                          className="w-full bg-[#21222d] border border-gray-700 rounded-lg p-2 focus:border-blue-500 focus:ring-blue-500"
                        />
                      </div>
                    </div>
                    
                    <div className="md:col-span-2">
                      <label className="block text-sm font-medium text-gray-400 mb-1">
                        Notas
                      </label>
                      <textarea
                        value={formularioFuente.notas || ''}
                        onChange={(e) => setFormularioFuente({...formularioFuente, notas: e.target.value})}
                        className="w-full bg-[#21222d] border border-gray-700 rounded-lg p-2 focus:border-blue-500 focus:ring-blue-500 h-20"
                        placeholder="Cualquier detalle adicional..."
                      />
                    </div>
                    
                    <div className="flex items-center">
                      <label className="flex items-center text-sm font-medium text-gray-400">
                        <input
                          type="checkbox"
                          checked={formularioFuente.activo === undefined ? true : formularioFuente.activo}
                          onChange={(e) => setFormularioFuente({...formularioFuente, activo: e.target.checked})}
                          className="mr-2 rounded bg-[#21222d] border-gray-700 text-blue-500 focus:ring-blue-500"
                        />
                        Fuente activa actualmente
                      </label>
                    </div>
                  </div>
                  
                  <div className="flex justify-end gap-2">
                    <button
                      onClick={() => {
                        setNuevaFuente(false);
                        setEditandoFuente(null);
                      }}
                      className="px-4 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg"
                    >
                      Cancelar
                    </button>
                    <button
                      onClick={guardarFuente}
                      className="px-4 py-2 bg-blue-600 hover:bg-blue-500 text-white rounded-lg"
                    >
                      Guardar
                    </button>
                  </div>
                </div>
              )}
              
              <div>
                {fuentesOrdenadas.length > 0 ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {fuentesOrdenadas.map((fuente) => (
                      <div 
                        key={fuente.id} 
                        className={`bg-[#2a2b38] rounded-xl overflow-hidden border ${
                          fuente.activo 
                            ? 'border-gray-700' 
                            : 'border-red-900/30 border-dashed'
                        }`}
                      >
                        <div className="flex p-4">
                          <div className="flex-shrink-0 w-12 h-12 rounded-xl bg-[#21222d] flex items-center justify-center text-blue-400 mr-3">
                            {fuente.icono}
                          </div>
                          <div className="flex-grow">
                            <div className="flex justify-between">
                              <h3 className="font-semibold">{fuente.nombre}</h3>
                              <div className="flex gap-1">
                                <button
                                  onClick={() => {
                                    setEditandoFuente(fuente.id);
                                    setFormularioFuente({
                                      nombre: fuente.nombre,
                                      tipo: fuente.tipo,
                                      ingresoMensual: fuente.ingresoMensual,
                                      variacion: fuente.variacion,
                                      notas: fuente.notas,
                                      activo: fuente.activo
                                    });
                                  }}
                                  className="text-gray-400 hover:text-white"
                                >
                                  <Edit2 className="w-4 h-4" />
                                </button>
                                <button
                                  onClick={() => eliminarFuente(fuente.id)}
                                  className="text-gray-400 hover:text-red-400"
                                >
                                  <Trash2 className="w-4 h-4" />
                                </button>
                              </div>
                            </div>
                            <div className="text-sm text-gray-400">{nombresTipos[fuente.tipo]}</div>
                          </div>
                        </div>
                        
                        <div className="px-4 pb-4">
                          <div className="flex justify-between mb-2">
                            <div className="text-sm text-gray-400">Ingreso mensual:</div>
                            <div className={`font-semibold ${fuente.activo ? 'text-green-400' : 'text-gray-500'}`}>
                              ${formatearNumero(fuente.ingresoMensual)}
                            </div>
                          </div>
                          
                          <div className="flex justify-between items-center">
                            <div className="text-sm text-gray-400">Variación:</div>
                            <div className={`font-medium text-sm ${
                              fuente.variacion > 0 ? 'text-green-400' : 
                              fuente.variacion < 0 ? 'text-red-400' : 'text-gray-400'
                            }`}>
                              {fuente.variacion > 0 ? '+' : ''}{fuente.variacion}%
                            </div>
                          </div>
                          
                          {fuente.notas && (
                            <div className="mt-2 p-2 bg-[#21222d] rounded-lg text-sm text-gray-300">
                              {fuente.notas}
                            </div>
                          )}
                          
                          {!fuente.activo && (
                            <div className="mt-2 text-xs text-red-400 italic">
                              Esta fuente está inactiva
                            </div>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8 bg-[#2a2b38] rounded-xl">
                    <CircleDollarSign className="w-12 h-12 text-gray-600 mx-auto mb-2" />
                    <p className="text-gray-400">No hay fuentes de ingreso pasivo registradas.</p>
                    <button
                      onClick={() => {
                        setNuevaFuente(true);
                        setFormularioFuente({
                          nombre: '',
                          tipo: 'producto_digital',
                          ingresoMensual: 0,
                          variacion: 0,
                          notas: '',
                          activo: true
                        });
                      }}
                      className="mt-4 px-4 py-2 bg-blue-600 hover:bg-blue-500 text-white rounded-lg inline-flex items-center gap-1"
                    >
                      <PlusCircle className="w-4 h-4" />
                      <span>Agregar Primera Fuente</span>
                    </button>
                  </div>
                )}
                
                {/* Estadísticas respecto al ingreso total */}
                {fuentesOrdenadas.length > 0 && (
                  <div className="mt-6 p-5 bg-[#2a2b38] rounded-xl">
                    <h3 className="text-lg font-semibold mb-4">Distribución de Fuentes</h3>
                    <div className="overflow-x-auto">
                      <table className="min-w-full">
                        <thead>
                          <tr className="border-b border-gray-700">
                            <th className="text-left py-2">Fuente</th>
                            <th className="text-right py-2">Ingreso</th>
                            <th className="text-right py-2">%</th>
                          </tr>
                        </thead>
                        <tbody>
                          {fuentesOrdenadas.map((fuente) => {
                            const porcentaje = totalIngresosMes > 0 
                              ? (fuente.ingresoMensual / totalIngresosMes) * 100 
                              : 0;
                            
                            return (
                              <tr key={fuente.id} className="border-b border-gray-700">
                                <td className="py-2 flex items-center">
                                  <div className="w-6 h-6 rounded-md bg-[#21222d] flex items-center justify-center text-blue-400 mr-2">
                                    {fuente.icono}
                                  </div>
                                  <span className={fuente.activo ? '' : 'text-gray-500'}>{fuente.nombre}</span>
                                </td>
                                <td className={`text-right py-2 ${fuente.activo ? 'text-green-400' : 'text-gray-500'}`}>
                                  ${formatearNumero(fuente.ingresoMensual)}
                                </td>
                                <td className="text-right py-2">
                                  <div className="flex items-center justify-end">
                                    <div className="w-16 bg-gray-700 h-2 rounded-full overflow-hidden mr-2">
                                      <div 
                                        className={`h-full ${
                                          fuente.activo ? 'bg-blue-500' : 'bg-gray-600'
                                        }`}
                                        style={{ width: `${porcentaje}%` }}
                                      ></div>
                                    </div>
                                    <span className={fuente.activo ? 'text-blue-400' : 'text-gray-500'}>
                                      {porcentaje.toFixed(1)}%
                                    </span>
                                  </div>
                                </td>
                              </tr>
                            );
                          })}
                        </tbody>
                      </table>
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </Card>
      
      {/* SECCIÓN 3 - CRECIMIENTO Y OBJETIVO FIRE */}
      <Card>
        <div className="p-6">
          <div className="flex justify-between items-center mb-4">
            <div className="flex items-center">
              <LineChart className="h-6 w-6 text-[#f9769d] mr-2" />
              <h2 className="text-xl font-semibold">Crecimiento y Objetivo FIRE</h2>
            </div>
            <button 
              onClick={() => toggleSeccion('crecimiento')}
              className="text-gray-400 hover:text-white"
            >
              {seccionesExpandidas.crecimiento ? <ChevronUp /> : <ChevronDown />}
            </button>
          </div>
          
          {seccionesExpandidas.crecimiento && (
            <div className="animate-fadeIn">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="md:col-span-1 bg-[#2a2b38] rounded-xl p-5">
                  <div className="flex justify-between items-center mb-3">
                    <div className="text-base font-medium">Ingreso Pasivo Ideal</div>
                    {!editandoIngresoIdeal ? (
                      <button
                        onClick={() => {
                          setEditandoIngresoIdeal(true);
                          setNuevoIngresoIdealInput(ingresoIdeal.toString());
                        }}
                        className="text-gray-400 hover:text-white"
                      >
                        <Edit2 className="h-4 w-4" />
                      </button>
                    ) : (
                      <div className="flex gap-2">
                        <button
                          onClick={() => {
                            if (nuevoIngresoIdealInput && !isNaN(Number(nuevoIngresoIdealInput))) {
                              setIngresoIdeal(Math.max(1, Number(nuevoIngresoIdealInput)));
                            }
                            setEditandoIngresoIdeal(false);
                          }}
                          className="text-green-400 hover:text-green-300"
                        >
                          <CheckCircle2 className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => setEditandoIngresoIdeal(false)}
                          className="text-red-400 hover:text-red-300"
                        >
                          <XCircle className="h-4 w-4" />
                        </button>
                      </div>
                    )}
                  </div>
                  
                  {editandoIngresoIdeal ? (
                    <div className="mb-3">
                      <div className="relative">
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                          <DollarSign className="h-5 w-5 text-gray-400" />
                        </div>
                        <input
                          type="number"
                          min="1"
                          value={nuevoIngresoIdealInput}
                          onChange={(e) => setNuevoIngresoIdealInput(e.target.value)}
                          className="w-full bg-[#21222d] border border-gray-700 rounded-lg p-2 pl-10 focus:border-blue-500 focus:ring-blue-500"
                        />
                      </div>
                    </div>
                  ) : (
                    <div className="text-3xl font-bold text-blue-400 mb-3">${formatearNumero(ingresoIdeal)}/mes</div>
                  )}
                  
                  <div className="text-sm text-gray-300 mb-4">
                    <p>Este es el ingreso mensual pasivo que te permitiría libertad financiera.</p>
                  </div>
                  
                  <div className="flex items-center">
                    <Link 
                      to="/calculadora-fire" 
                      className="text-[#f9769d] hover:text-[#f98bab] text-sm inline-flex items-center"
                    >
                      <span>Conectar con Calculadora FIRE</span>
                      <ArrowRight className="w-4 h-4 ml-1" />
                    </Link>
                  </div>
                </div>
                
                <div className="md:col-span-2 bg-[#2a2b38] rounded-xl p-5">
                  <div className="flex justify-between items-center mb-3">
                    <div className="text-base font-medium">Tu progreso hacia la libertad financiera</div>
                    <div className="text-sm text-blue-400">
                      {porcentajeIngresoIdeal.toFixed(1)}% completado
                    </div>
                  </div>
                  
                  <div className="w-full bg-gray-700 h-4 rounded-full overflow-hidden mb-4">
                    <div 
                      className={`h-full ${
                        porcentajeIngresoIdeal >= 100
                          ? 'bg-gradient-to-r from-green-500 to-green-400'
                          : 'bg-gradient-to-r from-blue-500 to-[#f9769d]'
                      }`}
                      style={{ 
                        width: `${Math.min(100, porcentajeIngresoIdeal)}%`,
                        transition: 'width 0.5s ease-in-out'
                      }}
                    ></div>
                  </div>
                  
                  <div className="text-center mb-4">
                    <p className="text-lg text-gray-300">
                      Actualmente generas el <span className="font-bold text-white">{porcentajeIngresoIdeal.toFixed(1)}%</span> de tu ingreso pasivo objetivo.
                    </p>
                  </div>
                  
                  {libertadFinancieraAlcanzada ? (
                    <div className="p-4 bg-green-900/30 border border-green-800 rounded-lg text-center">
                      <p className="text-green-300 font-bold text-lg">
                        🎉 ¡Felicidades, Carlos! Has alcanzado libertad financiera técnica.
                      </p>
                      <p className="text-green-300 text-sm mt-1">
                        Tus ingresos pasivos cubren tu objetivo mensual.
                      </p>
                    </div>
                  ) : (
                    <div className="p-4 bg-blue-900/30 border border-blue-800 rounded-lg">
                      <div className="flex items-start">
                        <Info className="h-5 w-5 mr-2 text-blue-400 flex-shrink-0 mt-0.5" />
                        <div>
                          <p className="text-blue-300">
                            <span className="font-medium">Sugerencia: </span> 
                            Si aumentas $100/mes en ingresos pasivos, llegarías a tu objetivo en {mesesParaObjetivo > 60 
                              ? "más de 5 años" 
                              : `${mesesParaObjetivo} meses (${(mesesParaObjetivo / 12).toFixed(1)} años)`}.
                          </p>
                          <div className="mt-2 grid grid-cols-1 md:grid-cols-3 gap-2">
                            <div className="bg-[#21222d] p-2 rounded-lg text-center">
                              <div className="text-sm text-gray-400">+$50/mes</div>
                              <div className="text-blue-400">
                                {calcularMesesParaObjetivo(50) > 60 
                                  ? ">5 años" 
                                  : `${calcularMesesParaObjetivo(50)} meses`}
                              </div>
                            </div>
                            <div className="bg-[#21222d] p-2 rounded-lg text-center">
                              <div className="text-sm text-gray-400">+$100/mes</div>
                              <div className="text-blue-400">
                                {calcularMesesParaObjetivo(100) > 60 
                                  ? ">5 años" 
                                  : `${calcularMesesParaObjetivo(100)} meses`}
                              </div>
                            </div>
                            <div className="bg-[#21222d] p-2 rounded-lg text-center">
                              <div className="text-sm text-gray-400">+$200/mes</div>
                              <div className="text-blue-400">
                                {calcularMesesParaObjetivo(200) > 60 
                                  ? ">5 años" 
                                  : `${calcularMesesParaObjetivo(200)} meses`}
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}
        </div>
      </Card>
      
      {/* SECCIÓN 4 - ACCIONES Y EXPERIMENTOS */}
      <Card>
        <div className="p-6">
          <div className="flex justify-between items-center mb-4">
            <div className="flex items-center">
              <Sparkles className="h-6 w-6 text-amber-400 mr-2" />
              <h2 className="text-xl font-semibold">Acciones y Experimentos</h2>
            </div>
            <div className="flex items-center gap-2">
              <button 
                onClick={() => toggleSeccion('experimentos')}
                className="text-gray-400 hover:text-white"
              >
                {seccionesExpandidas.experimentos ? <ChevronUp /> : <ChevronDown />}
              </button>
            </div>
          </div>
          
          {seccionesExpandidas.experimentos && (
            <div className="animate-fadeIn">
              <div className="flex justify-between items-center mb-4">
                <p className="text-gray-300">
                  Registra experimentos para incrementar tus ingresos pasivos.
                </p>
                <button
                  onClick={() => {
                    setNuevoExperimento(true);
                    setFormularioExperimento({
                      accion: '',
                      estado: 'pendiente',
                      resultado: 0,
                      notas: ''
                    });
                  }}
                  className="inline-flex items-center gap-1 py-2 px-3 rounded-lg text-sm bg-amber-500 hover:bg-amber-400 text-white font-medium"
                >
                  <PlusCircle className="w-4 h-4" />
                  <span>Agregar Experimento</span>
                </button>
              </div>
              
              {/* Formulario para nuevo/editar experimento */}
              {(nuevoExperimento || editandoExperimento) && (
                <div className="mb-6 bg-[#2a2b38] rounded-xl p-5 animate-fadeIn">
                  <div className="flex justify-between items-center mb-4">
                    <h3 className="text-lg font-semibold">
                      {editandoExperimento ? 'Editar Experimento' : 'Nuevo Experimento'}
                    </h3>
                    <button
                      onClick={() => {
                        setNuevoExperimento(false);
                        setEditandoExperimento(null);
                      }}
                      className="text-gray-400 hover:text-white"
                    >
                      <XCircle className="w-5 h-5" />
                    </button>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-400 mb-1">
                        Acción a probar
                      </label>
                      <input
                        type="text"
                        value={formularioExperimento.accion || ''}
                        onChange={(e) => setFormularioExperimento({...formularioExperimento, accion: e.target.value})}
                        className="w-full bg-[#21222d] border border-gray-700 rounded-lg p-2 focus:border-blue-500 focus:ring-blue-500"
                        placeholder="Ej. Crear un nuevo producto digital"
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-400 mb-1">
                        Estado
                      </label>
                      <select
                        value={formularioExperimento.estado || 'pendiente'}
                        onChange={(e) => setFormularioExperimento({...formularioExperimento, estado: e.target.value as 'pendiente' | 'completado' | 'fallido' | 'en_progreso'})}
                        className="w-full bg-[#21222d] border border-gray-700 rounded-lg p-2 focus:border-blue-500 focus:ring-blue-500"
                      >
                        <option value="pendiente">Pendiente</option>
                        <option value="en_progreso">En Progreso</option>
                        <option value="completado">Completado</option>
                        <option value="fallido">Fallido</option>
                      </select>
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-400 mb-1">
                        Resultado ($ de ingreso mensual generado)
                      </label>
                      <div className="relative">
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                          <DollarSign className="h-5 w-5 text-gray-400" />
                        </div>
                        <input
                          type="number"
                          min="0"
                          step="0.01"
                          value={formularioExperimento.resultado || 0}
                          onChange={(e) => setFormularioExperimento({...formularioExperimento, resultado: Number(e.target.value)})}
                          className="w-full bg-[#21222d] border border-gray-700 rounded-lg p-2 pl-10 focus:border-blue-500 focus:ring-blue-500"
                        />
                      </div>
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-400 mb-1">
                        Notas
                      </label>
                      <input
                        type="text"
                        value={formularioExperimento.notas || ''}
                        onChange={(e) => setFormularioExperimento({...formularioExperimento, notas: e.target.value})}
                        className="w-full bg-[#21222d] border border-gray-700 rounded-lg p-2 focus:border-blue-500 focus:ring-blue-500"
                        placeholder="Observaciones o aprendizajes..."
                      />
                    </div>
                  </div>
                  
                  <div className="flex justify-end gap-2">
                    <button
                      onClick={() => {
                        setNuevoExperimento(false);
                        setEditandoExperimento(null);
                      }}
                      className="px-4 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg"
                    >
                      Cancelar
                    </button>
                    <button
                      onClick={guardarExperimento}
                      className="px-4 py-2 bg-amber-600 hover:bg-amber-500 text-white rounded-lg"
                    >
                      Guardar
                    </button>
                  </div>
                </div>
              )}
              
              <div className="overflow-x-auto">
                <table className="w-full min-w-[600px]">
                  <thead>
                    <tr className="bg-[#2a2b38] border-b border-gray-700">
                      <th className="py-3 px-4 text-left">Acción a probar</th>
                      <th className="py-3 px-4 text-center">Estado</th>
                      <th className="py-3 px-4 text-right">Resultado</th>
                      <th className="py-3 px-4 text-left">Notas</th>
                      <th className="py-3 px-4 text-center">Acciones</th>
                    </tr>
                  </thead>
                  <tbody>
                    {experimentos.length > 0 ? (
                      experimentos.map((exp) => (
                        <tr key={exp.id} className="border-b border-gray-700 hover:bg-[#2a2b38]">
                          <td className="py-3 px-4">
                            {exp.accion}
                          </td>
                          <td className="py-3 px-4 text-center">
                            <span className={`inline-flex rounded-full px-2 py-1 text-xs font-medium ${
                              exp.estado === 'completado' ? 'bg-green-900/30 text-green-400' :
                              exp.estado === 'fallido' ? 'bg-red-900/30 text-red-400' :
                              exp.estado === 'en_progreso' ? 'bg-blue-900/30 text-blue-400' :
                              'bg-gray-700 text-gray-300'
                            }`}>
                              {exp.estado === 'completado' && <CheckCircle2 className="w-3 h-3 mr-1" />}
                              {exp.estado === 'fallido' && <XCircle className="w-3 h-3 mr-1" />}
                              {exp.estado === 'en_progreso' && <Clock className="w-3 h-3 mr-1" />}
                              {exp.estado === 'completado' ? 'Completado' :
                               exp.estado === 'fallido' ? 'Fallido' :
                               exp.estado === 'en_progreso' ? 'En progreso' : 'Pendiente'}
                            </span>
                          </td>
                          <td className="py-3 px-4 text-right">
                            <span className={exp.resultado > 0 ? 'text-green-400 font-semibold' : 'text-gray-400'}>
                              {exp.resultado > 0 ? `+$${exp.resultado}` : '$0'}
                            </span>
                          </td>
                          <td className="py-3 px-4 text-sm text-gray-300">
                            {exp.notas || '-'}
                          </td>
                          <td className="py-3 px-4 text-center">
                            <div className="flex justify-center gap-1">
                              <button
                                onClick={() => {
                                  setEditandoExperimento(exp.id);
                                  setFormularioExperimento({
                                    accion: exp.accion,
                                    estado: exp.estado,
                                    resultado: exp.resultado,
                                    notas: exp.notas
                                  });
                                }}
                                className="text-gray-400 hover:text-white"
                              >
                                <Edit2 className="w-4 h-4" />
                              </button>
                              <button
                                onClick={() => eliminarExperimento(exp.id)}
                                className="text-gray-400 hover:text-red-400"
                              >
                                <Trash2 className="w-4 h-4" />
                              </button>
                            </div>
                          </td>
                        </tr>
                      ))
                    ) : (
                      <tr>
                        <td colSpan={5} className="py-10 text-center text-gray-400">
                          <Sparkles className="w-10 h-10 mx-auto mb-2 opacity-30" />
                          <p>No hay experimentos registrados aún.</p>
                          <p className="text-sm mt-1">¡Agrega uno para comenzar a incrementar tus ingresos pasivos!</p>
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>
              
              {experimentos.length > 0 && (
                <div className="mt-6 p-5 bg-[#2a2b38] rounded-xl">
                  <h3 className="text-lg font-semibold mb-3">Resumen de Experimentos</h3>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div className="bg-[#21222d] p-3 rounded-lg">
                      <div className="text-sm text-gray-400 mb-1">Total Experimentos</div>
                      <div className="text-2xl font-bold">{experimentos.length}</div>
                    </div>
                    
                    <div className="bg-[#21222d] p-3 rounded-lg">
                      <div className="text-sm text-gray-400 mb-1">Completados</div>
                      <div className="text-2xl font-bold text-green-400">
                        {experimentos.filter(e => e.estado === 'completado').length}
                      </div>
                    </div>
                    
                    <div className="bg-[#21222d] p-3 rounded-lg">
                      <div className="text-sm text-gray-400 mb-1">En Progreso</div>
                      <div className="text-2xl font-bold text-blue-400">
                        {experimentos.filter(e => e.estado === 'en_progreso').length}
                      </div>
                    </div>
                    
                    <div className="bg-[#21222d] p-3 rounded-lg">
                      <div className="text-sm text-gray-400 mb-1">Ingreso Generado</div>
                      <div className="text-2xl font-bold text-green-400">
                        ${formatearNumero(experimentos.reduce((sum, exp) => sum + exp.resultado, 0))}
                      </div>
                    </div>
                  </div>
                  
                  <div className="mt-4 bg-[#1e1e2d] p-3 rounded-lg border border-[#2a2a3d]">
                    <div className="flex items-start">
                      <Info className="h-5 w-5 mr-2 text-blue-400 flex-shrink-0 mt-0.5" />
                      <div className="text-sm text-gray-300">
                        <p>Los experimentos exitosos deberían convertirse en nuevas fuentes de ingreso pasivo una vez validados.</p>
                        <p className="mt-1">La tasa de éxito típica es del 20-30%. Persiste y experimenta continuamente.</p>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </Card>
    </div>
  );
}