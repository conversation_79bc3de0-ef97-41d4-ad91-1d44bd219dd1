/*
  # Emergency Fund Configuration

  1. New Tables
    - `emergency_fund_config`
      - `id` (uuid, primary key)
      - `user_id` (uuid, foreign key to users)
      - `monthly_expenses` (numeric) - Estimated monthly expenses
      - `coverage_months` (integer) - Desired months of coverage (3-6)
      - `created_at` (timestamp)
      - `updated_at` (timestamp)

  2. Security
    - Enable RLS on `emergency_fund_config` table
    - Add policies for users to manage their own emergency fund config
*/

CREATE TABLE IF NOT EXISTS emergency_fund_config (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  monthly_expenses numeric DEFAULT 3000 CHECK (monthly_expenses > 0),
  coverage_months integer DEFAULT 3 CHECK (coverage_months >= 3 AND coverage_months <= 6),
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

ALTER TABLE emergency_fund_config ENABLE ROW LEVEL SECURITY;

-- Ensure each user has only one config
CREATE UNIQUE INDEX IF NOT EXISTS emergency_fund_config_user_id_unique 
ON emergency_fund_config(user_id);

-- Policies
CREATE POLICY "Users can view own emergency fund config"
  ON emergency_fund_config
  FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own emergency fund config"
  ON emergency_fund_config
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own emergency fund config"
  ON emergency_fund_config
  FOR UPDATE
  TO authenticated
  USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can delete own emergency fund config"
  ON emergency_fund_config
  FOR DELETE
  TO authenticated
  USING (auth.uid() = user_id);

-- Trigger to update updated_at
CREATE OR REPLACE FUNCTION update_emergency_fund_config_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_emergency_fund_config_updated_at
    BEFORE UPDATE ON emergency_fund_config
    FOR EACH ROW
    EXECUTE FUNCTION update_emergency_fund_config_updated_at();