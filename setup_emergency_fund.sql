-- <PERSON><PERSON><PERSON> para configurar la tabla emergency_fund_config en Supabase
-- Eje<PERSON>a este script en el SQL Editor de tu proyecto de Supabase

-- 1. <PERSON><PERSON>r la tabla emergency_fund_config
CREATE TABLE IF NOT EXISTS emergency_fund_config (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  monthly_expenses numeric DEFAULT 3000 CHECK (monthly_expenses > 0),
  coverage_months integer DEFAULT 3 CHECK (coverage_months >= 3 AND coverage_months <= 6),
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- 2. Habilitar Row Level Security
ALTER TABLE emergency_fund_config ENABLE ROW LEVEL SECURITY;

-- 3. <PERSON><PERSON><PERSON> índice único para asegurar una configuración por usuario
CREATE UNIQUE INDEX IF NOT EXISTS emergency_fund_config_user_id_unique 
ON emergency_fund_config(user_id);

-- 4. <PERSON><PERSON><PERSON> políticas de seguridad
CREATE POLICY "Users can view own emergency fund config"
  ON emergency_fund_config
  FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own emergency fund config"
  ON emergency_fund_config
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own emergency fund config"
  ON emergency_fund_config
  FOR UPDATE
  TO authenticated
  USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can delete own emergency fund config"
  ON emergency_fund_config
  FOR DELETE
  TO authenticated
  USING (auth.uid() = user_id);

-- 5. Crear función para actualizar timestamp
CREATE OR REPLACE FUNCTION update_emergency_fund_config_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 6. Crear trigger para actualizar automáticamente updated_at
DROP TRIGGER IF EXISTS update_emergency_fund_config_updated_at ON emergency_fund_config;
CREATE TRIGGER update_emergency_fund_config_updated_at
    BEFORE UPDATE ON emergency_fund_config
    FOR EACH ROW
    EXECUTE FUNCTION update_emergency_fund_config_updated_at();

-- 7. Verificar que la tabla transactions tenga user_id (necesario para el fondo de emergencia)
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'transactions' AND column_name = 'user_id'
    ) THEN
        ALTER TABLE transactions ADD COLUMN user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE;
        
        -- Actualizar políticas de transactions para incluir user_id
        DROP POLICY IF EXISTS "Allow all operations for now" ON transactions;
        DROP POLICY IF EXISTS "Users can view own transactions" ON transactions;
        DROP POLICY IF EXISTS "Users can insert own transactions" ON transactions;
        DROP POLICY IF EXISTS "Users can update own transactions" ON transactions;
        DROP POLICY IF EXISTS "Users can delete own transactions" ON transactions;
        
        CREATE POLICY "Users can view own transactions" ON transactions
          FOR SELECT USING (auth.uid() = user_id);

        CREATE POLICY "Users can insert own transactions" ON transactions
          FOR INSERT WITH CHECK (auth.uid() = user_id);

        CREATE POLICY "Users can update own transactions" ON transactions
          FOR UPDATE USING (auth.uid() = user_id);

        CREATE POLICY "Users can delete own transactions" ON transactions
          FOR DELETE USING (auth.uid() = user_id);
    END IF;
END $$;

-- Mensaje de confirmación
SELECT 'Configuración del fondo de emergencia completada exitosamente!' as status;
