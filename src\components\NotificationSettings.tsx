import React, { useState } from 'react';
import { <PERSON>tings, Bell, AlertTriangle, Target, Trophy, Calendar, Mail, Smartphone } from 'lucide-react';
import { useTheme } from '../lib/ThemeContext';
import { useNotificationSystem } from '../hooks/useNotificationSystem';
import { useToast } from './Toast';
import { FadeIn } from './ui/LoadingStates';

interface NotificationSettingsProps {
  isOpen: boolean;
  onClose: () => void;
}

export function NotificationSettings({ isOpen, onClose }: NotificationSettingsProps) {
  const { theme } = useTheme();
  const { settings, saveSettings, requestNotificationPermission } = useNotificationSystem();
  const { showSuccess, showError } = useToast();
  
  const [localSettings, setLocalSettings] = useState(settings);
  const [isRequestingPermission, setIsRequestingPermission] = useState(false);

  const handleSave = () => {
    saveSettings(localSettings);
    showSuccess('Configuración guardada', 'Las preferencias de notificaciones se actualizaron correctamente');
    onClose();
  };

  const handleRequestPermission = async () => {
    setIsRequestingPermission(true);
    try {
      const granted = await requestNotificationPermission();
      if (granted) {
        setLocalSettings(prev => ({ ...prev, pushNotifications: true }));
        showSuccess('Permisos concedidos', 'Ahora recibirás notificaciones del navegador');
      } else {
        showError('Permisos denegados', 'No se pudieron habilitar las notificaciones del navegador');
      }
    } catch (error) {
      showError('Error', 'Hubo un problema al solicitar permisos de notificación');
    } finally {
      setIsRequestingPermission(false);
    }
  };

  if (!isOpen) return null;

  const settingsSections = [
    {
      title: 'Alertas de Presupuesto',
      icon: <AlertTriangle className="w-5 h-5 text-orange-500" />,
      settings: [
        {
          key: 'budgetWarnings',
          label: 'Alertas de presupuesto',
          description: 'Recibir notificaciones cuando te acerques al límite de un presupuesto',
          type: 'toggle' as const
        },
        {
          key: 'budgetWarningThreshold',
          label: 'Umbral de alerta (%)',
          description: 'Porcentaje del presupuesto para enviar alerta',
          type: 'number' as const,
          min: 50,
          max: 95,
          step: 5,
          disabled: !localSettings.budgetWarnings
        }
      ]
    },
    {
      title: 'Metas de Ahorro',
      icon: <Target className="w-5 h-5 text-blue-500" />,
      settings: [
        {
          key: 'goalProgress',
          label: 'Progreso de metas',
          description: 'Notificaciones sobre el progreso de tus metas de ahorro',
          type: 'toggle' as const
        },
        {
          key: 'goalProgressInterval',
          label: 'Frecuencia (días)',
          description: 'Cada cuántos días recibir recordatorios de metas',
          type: 'number' as const,
          min: 1,
          max: 30,
          step: 1,
          disabled: !localSettings.goalProgress
        }
      ]
    },
    {
      title: 'Logros y Recordatorios',
      icon: <Trophy className="w-5 h-5 text-yellow-500" />,
      settings: [
        {
          key: 'achievements',
          label: 'Logros',
          description: 'Notificaciones cuando alcances hitos importantes',
          type: 'toggle' as const
        },
        {
          key: 'reminders',
          label: 'Recordatorios',
          description: 'Recordatorios para registrar transacciones y revisar finanzas',
          type: 'toggle' as const
        }
      ]
    },
    {
      title: 'Canales de Notificación',
      icon: <Bell className="w-5 h-5 text-purple-500" />,
      settings: [
        {
          key: 'pushNotifications',
          label: 'Notificaciones del navegador',
          description: 'Recibir notificaciones push en el navegador',
          type: 'toggle' as const,
          action: !localSettings.pushNotifications ? handleRequestPermission : undefined,
          actionLabel: 'Habilitar',
          actionLoading: isRequestingPermission
        },
        {
          key: 'emailNotifications',
          label: 'Notificaciones por email',
          description: 'Recibir resúmenes y alertas por correo electrónico',
          type: 'toggle' as const,
          disabled: true,
          note: 'Próximamente disponible'
        }
      ]
    }
  ];

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className={`rounded-xl max-w-2xl w-full max-h-[90vh] overflow-hidden transition-colors duration-200 ${
        theme === 'dark' 
          ? 'bg-[#21222d] border border-[#2a2a3d]' 
          : 'bg-white border border-slate-200 shadow-xl'
      }`}>
        {/* Header */}
        <div className="flex justify-between items-center p-6 border-b border-gray-600">
          <div className="flex items-center gap-3">
            <Settings className="w-6 h-6 text-blue-500" />
            <h2 className={`text-xl font-bold transition-colors duration-200 ${
              theme === 'dark' ? 'text-white' : 'text-slate-900'
            }`}>
              Configuración de Notificaciones
            </h2>
          </div>
          
          <button
            onClick={onClose}
            className={`p-2 rounded-lg transition-colors duration-200 ${
              theme === 'dark'
                ? 'text-gray-400 hover:text-white hover:bg-gray-700'
                : 'text-slate-400 hover:text-slate-600 hover:bg-slate-100'
            }`}
          >
            ×
          </button>
        </div>

        {/* Settings Content */}
        <div className="overflow-y-auto max-h-[calc(90vh-200px)] p-6">
          <FadeIn>
            <div className="space-y-8">
              {settingsSections.map((section, sectionIndex) => (
                <div key={sectionIndex} className="space-y-4">
                  <div className="flex items-center gap-3">
                    {section.icon}
                    <h3 className={`text-lg font-semibold transition-colors duration-200 ${
                      theme === 'dark' ? 'text-white' : 'text-slate-900'
                    }`}>
                      {section.title}
                    </h3>
                  </div>
                  
                  <div className="space-y-4 ml-8">
                    {section.settings.map((setting) => (
                      <div key={setting.key} className={`p-4 rounded-lg transition-colors duration-200 ${
                        theme === 'dark' 
                          ? 'bg-[#2a2b38] border border-gray-600' 
                          : 'bg-slate-50 border border-slate-200'
                      } ${setting.disabled ? 'opacity-50' : ''}`}>
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="flex items-center gap-3">
                              <h4 className={`font-medium transition-colors duration-200 ${
                                theme === 'dark' ? 'text-white' : 'text-slate-900'
                              }`}>
                                {setting.label}
                              </h4>
                              
                              {setting.type === 'toggle' && (
                                <div className="flex items-center gap-2">
                                  <button
                                    onClick={() => {
                                      if (!setting.disabled) {
                                        setLocalSettings(prev => ({
                                          ...prev,
                                          [setting.key]: !prev[setting.key as keyof typeof prev]
                                        }));
                                      }
                                    }}
                                    disabled={setting.disabled}
                                    className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors duration-200 ${
                                      localSettings[setting.key as keyof typeof localSettings]
                                        ? 'bg-blue-500'
                                        : theme === 'dark' ? 'bg-gray-600' : 'bg-gray-300'
                                    } ${setting.disabled ? 'cursor-not-allowed' : 'cursor-pointer'}`}
                                  >
                                    <span
                                      className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform duration-200 ${
                                        localSettings[setting.key as keyof typeof localSettings]
                                          ? 'translate-x-6'
                                          : 'translate-x-1'
                                      }`}
                                    />
                                  </button>
                                  
                                  {setting.action && !localSettings[setting.key as keyof typeof localSettings] && (
                                    <button
                                      onClick={setting.action}
                                      disabled={setting.actionLoading}
                                      className="px-3 py-1 text-xs bg-blue-500 hover:bg-blue-600 text-white rounded-lg transition-colors duration-200 disabled:opacity-50"
                                    >
                                      {setting.actionLoading ? 'Habilitando...' : setting.actionLabel}
                                    </button>
                                  )}
                                </div>
                              )}
                            </div>
                            
                            <p className={`text-sm mt-1 transition-colors duration-200 ${
                              theme === 'dark' ? 'text-gray-400' : 'text-slate-600'
                            }`}>
                              {setting.description}
                            </p>
                            
                            {setting.note && (
                              <p className={`text-xs mt-1 italic transition-colors duration-200 ${
                                theme === 'dark' ? 'text-gray-500' : 'text-slate-400'
                              }`}>
                                {setting.note}
                              </p>
                            )}
                          </div>
                          
                          {setting.type === 'number' && (
                            <div className="ml-4">
                              <input
                                type="number"
                                min={setting.min}
                                max={setting.max}
                                step={setting.step}
                                value={localSettings[setting.key as keyof typeof localSettings] as number}
                                onChange={(e) => {
                                  const value = parseInt(e.target.value);
                                  if (!isNaN(value)) {
                                    setLocalSettings(prev => ({
                                      ...prev,
                                      [setting.key]: value
                                    }));
                                  }
                                }}
                                disabled={setting.disabled}
                                className={`w-20 px-2 py-1 text-sm rounded border transition-colors duration-200 ${
                                  theme === 'dark'
                                    ? 'bg-[#21222d] border-gray-600 text-white'
                                    : 'bg-white border-slate-300 text-slate-900'
                                } disabled:opacity-50`}
                              />
                            </div>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </FadeIn>
        </div>

        {/* Footer */}
        <div className="flex justify-end gap-3 p-6 border-t border-gray-600">
          <button
            onClick={onClose}
            className={`px-4 py-2 rounded-lg border transition-colors duration-200 ${
              theme === 'dark'
                ? 'border-gray-600 text-gray-300 hover:bg-gray-700'
                : 'border-slate-300 text-slate-700 hover:bg-slate-50'
            }`}
          >
            Cancelar
          </button>
          <button
            onClick={handleSave}
            className="px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg transition-colors duration-200"
          >
            Guardar Configuración
          </button>
        </div>
      </div>
    </div>
  );
}
