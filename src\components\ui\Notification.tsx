import React, { useState, useEffect } from 'react';
import { X, CheckCircle, AlertCircle, Info, AlertTriangle } from 'lucide-react';
import { useTheme } from '../../lib/ThemeContext';

export interface NotificationProps {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message?: string;
  duration?: number;
  onClose: (id: string) => void;
  action?: {
    label: string;
    onClick: () => void;
  };
}

export function Notification({ 
  id, 
  type, 
  title, 
  message, 
  duration = 5000, 
  onClose,
  action 
}: NotificationProps) {
  const { theme } = useTheme();
  const [isVisible, setIsVisible] = useState(true);
  const [isExiting, setIsExiting] = useState(false);

  useEffect(() => {
    if (duration > 0) {
      const timer = setTimeout(() => {
        handleClose();
      }, duration);

      return () => clearTimeout(timer);
    }
  }, [duration]);

  const handleClose = () => {
    setIsExiting(true);
    setTimeout(() => {
      setIsVisible(false);
      onClose(id);
    }, 300);
  };

  const getIcon = () => {
    switch (type) {
      case 'success':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'error':
        return <AlertCircle className="w-5 h-5 text-red-500" />;
      case 'warning':
        return <AlertTriangle className="w-5 h-5 text-yellow-500" />;
      case 'info':
        return <Info className="w-5 h-5 text-blue-500" />;
      default:
        return <Info className="w-5 h-5 text-blue-500" />;
    }
  };

  const getColors = () => {
    const baseClasses = theme === 'dark' 
      ? 'bg-[#21222d] border-[#2a2a3d] text-white' 
      : 'bg-white border-slate-200 text-slate-900';

    switch (type) {
      case 'success':
        return `${baseClasses} border-l-4 border-l-green-500`;
      case 'error':
        return `${baseClasses} border-l-4 border-l-red-500`;
      case 'warning':
        return `${baseClasses} border-l-4 border-l-yellow-500`;
      case 'info':
        return `${baseClasses} border-l-4 border-l-blue-500`;
      default:
        return baseClasses;
    }
  };

  if (!isVisible) return null;

  return (
    <div
      className={`
        fixed top-4 right-4 z-50 max-w-sm w-full
        ${isExiting ? 'animate-slideInRight' : 'animate-slideInLeft'}
        transition-all duration-300 ease-out
      `}
    >
      <div className={`
        rounded-lg border shadow-lg p-4
        ${getColors()}
        hover-lift
      `}>
        <div className="flex items-start space-x-3">
          <div className="flex-shrink-0 mt-0.5">
            {getIcon()}
          </div>
          
          <div className="flex-1 min-w-0">
            <h4 className="text-sm font-medium">{title}</h4>
            {message && (
              <p className={`text-sm mt-1 ${
                theme === 'dark' ? 'text-gray-300' : 'text-slate-600'
              }`}>
                {message}
              </p>
            )}
            
            {action && (
              <button
                onClick={action.onClick}
                className={`text-sm font-medium mt-2 transition-colors ${
                  type === 'success' ? 'text-green-600 hover:text-green-700' :
                  type === 'error' ? 'text-red-600 hover:text-red-700' :
                  type === 'warning' ? 'text-yellow-600 hover:text-yellow-700' :
                  'text-blue-600 hover:text-blue-700'
                }`}
              >
                {action.label}
              </button>
            )}
          </div>
          
          <button
            onClick={handleClose}
            className={`flex-shrink-0 p-1 rounded-md transition-colors ${
              theme === 'dark'
                ? 'text-gray-400 hover:text-gray-200 hover:bg-gray-700'
                : 'text-slate-400 hover:text-slate-600 hover:bg-slate-100'
            }`}
          >
            <X className="w-4 h-4" />
          </button>
        </div>
        
        {/* Progress bar for timed notifications */}
        {duration > 0 && (
          <div className={`mt-3 h-1 rounded-full overflow-hidden ${
            theme === 'dark' ? 'bg-gray-700' : 'bg-slate-200'
          }`}>
            <div
              className={`h-full transition-all ease-linear ${
                type === 'success' ? 'bg-green-500' :
                type === 'error' ? 'bg-red-500' :
                type === 'warning' ? 'bg-yellow-500' :
                'bg-blue-500'
              }`}
              style={{
                width: '100%',
                animation: `shrink ${duration}ms linear`
              }}
            />
          </div>
        )}
      </div>
    </div>
  );
}

// Notification Manager Component
interface NotificationManagerProps {
  notifications: NotificationProps[];
  onClose: (id: string) => void;
}

export function NotificationManager({ notifications, onClose }: NotificationManagerProps) {
  return (
    <div className="fixed top-4 right-4 z-50 space-y-2">
      {notifications.map((notification, index) => (
        <div
          key={notification.id}
          style={{ 
            transform: `translateY(${index * 10}px)`,
            zIndex: 50 - index 
          }}
        >
          <Notification {...notification} onClose={onClose} />
        </div>
      ))}
    </div>
  );
}

// Hook for managing notifications
export function useNotifications() {
  const [notifications, setNotifications] = useState<NotificationProps[]>([]);

  const addNotification = (notification: Omit<NotificationProps, 'id' | 'onClose'>) => {
    const id = Date.now().toString();
    const newNotification: NotificationProps = {
      ...notification,
      id,
      onClose: removeNotification
    };
    
    setNotifications(prev => [...prev, newNotification]);
    return id;
  };

  const removeNotification = (id: string) => {
    setNotifications(prev => prev.filter(n => n.id !== id));
  };

  const clearAll = () => {
    setNotifications([]);
  };

  // Convenience methods
  const success = (title: string, message?: string, options?: Partial<NotificationProps>) => {
    return addNotification({ type: 'success', title, message, ...options });
  };

  const error = (title: string, message?: string, options?: Partial<NotificationProps>) => {
    return addNotification({ type: 'error', title, message, ...options });
  };

  const warning = (title: string, message?: string, options?: Partial<NotificationProps>) => {
    return addNotification({ type: 'warning', title, message, ...options });
  };

  const info = (title: string, message?: string, options?: Partial<NotificationProps>) => {
    return addNotification({ type: 'info', title, message, ...options });
  };

  return {
    notifications,
    addNotification,
    removeNotification,
    clearAll,
    success,
    error,
    warning,
    info
  };
}

// CSS for progress bar animation (add to index.css)
const progressBarCSS = `
@keyframes shrink {
  from {
    width: 100%;
  }
  to {
    width: 0%;
  }
}
`;
