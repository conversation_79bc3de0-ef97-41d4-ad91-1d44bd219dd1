import { useState } from 'react';
import { X, Plus, DollarSign } from 'lucide-react';
import { usePatrimonio } from '../hooks/usePatrimonio';
import { LoadingSpinner } from './LoadingSpinner';

interface AddPatrimonioModalProps {
  type: 'asset' | 'liability';
  isOpen: boolean;
  onClose: () => void;
}

export function AddPatrimonioModal({ type, isOpen, onClose }: AddPatrimonioModalProps) {
  const { addAsset, addLiability } = usePatrimonio();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    value: '',
    category: ''
  });

  const assetCategories = [
    { value: 'liquid', label: '💰 Líquido (Efectivo, Cuentas)' },
    { value: 'investment', label: '📈 Inversiones (Acciones, Fondos)' },
    { value: 'real_estate', label: '🏠 Inmuebles (Casa, Terreno)' }
  ];

  const liabilityCategories = [
    { value: 'credit_card', label: '💳 Tarjetas de Crédito' },
    { value: 'loan', label: '🏦 Préstamos' },
    { value: 'mortgage', label: '🏠 Hipoteca' }
  ];

  const categories = type === 'asset' ? assetCategories : liabilityCategories;

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.name || !formData.value || !formData.category) {
      return;
    }

    const value = parseFloat(formData.value);
    if (isNaN(value) || value <= 0) {
      return;
    }

    setIsSubmitting(true);

    try {
      const result = type === 'asset' 
        ? await addAsset({
            name: formData.name,
            value,
            category: formData.category as any
          })
        : await addLiability({
            name: formData.name,
            value,
            category: formData.category as any
          });

      if (result.success) {
        setFormData({ name: '', value: '', category: '' });
        onClose();
      }
    } catch (error) {
      console.error('Error adding item:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-70 flex items-center justify-center z-50 p-4">
      <div className="bg-[#21222d] border border-[#2a2a3d] rounded-xl w-full max-w-md shadow-2xl">
        <div className="p-6">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-xl font-bold flex items-center">
              <Plus className="w-6 h-6 text-blue-400 mr-2" />
              Agregar {type === 'asset' ? 'Activo' : 'Pasivo'}
            </h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-white transition-colors"
            >
              <X className="w-5 h-5" />
            </button>
          </div>

          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Nombre
              </label>
              <input
                type="text"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                className="w-full bg-[#2a2b38] border border-gray-700 rounded-lg p-3 text-white placeholder-gray-400 focus:border-blue-500 focus:ring-1 focus:ring-blue-500"
                placeholder={type === 'asset' ? 'Ej. Cuenta de ahorros' : 'Ej. Tarjeta de crédito'}
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Categoría
              </label>
              <select
                value={formData.category}
                onChange={(e) => handleInputChange('category', e.target.value)}
                className="w-full bg-[#2a2b38] border border-gray-700 rounded-lg p-3 text-white focus:border-blue-500 focus:ring-1 focus:ring-blue-500"
                required
              >
                <option value="">Seleccionar categoría</option>
                {categories.map(cat => (
                  <option key={cat.value} value={cat.value}>
                    {cat.label}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Valor
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <DollarSign className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  type="number"
                  min="0"
                  step="0.01"
                  value={formData.value}
                  onChange={(e) => handleInputChange('value', e.target.value)}
                  className="w-full bg-[#2a2b38] border border-gray-700 rounded-lg p-3 pl-10 text-white placeholder-gray-400 focus:border-blue-500 focus:ring-1 focus:ring-blue-500"
                  placeholder="0.00"
                  required
                />
              </div>
            </div>

            <div className="flex gap-3 mt-6">
              <button
                type="button"
                onClick={onClose}
                className="flex-1 bg-gray-700 hover:bg-gray-600 text-white rounded-lg p-3 font-medium transition-colors"
              >
                Cancelar
              </button>
              <button
                type="submit"
                disabled={isSubmitting || !formData.name || !formData.value || !formData.category}
                className={`flex-1 text-white rounded-lg p-3 font-medium transition-colors ${
                  type === 'asset'
                    ? 'bg-green-600 hover:bg-green-500 disabled:bg-gray-500'
                    : 'bg-red-600 hover:bg-red-500 disabled:bg-gray-500'
                } disabled:cursor-not-allowed`}
              >
                {isSubmitting ? (
                  <div className="flex items-center justify-center">
                    <LoadingSpinner size="sm" />
                    <span className="ml-2">Guardando...</span>
                  </div>
                ) : (
                  `Agregar ${type === 'asset' ? 'Activo' : 'Pasivo'}`
                )}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}