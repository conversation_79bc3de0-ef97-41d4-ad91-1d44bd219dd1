import { useState } from 'react';

interface CardProps {
  children: React.ReactNode;
  className?: string;
}

export function Card({ children, className }: CardProps) {
  const [isHovered, setIsHovered] = useState(false);

  return (
    <div 
      className={`rounded-xl transition-all duration-300 bg-[#21222d] text-white border border-[#2a2a3d] ${
        isHovered ? 'shadow-lg transform scale-[1.01]' : ''
      } ${className}`}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {children}
    </div>
  );
}