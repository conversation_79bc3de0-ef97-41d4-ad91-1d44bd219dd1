import { useState } from 'react';
import { useTheme } from '../../lib/ThemeContext';

interface CardProps {
  children: React.ReactNode;
  className?: string;
}

export function Card({ children, className }: CardProps) {
  const [isHovered, setIsHovered] = useState(false);
  const { theme } = useTheme();

  return (
    <div
      className={`rounded-xl transition-all duration-300 ${
        theme === 'dark'
          ? 'bg-[#21222d] text-white border border-[#2a2a3d]'
          : 'bg-slate-50 text-slate-900 border border-slate-200 shadow-sm'
      } ${
        isHovered ? (theme === 'dark' ? 'shadow-lg transform scale-[1.01]' : 'shadow-md transform scale-[1.01] border-slate-300') : ''
      } ${className}`}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {children}
    </div>
  );
}