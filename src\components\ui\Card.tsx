import { useState } from 'react';
import { useTheme } from '../../lib/ThemeContext';

interface CardProps {
  children: React.ReactNode;
  className?: string;
}

export function Card({ children, className }: CardProps) {
  const [isHovered, setIsHovered] = useState(false);
  const { theme } = useTheme();

  return (
    <div
      className={`rounded-xl transition-all duration-300 ${
        theme === 'dark'
          ? 'bg-[#21222d] text-white border border-[#2a2a3d]'
          : 'bg-white text-gray-900 border border-gray-200 shadow-sm'
      } ${
        isHovered ? 'shadow-lg transform scale-[1.01]' : ''
      } ${className}`}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {children}
    </div>
  );
}