/*
  # Extend transactions table for planned expenses

  1. New Columns
    - `is_essential` (boolean) - Whether this expense is essential
    - `is_flexible` (boolean) - Whether this expense can be adjusted
    - `status` (text) - Status of the expense (pendiente, pagado, parcial, pospuesto, cancelado)
    - `is_planned` (boolean) - Whether this is a planned expense vs actual transaction
    - `priority_order` (integer) - Order for expense prioritization

  2. Updates
    - Add check constraint for status values
    - Add default values for new columns
    - Update existing transactions to have default values

  3. Security
    - RLS policies remain the same (already cover these new fields)
*/

-- Add new columns to transactions table
DO $$
BEGIN
  -- Add is_essential column if it doesn't exist
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns
    WHERE table_name = 'transactions' AND column_name = 'is_essential'
  ) THEN
    ALTER TABLE transactions ADD COLUMN is_essential boolean DEFAULT false;
  END IF;

  -- Add is_flexible column if it doesn't exist
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns
    WHERE table_name = 'transactions' AND column_name = 'is_flexible'
  ) THEN
    ALTER TABLE transactions ADD COLUMN is_flexible boolean DEFAULT true;
  END IF;

  -- Add status column if it doesn't exist
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns
    WHERE table_name = 'transactions' AND column_name = 'status'
  ) THEN
    ALTER TABLE transactions ADD COLUMN status text DEFAULT 'pendiente';
  END IF;

  -- Add is_planned column if it doesn't exist
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns
    WHERE table_name = 'transactions' AND column_name = 'is_planned'
  ) THEN
    ALTER TABLE transactions ADD COLUMN is_planned boolean DEFAULT false;
  END IF;

  -- Add priority_order column if it doesn't exist
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns
    WHERE table_name = 'transactions' AND column_name = 'priority_order'
  ) THEN
    ALTER TABLE transactions ADD COLUMN priority_order integer DEFAULT 0;
  END IF;
END $$;

-- Add check constraint for status values
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.check_constraints
    WHERE constraint_name = 'transactions_status_check'
  ) THEN
    ALTER TABLE transactions ADD CONSTRAINT transactions_status_check 
    CHECK (status IN ('pendiente', 'pagado', 'parcial', 'pospuesto', 'cancelado'));
  END IF;
END $$;

-- Create index for planned expenses queries
CREATE INDEX IF NOT EXISTS transactions_is_planned_idx ON transactions(is_planned, user_id);
CREATE INDEX IF NOT EXISTS transactions_priority_order_idx ON transactions(priority_order) WHERE is_planned = true;