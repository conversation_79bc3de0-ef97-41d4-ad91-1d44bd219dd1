import React, { useState, useMemo } from 'react';
import { Plus, Edit, Trash2, Play, Pause, Calendar, Repeat, Clock, AlertCircle } from 'lucide-react';
import { useTheme } from '../lib/ThemeContext';
import { useTransactionContext } from '../lib/TransactionContext';
import { useCustomCategories } from '../hooks/useCustomCategories';
import { useToast } from './Toast';
import { FadeIn, SlideIn } from './ui/LoadingStates';
import { InfoTooltip, HelpTooltip } from './ui/Tooltip';
import { format, addDays, addWeeks, addMonths, addYears, parseISO, isBefore, isAfter } from 'date-fns';
import { es } from 'date-fns/locale';

interface RecurringTransaction {
  id: string;
  description: string;
  amount: number;
  type: 'income' | 'expense';
  category: string;
  frequency: 'daily' | 'weekly' | 'biweekly' | 'monthly' | 'quarterly' | 'yearly';
  startDate: string;
  endDate?: string;
  nextDate: string;
  isActive: boolean;
  createdAt: string;
  lastExecuted?: string;
  executionCount: number;
  maxExecutions?: number;
}

const frequencyOptions = {
  daily: { label: 'Diario', icon: '📅' },
  weekly: { label: 'Semanal', icon: '📆' },
  biweekly: { label: 'Quincenal', icon: '🗓️' },
  monthly: { label: 'Mensual', icon: '📊' },
  quarterly: { label: 'Trimestral', icon: '📈' },
  yearly: { label: 'Anual', icon: '🎯' }
};

export function RecurringTransactionsPage() {
  const { theme } = useTheme();
  const { addTransaction } = useTransactionContext();
  const { getCategoriesForType, getCategoryInfo } = useCustomCategories();
  const { showSuccess, showError } = useToast();
  
  const [recurringTransactions, setRecurringTransactions] = useState<RecurringTransaction[]>(() => {
    const saved = localStorage.getItem('recurringTransactions');
    return saved ? JSON.parse(saved) : [];
  });
  
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingTransaction, setEditingTransaction] = useState<RecurringTransaction | null>(null);
  
  const [formData, setFormData] = useState({
    description: '',
    amount: '',
    type: 'expense' as 'income' | 'expense',
    category: '',
    frequency: 'monthly' as keyof typeof frequencyOptions,
    startDate: format(new Date(), 'yyyy-MM-dd'),
    endDate: '',
    maxExecutions: ''
  });

  // Save recurring transactions to localStorage
  const saveRecurringTransactions = (newTransactions: RecurringTransaction[]) => {
    localStorage.setItem('recurringTransactions', JSON.stringify(newTransactions));
    setRecurringTransactions(newTransactions);
  };

  // Calculate next execution date
  const calculateNextDate = (startDate: string, frequency: keyof typeof frequencyOptions, lastExecuted?: string): string => {
    const baseDate = lastExecuted ? parseISO(lastExecuted) : parseISO(startDate);
    
    switch (frequency) {
      case 'daily':
        return format(addDays(baseDate, 1), 'yyyy-MM-dd');
      case 'weekly':
        return format(addWeeks(baseDate, 1), 'yyyy-MM-dd');
      case 'biweekly':
        return format(addWeeks(baseDate, 2), 'yyyy-MM-dd');
      case 'monthly':
        return format(addMonths(baseDate, 1), 'yyyy-MM-dd');
      case 'quarterly':
        return format(addMonths(baseDate, 3), 'yyyy-MM-dd');
      case 'yearly':
        return format(addYears(baseDate, 1), 'yyyy-MM-dd');
      default:
        return format(addMonths(baseDate, 1), 'yyyy-MM-dd');
    }
  };

  // Check for due transactions and execute them
  const executeDueTransactions = () => {
    const today = format(new Date(), 'yyyy-MM-dd');
    let executedCount = 0;

    const updatedTransactions = recurringTransactions.map(rt => {
      if (!rt.isActive || isAfter(parseISO(rt.nextDate), new Date())) {
        return rt;
      }

      // Check if end date has passed
      if (rt.endDate && isAfter(new Date(), parseISO(rt.endDate))) {
        return { ...rt, isActive: false };
      }

      // Check if max executions reached
      if (rt.maxExecutions && rt.executionCount >= rt.maxExecutions) {
        return { ...rt, isActive: false };
      }

      // Execute the transaction
      const newTransaction = {
        id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
        description: `${rt.description} (Recurrente)`,
        amount: rt.amount,
        type: rt.type,
        category: rt.category,
        date: today,
        time: format(new Date(), 'HH:mm')
      };

      addTransaction(newTransaction);
      executedCount++;

      // Update recurring transaction
      const nextDate = calculateNextDate(rt.startDate, rt.frequency, today);
      return {
        ...rt,
        lastExecuted: today,
        nextDate,
        executionCount: rt.executionCount + 1
      };
    });

    if (executedCount > 0) {
      saveRecurringTransactions(updatedTransactions);
      showSuccess(
        'Transacciones ejecutadas',
        `Se ejecutaron ${executedCount} transacciones recurrentes`
      );
    }
  };

  // Execute due transactions on component mount
  React.useEffect(() => {
    executeDueTransactions();
  }, []);

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.description || !formData.amount || !formData.category) {
      showError('Error', 'Todos los campos requeridos deben completarse');
      return;
    }

    const amount = parseFloat(formData.amount);
    if (amount <= 0) {
      showError('Error', 'El monto debe ser mayor a 0');
      return;
    }

    const maxExecutions = formData.maxExecutions ? parseInt(formData.maxExecutions) : undefined;
    if (maxExecutions && maxExecutions <= 0) {
      showError('Error', 'El número máximo de ejecuciones debe ser mayor a 0');
      return;
    }

    if (editingTransaction) {
      // Update existing recurring transaction
      const updatedTransactions = recurringTransactions.map(rt =>
        rt.id === editingTransaction.id
          ? {
              ...rt,
              description: formData.description,
              amount,
              type: formData.type,
              category: formData.category,
              frequency: formData.frequency,
              startDate: formData.startDate,
              endDate: formData.endDate || undefined,
              maxExecutions,
              nextDate: calculateNextDate(formData.startDate, formData.frequency)
            }
          : rt
      );
      saveRecurringTransactions(updatedTransactions);
      showSuccess('Transacción actualizada', 'La transacción recurrente se actualizó correctamente');
      setEditingTransaction(null);
    } else {
      // Add new recurring transaction
      const newRecurringTransaction: RecurringTransaction = {
        id: Date.now().toString(),
        description: formData.description,
        amount,
        type: formData.type,
        category: formData.category,
        frequency: formData.frequency,
        startDate: formData.startDate,
        endDate: formData.endDate || undefined,
        nextDate: calculateNextDate(formData.startDate, formData.frequency),
        isActive: true,
        createdAt: new Date().toISOString(),
        executionCount: 0,
        maxExecutions
      };
      
      saveRecurringTransactions([...recurringTransactions, newRecurringTransaction]);
      showSuccess('Transacción creada', 'La nueva transacción recurrente se creó correctamente');
    }

    // Reset form
    setFormData({
      description: '',
      amount: '',
      type: 'expense',
      category: '',
      frequency: 'monthly',
      startDate: format(new Date(), 'yyyy-MM-dd'),
      endDate: '',
      maxExecutions: ''
    });
    setShowAddForm(false);
  };

  const handleEdit = (transaction: RecurringTransaction) => {
    setEditingTransaction(transaction);
    setFormData({
      description: transaction.description,
      amount: transaction.amount.toString(),
      type: transaction.type,
      category: transaction.category,
      frequency: transaction.frequency,
      startDate: transaction.startDate,
      endDate: transaction.endDate || '',
      maxExecutions: transaction.maxExecutions?.toString() || ''
    });
    setShowAddForm(true);
  };

  const handleDelete = (transactionId: string) => {
    if (window.confirm('¿Estás seguro de que quieres eliminar esta transacción recurrente?')) {
      const updatedTransactions = recurringTransactions.filter(rt => rt.id !== transactionId);
      saveRecurringTransactions(updatedTransactions);
      showSuccess('Transacción eliminada', 'La transacción recurrente se eliminó correctamente');
    }
  };

  const handleToggleActive = (transactionId: string) => {
    const updatedTransactions = recurringTransactions.map(rt =>
      rt.id === transactionId ? { ...rt, isActive: !rt.isActive } : rt
    );
    saveRecurringTransactions(updatedTransactions);
    showSuccess(
      'Estado actualizado',
      'El estado de la transacción recurrente se actualizó correctamente'
    );
  };

  const handleExecuteNow = (transaction: RecurringTransaction) => {
    const today = format(new Date(), 'yyyy-MM-dd');
    
    const newTransaction = {
      id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
      description: `${transaction.description} (Ejecutado manualmente)`,
      amount: transaction.amount,
      type: transaction.type,
      category: transaction.category,
      date: today,
      time: format(new Date(), 'HH:mm')
    };

    addTransaction(newTransaction);
    
    // Update recurring transaction
    const nextDate = calculateNextDate(transaction.startDate, transaction.frequency, today);
    const updatedTransactions = recurringTransactions.map(rt =>
      rt.id === transaction.id
        ? {
            ...rt,
            lastExecuted: today,
            nextDate,
            executionCount: rt.executionCount + 1
          }
        : rt
    );
    
    saveRecurringTransactions(updatedTransactions);
    showSuccess('Transacción ejecutada', 'La transacción se ejecutó y registró correctamente');
  };

  const handleCancel = () => {
    setShowAddForm(false);
    setEditingTransaction(null);
    setFormData({
      description: '',
      amount: '',
      type: 'expense',
      category: '',
      frequency: 'monthly',
      startDate: format(new Date(), 'yyyy-MM-dd'),
      endDate: '',
      maxExecutions: ''
    });
  };

  // Calculate statistics
  const stats = useMemo(() => {
    const active = recurringTransactions.filter(rt => rt.isActive).length;
    const inactive = recurringTransactions.length - active;
    const dueToday = recurringTransactions.filter(rt => 
      rt.isActive && rt.nextDate <= format(new Date(), 'yyyy-MM-dd')
    ).length;
    const totalExecutions = recurringTransactions.reduce((sum, rt) => sum + rt.executionCount, 0);

    return { active, inactive, dueToday, totalExecutions };
  }, [recurringTransactions]);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div className="flex items-center gap-3">
          <div>
            <h1 className={`text-3xl font-bold transition-colors duration-200 ${
              theme === 'dark' ? 'text-white' : 'text-slate-900'
            }`}>
              Transacciones Recurrentes
            </h1>
            <p className={`text-sm mt-1 transition-colors duration-200 ${
              theme === 'dark' ? 'text-gray-400' : 'text-slate-500'
            }`}>
              Automatiza tus ingresos y gastos regulares
            </p>
          </div>
          <HelpTooltip content="Las transacciones recurrentes te permiten automatizar el registro de ingresos y gastos que ocurren regularmente, como salarios, alquileres o suscripciones." />
        </div>
        
        <div className="flex gap-3">
          <button
            onClick={executeDueTransactions}
            className="px-4 py-2 bg-green-500 hover:bg-green-600 text-white rounded-lg transition-colors duration-200 flex items-center gap-2"
          >
            <Play className="w-4 h-4" />
            Ejecutar Pendientes
          </button>
          
          <button
            onClick={() => setShowAddForm(true)}
            className="px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg transition-colors duration-200 flex items-center gap-2"
          >
            <Plus className="w-4 h-4" />
            Nueva Recurrente
          </button>
        </div>
      </div>

      {/* Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <InfoTooltip title="Activas" description="Número de transacciones recurrentes activas">
          <div className={`rounded-xl p-4 transition-colors duration-200 ${
            theme === 'dark' 
              ? 'bg-[#21222d] border border-[#2a2a3d]' 
              : 'bg-slate-50 border border-slate-200'
          }`}>
            <div className="flex items-center">
              <div className="p-2 bg-green-500 rounded-lg">
                <Repeat className="w-5 h-5 text-white" />
              </div>
              <div className="ml-3">
                <p className={`text-sm transition-colors duration-200 ${
                  theme === 'dark' ? 'text-gray-400' : 'text-slate-500'
                }`}>
                  Activas
                </p>
                <p className="text-lg font-bold text-green-500">
                  {stats.active}
                </p>
              </div>
            </div>
          </div>
        </InfoTooltip>

        <div className={`rounded-xl p-4 transition-colors duration-200 ${
          theme === 'dark' 
            ? 'bg-[#21222d] border border-[#2a2a3d]' 
            : 'bg-slate-50 border border-slate-200'
        }`}>
          <div className="flex items-center">
            <div className="p-2 bg-orange-500 rounded-lg">
              <AlertCircle className="w-5 h-5 text-white" />
            </div>
            <div className="ml-3">
              <p className={`text-sm transition-colors duration-200 ${
                theme === 'dark' ? 'text-gray-400' : 'text-slate-500'
              }`}>
                Pendientes Hoy
              </p>
              <p className="text-lg font-bold text-orange-500">
                {stats.dueToday}
              </p>
            </div>
          </div>
        </div>

        <div className={`rounded-xl p-4 transition-colors duration-200 ${
          theme === 'dark' 
            ? 'bg-[#21222d] border border-[#2a2a3d]' 
            : 'bg-slate-50 border border-slate-200'
        }`}>
          <div className="flex items-center">
            <div className="p-2 bg-gray-500 rounded-lg">
              <Pause className="w-5 h-5 text-white" />
            </div>
            <div className="ml-3">
              <p className={`text-sm transition-colors duration-200 ${
                theme === 'dark' ? 'text-gray-400' : 'text-slate-500'
              }`}>
                Inactivas
              </p>
              <p className="text-lg font-bold text-gray-500">
                {stats.inactive}
              </p>
            </div>
          </div>
        </div>

        <div className={`rounded-xl p-4 transition-colors duration-200 ${
          theme === 'dark' 
            ? 'bg-[#21222d] border border-[#2a2a3d]' 
            : 'bg-slate-50 border border-slate-200'
        }`}>
          <div className="flex items-center">
            <div className="p-2 bg-blue-500 rounded-lg">
              <Clock className="w-5 h-5 text-white" />
            </div>
            <div className="ml-3">
              <p className={`text-sm transition-colors duration-200 ${
                theme === 'dark' ? 'text-gray-400' : 'text-slate-500'
              }`}>
                Total Ejecutadas
              </p>
              <p className="text-lg font-bold text-blue-500">
                {stats.totalExecutions}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Add/Edit Form */}
      {showAddForm && (
        <SlideIn direction="down">
          <div className={`rounded-xl p-6 transition-colors duration-200 ${
            theme === 'dark'
              ? 'bg-[#21222d] border border-[#2a2a3d]'
              : 'bg-slate-50 border border-slate-200'
          }`}>
            <h3 className={`text-lg font-semibold mb-4 transition-colors duration-200 ${
              theme === 'dark' ? 'text-white' : 'text-slate-900'
            }`}>
              {editingTransaction ? 'Editar Transacción Recurrente' : 'Nueva Transacción Recurrente'}
            </h3>

            <form onSubmit={handleSubmit} className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {/* Description */}
              <div className="md:col-span-3">
                <label className={`block text-sm font-medium mb-2 transition-colors duration-200 ${
                  theme === 'dark' ? 'text-gray-300' : 'text-slate-700'
                }`}>
                  Descripción *
                </label>
                <input
                  type="text"
                  value={formData.description}
                  onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                  className={`w-full px-3 py-2 rounded-lg border transition-colors duration-200 ${
                    theme === 'dark'
                      ? 'bg-[#2a2b38] border-gray-600 text-white placeholder-gray-400'
                      : 'bg-white border-slate-300 text-slate-900 placeholder-slate-400'
                  }`}
                  placeholder="Ej: Salario mensual, Alquiler, Netflix"
                  required
                />
              </div>

              {/* Type */}
              <div>
                <label className={`block text-sm font-medium mb-2 transition-colors duration-200 ${
                  theme === 'dark' ? 'text-gray-300' : 'text-slate-700'
                }`}>
                  Tipo *
                </label>
                <select
                  value={formData.type}
                  onChange={(e) => setFormData(prev => ({ ...prev, type: e.target.value as 'income' | 'expense', category: '' }))}
                  className={`w-full px-3 py-2 rounded-lg border transition-colors duration-200 ${
                    theme === 'dark'
                      ? 'bg-[#2a2b38] border-gray-600 text-white'
                      : 'bg-white border-slate-300 text-slate-900'
                  }`}
                  required
                >
                  <option value="expense">Gasto</option>
                  <option value="income">Ingreso</option>
                </select>
              </div>

              {/* Amount */}
              <div>
                <label className={`block text-sm font-medium mb-2 transition-colors duration-200 ${
                  theme === 'dark' ? 'text-gray-300' : 'text-slate-700'
                }`}>
                  Monto ($) *
                </label>
                <input
                  type="number"
                  step="0.01"
                  value={formData.amount}
                  onChange={(e) => setFormData(prev => ({ ...prev, amount: e.target.value }))}
                  className={`w-full px-3 py-2 rounded-lg border transition-colors duration-200 ${
                    theme === 'dark'
                      ? 'bg-[#2a2b38] border-gray-600 text-white placeholder-gray-400'
                      : 'bg-white border-slate-300 text-slate-900 placeholder-slate-400'
                  }`}
                  placeholder="0.00"
                  required
                />
              </div>

              {/* Category */}
              <div>
                <label className={`block text-sm font-medium mb-2 transition-colors duration-200 ${
                  theme === 'dark' ? 'text-gray-300' : 'text-slate-700'
                }`}>
                  Categoría *
                </label>
                <select
                  value={formData.category}
                  onChange={(e) => setFormData(prev => ({ ...prev, category: e.target.value }))}
                  className={`w-full px-3 py-2 rounded-lg border transition-colors duration-200 ${
                    theme === 'dark'
                      ? 'bg-[#2a2b38] border-gray-600 text-white'
                      : 'bg-white border-slate-300 text-slate-900'
                  }`}
                  required
                >
                  <option value="">Seleccionar categoría</option>
                  {getCategoriesForType(formData.type).map(cat => (
                    <option key={cat.key} value={cat.key}>
                      {cat.icon} {cat.name}
                    </option>
                  ))}
                </select>
              </div>

              {/* Form Actions */}
              <div className="md:col-span-3 flex gap-3 pt-4">
                <button
                  type="button"
                  onClick={handleCancel}
                  className={`flex-1 py-2 px-4 rounded-lg border transition-colors duration-200 ${
                    theme === 'dark'
                      ? 'border-gray-600 text-gray-300 hover:bg-gray-700'
                      : 'border-slate-300 text-slate-700 hover:bg-slate-50'
                  }`}
                >
                  Cancelar
                </button>
                <button
                  type="submit"
                  className="flex-1 py-2 px-4 rounded-lg bg-blue-500 hover:bg-blue-600 text-white transition-colors duration-200"
                >
                  {editingTransaction ? 'Actualizar' : 'Crear'} Transacción
                </button>
              </div>
            </form>
          </div>
        </SlideIn>
      )}

      {/* Recurring Transactions List */}
      <div className={`rounded-xl transition-colors duration-200 ${
        theme === 'dark'
          ? 'bg-[#21222d] border border-[#2a2a3d]'
          : 'bg-slate-50 border border-slate-200'
      }`}>
        <div className="p-4 border-b border-gray-600">
          <h2 className={`text-lg font-semibold transition-colors duration-200 ${
            theme === 'dark' ? 'text-white' : 'text-slate-900'
          }`}>
            Transacciones Recurrentes ({recurringTransactions.length})
          </h2>
        </div>

        {recurringTransactions.length === 0 ? (
          <FadeIn>
            <div className={`text-center py-12 transition-colors duration-200 ${
              theme === 'dark' ? 'text-gray-500' : 'text-slate-500'
            }`}>
              <Repeat className="w-12 h-12 mx-auto mb-4 opacity-50" />
              <p className="font-medium">No tienes transacciones recurrentes</p>
              <p className="text-sm mt-1">Crea tu primera transacción recurrente para automatizar tus finanzas</p>
            </div>
          </FadeIn>
        ) : (
          <div className="divide-y divide-gray-600">
            {recurringTransactions.map((transaction, index) => {
              const categoryInfo = getCategoryInfo(transaction.category);
              const isDue = transaction.nextDate <= format(new Date(), 'yyyy-MM-dd');

              return (
                <SlideIn key={transaction.id} direction="up" delay={index * 100}>
                  <div className={`p-4 transition-colors duration-200 ${
                    theme === 'dark' ? 'hover:bg-[#2a2b38]' : 'hover:bg-slate-100'
                  } ${!transaction.isActive ? 'opacity-60' : ''}`}>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-4">
                        <div className={`w-10 h-10 rounded-lg flex items-center justify-center text-white font-medium ${
                          categoryInfo?.color ? '' : 'bg-gray-500'
                        }`} style={categoryInfo?.color ? { backgroundColor: categoryInfo.color } : {}}>
                          {categoryInfo?.icon || '📊'}
                        </div>
                        <div>
                          <h3 className={`font-medium transition-colors duration-200 ${
                            theme === 'dark' ? 'text-white' : 'text-slate-900'
                          }`}>
                            {transaction.description}
                          </h3>
                          <div className="flex items-center gap-4 text-sm">
                            <span className={`transition-colors duration-200 ${
                              theme === 'dark' ? 'text-gray-400' : 'text-slate-500'
                            }`}>
                              {frequencyOptions[transaction.frequency].icon} {frequencyOptions[transaction.frequency].label}
                            </span>
                            <span className={`flex items-center gap-1 ${
                              !transaction.isActive ? 'text-gray-500' :
                              isDue ? 'text-orange-500' : 'text-blue-500'
                            }`}>
                              {!transaction.isActive ? (
                                <>
                                  <Pause className="w-3 h-3" />
                                  Pausada
                                </>
                              ) : isDue ? (
                                <>
                                  <AlertCircle className="w-3 h-3" />
                                  Pendiente
                                </>
                              ) : (
                                <>
                                  <Clock className="w-3 h-3" />
                                  Próxima: {format(parseISO(transaction.nextDate), 'dd/MM', { locale: es })}
                                </>
                              )}
                            </span>
                          </div>
                        </div>
                      </div>

                      <div className="flex items-center gap-4">
                        <div className="text-right">
                          <p className={`text-lg font-bold ${
                            transaction.type === 'income' ? 'text-green-500' : 'text-red-500'
                          }`}>
                            {transaction.type === 'income' ? '+' : '-'}${transaction.amount.toLocaleString()}
                          </p>
                          <p className={`text-sm transition-colors duration-200 ${
                            theme === 'dark' ? 'text-gray-400' : 'text-slate-500'
                          }`}>
                            {transaction.executionCount} ejecuciones
                          </p>
                        </div>

                        <div className="flex items-center gap-1">
                          {transaction.isActive && isDue && (
                            <button
                              onClick={() => handleExecuteNow(transaction)}
                              className={`p-1 rounded transition-colors duration-200 ${
                                theme === 'dark'
                                  ? 'text-gray-400 hover:text-green-400 hover:bg-green-900/20'
                                  : 'text-slate-400 hover:text-green-600 hover:bg-green-50'
                              }`}
                              title="Ejecutar ahora"
                            >
                              <Play className="w-4 h-4" />
                            </button>
                          )}

                          <button
                            onClick={() => handleToggleActive(transaction.id)}
                            className={`p-1 rounded transition-colors duration-200 ${
                              theme === 'dark'
                                ? 'text-gray-400 hover:text-blue-400 hover:bg-blue-900/20'
                                : 'text-slate-400 hover:text-blue-600 hover:bg-blue-50'
                            }`}
                            title={transaction.isActive ? 'Pausar' : 'Activar'}
                          >
                            {transaction.isActive ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
                          </button>

                          <button
                            onClick={() => handleEdit(transaction)}
                            className={`p-1 rounded transition-colors duration-200 ${
                              theme === 'dark'
                                ? 'text-gray-400 hover:text-blue-400 hover:bg-blue-900/20'
                                : 'text-slate-400 hover:text-blue-600 hover:bg-blue-50'
                            }`}
                            title="Editar"
                          >
                            <Edit className="w-4 h-4" />
                          </button>

                          <button
                            onClick={() => handleDelete(transaction.id)}
                            className={`p-1 rounded transition-colors duration-200 ${
                              theme === 'dark'
                                ? 'text-gray-400 hover:text-red-400 hover:bg-red-900/20'
                                : 'text-slate-400 hover:text-red-600 hover:bg-red-50'
                            }`}
                            title="Eliminar"
                          >
                            <Trash2 className="w-4 h-4" />
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </SlideIn>
              );
            })}
          </div>
        )}
      </div>
    </div>
  );
}
