import { useState, useEffect, useCallback } from 'react';
import { useTransactionContext } from '../lib/TransactionContext';
import { supabase } from '../lib/supabaseClient';

interface BackupData {
  transactions: any[];
  budgets: any[];
  goals: any[];
  categories: any[];
  settings: any;
  timestamp: string;
  version: string;
}

interface BackupState {
  isBackingUp: boolean;
  lastBackupTime: string | null;
  backupError: string | null;
  autoBackupEnabled: boolean;
  backupFrequency: 'daily' | 'weekly' | 'monthly';
}

export function useAutoBackup() {
  const { transactions } = useTransactionContext();
  const [state, setState] = useState<BackupState>({
    isBackingUp: false,
    lastBackupTime: null,
    backupError: null,
    autoBackupEnabled: true,
    backupFrequency: 'weekly'
  });

  // Load backup settings from localStorage
  useEffect(() => {
    const settings = localStorage.getItem('backupSettings');
    if (settings) {
      const parsed = JSON.parse(settings);
      setState(prev => ({ ...prev, ...parsed }));
    }

    const lastBackup = localStorage.getItem('lastBackupTime');
    if (lastBackup) {
      setState(prev => ({ ...prev, lastBackupTime: lastBackup }));
    }
  }, []);

  // Save backup settings to localStorage
  const saveSettings = useCallback((newSettings: Partial<BackupState>) => {
    const updatedState = { ...state, ...newSettings };
    setState(updatedState);
    
    localStorage.setItem('backupSettings', JSON.stringify({
      autoBackupEnabled: updatedState.autoBackupEnabled,
      backupFrequency: updatedState.backupFrequency
    }));
  }, [state]);

  // Create backup data
  const createBackupData = useCallback(async (): Promise<BackupData> => {
    // Get all data from localStorage and context
    const budgets = JSON.parse(localStorage.getItem('budgets') || '[]');
    const goals = JSON.parse(localStorage.getItem('savingsGoals') || '[]');
    const categories = JSON.parse(localStorage.getItem('customCategories') || '[]');
    const settings = {
      theme: localStorage.getItem('theme'),
      currency: localStorage.getItem('currency'),
      language: localStorage.getItem('language'),
      notifications: JSON.parse(localStorage.getItem('notificationSettings') || '{}')
    };

    return {
      transactions,
      budgets,
      goals,
      categories,
      settings,
      timestamp: new Date().toISOString(),
      version: '1.0.0'
    };
  }, [transactions]);

  // Perform backup to Supabase
  const performBackup = useCallback(async (manual = false): Promise<boolean> => {
    if (state.isBackingUp) return false;

    setState(prev => ({ ...prev, isBackingUp: true, backupError: null }));

    try {
      const backupData = await createBackupData();
      
      // Get current user
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        throw new Error('Usuario no autenticado');
      }

      // Save backup to Supabase
      const { error } = await supabase
        .from('user_backups')
        .upsert({
          user_id: user.id,
          backup_data: backupData,
          backup_type: manual ? 'manual' : 'automatic',
          created_at: new Date().toISOString()
        });

      if (error) throw error;

      // Update last backup time
      const now = new Date().toISOString();
      localStorage.setItem('lastBackupTime', now);
      setState(prev => ({ 
        ...prev, 
        lastBackupTime: now,
        isBackingUp: false,
        backupError: null
      }));

      console.log('Backup completed successfully');
      return true;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Error desconocido';
      setState(prev => ({ 
        ...prev, 
        isBackingUp: false,
        backupError: errorMessage
      }));
      console.error('Backup failed:', error);
      return false;
    }
  }, [state.isBackingUp, createBackupData]);

  // Restore from backup
  const restoreFromBackup = useCallback(async (backupId?: string): Promise<boolean> => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        throw new Error('Usuario no autenticado');
      }

      let query = supabase
        .from('user_backups')
        .select('*')
        .eq('user_id', user.id);

      if (backupId) {
        query = query.eq('id', backupId);
      } else {
        query = query.order('created_at', { ascending: false }).limit(1);
      }

      const { data, error } = await query;
      if (error) throw error;
      if (!data || data.length === 0) {
        throw new Error('No se encontraron backups');
      }

      const backup = data[0];
      const backupData: BackupData = backup.backup_data;

      // Restore data to localStorage and context
      localStorage.setItem('budgets', JSON.stringify(backupData.budgets));
      localStorage.setItem('savingsGoals', JSON.stringify(backupData.goals));
      localStorage.setItem('customCategories', JSON.stringify(backupData.categories));
      
      // Restore settings
      if (backupData.settings.theme) {
        localStorage.setItem('theme', backupData.settings.theme);
      }
      if (backupData.settings.currency) {
        localStorage.setItem('currency', backupData.settings.currency);
      }
      if (backupData.settings.language) {
        localStorage.setItem('language', backupData.settings.language);
      }
      if (backupData.settings.notifications) {
        localStorage.setItem('notificationSettings', JSON.stringify(backupData.settings.notifications));
      }

      // Note: Transactions would need to be restored through the TransactionContext
      // This would require additional implementation in the context

      console.log('Restore completed successfully');
      return true;

    } catch (error) {
      console.error('Restore failed:', error);
      return false;
    }
  }, []);

  // Get list of available backups
  const getBackupHistory = useCallback(async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return [];

      const { data, error } = await supabase
        .from('user_backups')
        .select('id, backup_type, created_at')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false })
        .limit(10);

      if (error) throw error;
      return data || [];

    } catch (error) {
      console.error('Failed to get backup history:', error);
      return [];
    }
  }, []);

  // Check if backup is needed
  const isBackupNeeded = useCallback((): boolean => {
    if (!state.autoBackupEnabled || !state.lastBackupTime) return true;

    const lastBackup = new Date(state.lastBackupTime);
    const now = new Date();
    const diffHours = (now.getTime() - lastBackup.getTime()) / (1000 * 60 * 60);

    switch (state.backupFrequency) {
      case 'daily':
        return diffHours >= 24;
      case 'weekly':
        return diffHours >= 24 * 7;
      case 'monthly':
        return diffHours >= 24 * 30;
      default:
        return false;
    }
  }, [state.autoBackupEnabled, state.lastBackupTime, state.backupFrequency]);

  // Auto backup check
  useEffect(() => {
    if (!state.autoBackupEnabled) return;

    const checkAndBackup = async () => {
      if (isBackupNeeded() && !state.isBackingUp) {
        await performBackup(false);
      }
    };

    // Check on mount
    checkAndBackup();

    // Set up interval to check periodically
    const interval = setInterval(checkAndBackup, 60 * 60 * 1000); // Check every hour

    return () => clearInterval(interval);
  }, [state.autoBackupEnabled, isBackupNeeded, performBackup, state.isBackingUp]);

  // Manual backup
  const manualBackup = useCallback(() => {
    return performBackup(true);
  }, [performBackup]);

  // Toggle auto backup
  const toggleAutoBackup = useCallback((enabled: boolean) => {
    saveSettings({ autoBackupEnabled: enabled });
  }, [saveSettings]);

  // Change backup frequency
  const setBackupFrequency = useCallback((frequency: 'daily' | 'weekly' | 'monthly') => {
    saveSettings({ backupFrequency: frequency });
  }, [saveSettings]);

  // Get backup status
  const getBackupStatus = useCallback(() => {
    const lastBackupDate = state.lastBackupTime ? new Date(state.lastBackupTime) : null;
    const needsBackup = isBackupNeeded();
    
    return {
      isBackingUp: state.isBackingUp,
      lastBackupDate,
      needsBackup,
      autoBackupEnabled: state.autoBackupEnabled,
      backupFrequency: state.backupFrequency,
      backupError: state.backupError
    };
  }, [state, isBackupNeeded]);

  // Clear backup error
  const clearBackupError = useCallback(() => {
    setState(prev => ({ ...prev, backupError: null }));
  }, []);

  return {
    ...state,
    manualBackup,
    restoreFromBackup,
    getBackupHistory,
    toggleAutoBackup,
    setBackupFrequency,
    getBackupStatus,
    clearBackupError,
    isBackupNeeded: isBackupNeeded()
  };
}
