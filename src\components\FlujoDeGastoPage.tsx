import { useState, useRef, useEffect } from 'react';
import { Card } from './ui/Card';
import { Wallet, ArrowDown, PlusCircle, ShoppingCart, Calendar, CreditCard, Home, Smartphone, Wifi, GraduationCap, Heart, PlayCircle, BookOpen, Sailboat, Filter, ArrowUpDown, ChevronUp, ChevronDown, X, Star, Banknote, PiggyBank, Info } from 'lucide-react';
import { InfoTooltip } from './ui/Tooltip';
import { useTransactionContext } from '../lib/TransactionContext';
import { LoadingSpinner } from './LoadingSpinner';

// Import the extended transaction type
import { PlannedExpense } from '../hooks/useTransactions';

export function FlujoDeGastoPage() {
  const { 
    plannedExpenses, 
    loading, 
    error, 
    addPlannedExpense, 
    updatePlannedExpense, 
    deletePlannedExpense,
    convertToRealTransaction 
  } = useTransactionContext();
  
  const [income, setIncome] = useState(5000);
  const [newExpenseName, setNewExpenseName] = useState('');
  const [newExpenseAmount, setNewExpenseAmount] = useState('');
  const [newExpenseIsEssential, setNewExpenseIsEssential] = useState(false);
  const [newExpenseIsFlexible, setNewExpenseIsFlexible] = useState(true);
  const [selectedIcon, setSelectedIcon] = useState<string>('ShoppingCart');
  const [isAddingExpense, setIsAddingExpense] = useState(false);
  const [filterBy, setFilterBy] = useState<'todos' | 'esenciales' | 'flexibles' | 'pendientes'>('todos');
  const [isFilterMenuOpen, setIsFilterMenuOpen] = useState(false);
  const [savingSuggestion, setSavingSuggestion] = useState(false);
  
  const icons = {
    ShoppingCart: <ShoppingCart className="w-5 h-5" />,
    Calendar: <Calendar className="w-5 h-5" />,
    CreditCard: <CreditCard className="w-5 h-5" />,
    Home: <Home className="w-5 h-5" />,
    Smartphone: <Smartphone className="w-5 h-5" />,
    Wifi: <Wifi className="w-5 h-5" />,
    GraduationCap: <GraduationCap className="w-5 h-5" />,
    Heart: <Heart className="w-5 h-5" />,
    PlayCircle: <PlayCircle className="w-5 h-5" />,
    BookOpen: <BookOpen className="w-5 h-5" />,
    Sailboat: <Sailboat className="w-5 h-5" />,
    Banknote: <Banknote className="w-5 h-5" />,
    PiggyBank: <PiggyBank className="w-5 h-5" />
  };
  
  const statusOptions = [
    { value: 'pendiente', label: 'Pendiente', color: 'text-gray-300' },
    { value: 'pagado', label: 'Pagado', color: 'text-green-400' },
    { value: 'parcial', label: 'Pago Parcial', color: 'text-yellow-400' },
    { value: 'pospuesto', label: 'Pospuesto', color: 'text-blue-400' },
    { value: 'cancelado', label: 'Cancelado', color: 'text-red-400' }
  ];
  
  const draggedItem = useRef<number | null>(null);
  const filterMenuRef = useRef<HTMLDivElement>(null);

  // Convert icon string back to JSX for display
  const getIconComponent = (iconString: string) => {
    const iconMap = {
      '🎓': <GraduationCap className="w-5 h-5" />,
      '❤️': <Heart className="w-5 h-5" />,
      '📱': <Smartphone className="w-5 h-5" />,
      '📶': <Wifi className="w-5 h-5" />,
      '🛒': <ShoppingCart className="w-5 h-5" />,
      '▶️': <PlayCircle className="w-5 h-5" />,
      '🏠': <Home className="w-5 h-5" />,
    };
    return iconMap[iconString] || <ShoppingCart className="w-5 h-5" />;
  };

  // Calculate remaining balance after each expense
  const calculateRunningBalance = () => {
    let balance = income;
    return plannedExpenses.map(expense => {
      if (expense.status !== 'cancelado') {
        if (expense.status === 'parcial') {
          balance -= expense.amount * 0.5; // Assume 50% paid for partial
        } else if (expense.status !== 'pagado') {
          balance -= expense.amount;
        }
      }
      return balance;
    });
  };

  // Calculate totals
  const totalExpenses = plannedExpenses
    .filter(e => e.status !== 'cancelado')
    .reduce((sum, expense) => {
      if (expense.status === 'parcial') {
        return sum + (expense.amount * 0.5);
      } else if (expense.status !== 'pagado') {
        return sum + expense.amount;
      }
      return sum;
    }, 0);
    
  const totalEssentialExpenses = plannedExpenses
    .filter(e => e.is_essential && e.status !== 'cancelado' && e.status !== 'pagado')
    .reduce((sum, expense) => sum + expense.amount, 0);
    
  const totalFlexibleExpenses = plannedExpenses
    .filter(e => e.is_flexible && e.status !== 'cancelado' && e.status !== 'pagado')
    .reduce((sum, expense) => sum + expense.amount, 0);

  const runningBalances = calculateRunningBalance();
  const remainingBalance = runningBalances[runningBalances.length - 1] || income;
  
  const savingsAmount = Math.round(remainingBalance * 0.8);
  
  // Close filter menu when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (filterMenuRef.current && !filterMenuRef.current.contains(event.target as Node)) {
        setIsFilterMenuOpen(false);
      }
    }
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  // Show saving suggestion when there's excess money
  useEffect(() => {
    setSavingSuggestion(remainingBalance > income * 0.15);
  }, [income, remainingBalance]);

  const handleDragStart = (index: number) => {
    draggedItem.current = index;
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>, index: number) => {
    e.preventDefault();
    // Note: Drag and drop reordering will need to update priority_order in database
    // For now, we'll disable reordering until we implement the full logic
    return;
  };

  const handleDragEnd = () => {
    draggedItem.current = null;
  };

  const handleAddExpense = async () => {
    if (newExpenseName && newExpenseAmount && parseFloat(newExpenseAmount) > 0) {
      const maxPriorityOrder = Math.max(0, ...plannedExpenses.map(e => e.priority_order || 0));
      
      const newExpense = {
        description: newExpenseName,
        date: new Date().toISOString().split('T')[0],
        type: 'expense' as const,
        amount: parseFloat(newExpenseAmount),
        category: 'otros',
        icon: icons[selectedIcon as keyof typeof icons] ? selectedIcon : '🛒',
        is_essential: newExpenseIsEssential,
        is_flexible: newExpenseIsFlexible,
        status: 'pendiente' as const,
        is_planned: true,
        priority_order: maxPriorityOrder + 1
      };
      
      const result = await addPlannedExpense(newExpense);
      
      if (result.success) {
        setNewExpenseName('');
        setNewExpenseAmount('');
        setNewExpenseIsEssential(false);
        setNewExpenseIsFlexible(true);
        setIsAddingExpense(false);
      }
    }
  };

  const handleTogglePaid = async (id: string) => {
    const expense = plannedExpenses.find(e => e.id === id);
    if (!expense) return;
    
    const newStatus = expense.status === 'pagado' ? 'pendiente' : 'pagado';
    
    if (newStatus === 'pagado') {
      // Convert to real transaction
      await convertToRealTransaction(id);
    } else {
      // Update status back to pending
      await updatePlannedExpense(id, { status: newStatus });
    }
  };

  const handleStatusChange = async (id: string, status: 'pendiente' | 'pagado' | 'parcial' | 'pospuesto' | 'cancelado') => {
    if (status === 'pagado') {
      await convertToRealTransaction(id);
    } else {
      await updatePlannedExpense(id, { status });
    }
  };

  const handleDeleteExpense = async (id: string) => {
    await deletePlannedExpense(id);
  };

  const handleMoveExpense = (index: number, direction: 'up' | 'down') => {
    if ((direction === 'up' && index === 0) || (direction === 'down' && index === plannedExpenses.length - 1)) {
      return;
    }
    
    // For now, we'll implement a simple priority order update
    // In a full implementation, you'd want to batch update multiple items
    const currentExpense = plannedExpenses[index];
    const targetExpense = plannedExpenses[direction === 'up' ? index - 1 : index + 1];
    
    if (currentExpense && targetExpense) {
      const currentOrder = currentExpense.priority_order || 0;
      const targetOrder = targetExpense.priority_order || 0;
      
      updatePlannedExpense(currentExpense.id, { priority_order: targetOrder });
      updatePlannedExpense(targetExpense.id, { priority_order: currentOrder });
    }
  };

  // Filter expenses based on current filter
  const filteredExpenses = plannedExpenses.filter(expense => {
    if (filterBy === 'todos') return true;
    if (filterBy === 'esenciales') return expense.is_essential;
    if (filterBy === 'flexibles') return expense.is_flexible;
    if (filterBy === 'pendientes') return expense.status === 'pendiente';
    return true;
  });

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <LoadingSpinner size="lg" text="Cargando flujo de gastos..." />
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-900/30 border border-red-800 text-red-300 p-6 rounded-lg">
        <p className="font-medium">Error al cargar gastos planificados:</p>
        <p className="text-sm">{error}</p>
      </div>
    );
  }

  return (
    <div className="space-y-8 pb-20">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6">
        <div className="flex items-center gap-2">
          <h1 className="text-3xl font-bold text-white">Flujo de Gastos</h1>
          <InfoTooltip
            title="Prioriza tus Gastos"
            description="Aquí puedes organizar tus gastos por orden de prioridad. Arrastra para reordenar y asegúrate de cubrir primero lo más importante con tu ingreso disponible."
          >
            <Info className="w-5 h-5 text-gray-400 hover:text-white cursor-help" />
          </InfoTooltip>
        </div>
      </div>
      
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <Card className="lg:col-span-1">
          <div className="p-6">
            <h2 className="text-xl font-semibold mb-4">Ingreso Disponible del Mes</h2>
            
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <div className="flex items-center gap-2">
                  <div className="w-10 h-10 rounded-full bg-green-500/20 flex items-center justify-center">
                    <Wallet className="w-5 h-5 text-green-500" />
                  </div>
                  <span className="font-medium text-base">Ingreso Total:</span>
                </div>
                <div className="flex items-center gap-1">
                  <span className="text-lg font-semibold text-green-500">${income.toLocaleString()}</span>
                  <button
                    onClick={() => {
                      const newIncome = prompt('Ingrese nuevo monto de ingreso:', income.toString());
                      if (newIncome && !isNaN(parseFloat(newIncome))) {
                        setIncome(parseFloat(newIncome));
                      }
                    }}
                    className="text-gray-400 hover:text-white"
                  >
                    ✏️
                  </button>
                </div>
              </div>
              
              <div className="flex justify-between items-center">
                <div className="flex items-center gap-2">
                  <div className="w-10 h-10 rounded-full bg-red-500/20 flex items-center justify-center">
                    <Star className="w-5 h-5 text-yellow-500" />
                  </div>
                  <span className="font-medium text-base">Gastos Esenciales:</span>
                </div>
                <span className="text-lg font-semibold text-yellow-500">${totalEssentialExpenses.toLocaleString()}</span>
              </div>
              
              <div className="flex justify-between items-center">
                <div className="flex items-center gap-2">
                  <div className="w-10 h-10 rounded-full bg-blue-500/20 flex items-center justify-center">
                    <PlayCircle className="w-5 h-5 text-blue-400" />
                  </div>
                  <span className="font-medium text-base">Gastos Flexibles:</span>
                </div>
                <span className="text-lg font-semibold text-blue-400">${totalFlexibleExpenses.toLocaleString()}</span>
              </div>
              
              <div className="flex justify-between items-center">
                <div className="flex items-center gap-2">
                  <div className="w-10 h-10 rounded-full bg-red-500/20 flex items-center justify-center">
                    <ShoppingCart className="w-5 h-5 text-red-500" />
                  </div>
                  <span className="font-medium text-base">Total Comprometido:</span>
                </div>
                <span className="text-lg font-semibold text-red-500">${totalExpenses.toLocaleString()}</span>
              </div>
              
              <div className="pt-4 border-t border-gray-700">
                <div className="flex justify-between items-center">
                  <span className="font-medium text-base">Remanente Disponible:</span>
                  <span className={`text-lg font-bold ${remainingBalance >= 0 ? 'text-blue-400' : 'text-red-500'}`}>
                    ${remainingBalance.toLocaleString()}
                  </span>
                </div>
              </div>
              
              <div className="pt-4">
                <div className="w-full bg-gray-700 h-4 rounded-full overflow-hidden">
                  {income > 0 && (
                    <div 
                      className="h-full bg-gradient-to-r from-green-500 to-blue-500"
                      style={{ 
                        width: `${Math.min(100, 100 - (remainingBalance / income) * 100)}%`,
                        transition: 'width 0.5s ease-in-out'
                      }}
                    ></div>
                  )}
                </div>
                <div className="flex justify-between mt-1 text-xs text-gray-400">
                  <span>0%</span>
                  <span>Asignado: {income > 0 ? ((totalExpenses / income) * 100).toFixed(0) : 0}%</span>
                  <span>100%</span>
                </div>
              </div>
              
              {remainingBalance < 0 && (
                <div className="mt-4 p-3 rounded-lg bg-red-900/30 border border-red-800 text-red-300">
                  <p className="text-sm font-medium">⚠️ ¡Alerta! Tus gastos superan tus ingresos por ${Math.abs(remainingBalance).toLocaleString()}.</p>
                  <p className="text-sm mt-1">Revisa tus gastos flexibles para reducir o posponer algunos.</p>
                </div>
              )}
              
              {remainingBalance > 0 && remainingBalance < income * 0.1 && (
                <div className="mt-4 p-3 rounded-lg bg-yellow-900/30 border border-yellow-800 text-yellow-300">
                  <p className="text-sm font-medium">⚠️ Tu remanente disponible es menor al 10% de tus ingresos.</p>
                  <p className="text-sm mt-1">Considera revisar tus gastos flexibles.</p>
                </div>
              )}
              
              {savingSuggestion && (
                <div className="mt-4 p-3 rounded-lg bg-blue-900/30 border border-blue-800 text-blue-300">
                  <p className="text-sm font-medium">💡 Tienes ${remainingBalance.toLocaleString()} sin asignar.</p>
                  <p className="text-sm mt-1">Recomendación: Destina ${savingsAmount.toLocaleString()} a tu ahorro o inversión.</p>
                  <button 
                    className="mt-2 px-3 py-1 bg-blue-800 hover:bg-blue-700 rounded-lg text-sm font-medium transition-colors"
                    onClick={() => {
                      setExpenses([
                        ...expenses, 
                        {
                          id: Date.now().toString(),
                          name: "Ahorro Mensual",
                          amount: savingsAmount,
                          icon: icons.PiggyBank,
                          isPaid: false,
                          isEssential: false,
                          isFlexible: false,
                          status: 'pendiente'
                        }
                      ]);
                      setSavingSuggestion(false);
                    }}
                  >
                    Agregar Ahorro
                  </button>
                </div>
              )}
            </div>
          </div>
        </Card>
        
        <Card className="lg:col-span-2">
          <div className="p-6">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-semibold">Priorización de Gastos</h2>
              <div className="flex items-center gap-2">
                <div className="relative" ref={filterMenuRef}>
                  <button 
                    onClick={() => setIsFilterMenuOpen(!isFilterMenuOpen)}
                    className="flex items-center gap-1 text-gray-300 hover:text-white transition-colors text-sm font-medium border border-gray-700 px-3 py-1.5 rounded-lg bg-[#2a2b38]"
                  >
                    <Filter className="w-4 h-4" />
                    <span>
                      {filterBy === 'todos' && 'Todos'}
                      {filterBy === 'esenciales' && 'Esenciales'}
                      {filterBy === 'flexibles' && 'Flexibles'}
                      {filterBy === 'pendientes' && 'Pendientes'}
                    </span>
                  </button>
                  
                  {isFilterMenuOpen && (
                    <div className="absolute right-0 mt-2 w-48 bg-[#2a2b38] rounded-lg shadow-xl z-10 p-2 animate-fadeIn">
                      <button
                        className={`w-full text-left px-3 py-2 rounded-md text-sm ${filterBy === 'todos' ? 'bg-blue-500 text-white' : 'text-gray-300 hover:bg-[#32334a]'}`}
                        onClick={() => {
                          setFilterBy('todos');
                          setIsFilterMenuOpen(false);
                        }}
                      >
                        Mostrar Todos
                      </button>
                      <button
                        className={`w-full text-left px-3 py-2 rounded-md text-sm ${filterBy === 'esenciales' ? 'bg-blue-500 text-white' : 'text-gray-300 hover:bg-[#32334a]'}`}
                        onClick={() => {
                          setFilterBy('esenciales');
                          setIsFilterMenuOpen(false);
                        }}
                      >
                        Solo Esenciales
                      </button>
                      <button
                        className={`w-full text-left px-3 py-2 rounded-md text-sm ${filterBy === 'flexibles' ? 'bg-blue-500 text-white' : 'text-gray-300 hover:bg-[#32334a]'}`}
                        onClick={() => {
                          setFilterBy('flexibles');
                          setIsFilterMenuOpen(false);
                        }}
                      >
                        Solo Flexibles
                      </button>
                      <button
                        className={`w-full text-left px-3 py-2 rounded-md text-sm ${filterBy === 'pendientes' ? 'bg-blue-500 text-white' : 'text-gray-300 hover:bg-[#32334a]'}`}
                        onClick={() => {
                          setFilterBy('pendientes');
                          setIsFilterMenuOpen(false);
                        }}
                      >
                        Solo Pendientes
                      </button>
                    </div>
                  )}
                </div>
                
                <button 
                  onClick={() => setIsAddingExpense(true)}
                  className="flex items-center gap-1 text-blue-400 hover:text-blue-300 transition-colors text-sm font-medium"
                >
                  <PlusCircle className="w-4 h-4" />
                  Agregar Gasto
                </button>
              </div>
            </div>
            
            {isAddingExpense && (
              <div className="mb-6 p-4 bg-[#2a2b38] rounded-xl animate-fadeIn">
                <div className="flex justify-between items-center mb-3">
                  <h3 className="font-medium">Nuevo Gasto</h3>
                  <button 
                    onClick={() => setIsAddingExpense(false)}
                    className="text-gray-500 hover:text-white"
                  >
                    <X className="w-4 h-4" />
                  </button>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-3">
                  <div>
                    <label className="block text-sm font-medium text-gray-400 mb-1">Concepto</label>
                    <input
                      type="text"
                      value={newExpenseName}
                      onChange={(e) => setNewExpenseName(e.target.value)}
                      className="w-full bg-[#21222d] border border-gray-700 rounded-lg p-2 text-white"
                      placeholder="Ej. Netflix"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-400 mb-1">Monto Estimado</label>
                    <input
                      type="number"
                      value={newExpenseAmount}
                      onChange={(e) => setNewExpenseAmount(e.target.value)}
                      className="w-full bg-[#21222d] border border-gray-700 rounded-lg p-2 text-white"
                      placeholder="Ej. 15.99"
                    />
                  </div>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-3">
                  <div>
                    <label className="flex items-center text-sm font-medium text-gray-400 mb-1">
                      <input
                        type="checkbox"
                        checked={newExpenseIsEssential}
                        onChange={() => setNewExpenseIsEssential(!newExpenseIsEssential)}
                        className="mr-2 rounded bg-[#21222d] border-gray-700 text-blue-500 focus:ring-blue-500"
                      />
                      ¿Es un gasto esencial?
                    </label>
                    <p className="text-xs text-gray-500">Los gastos esenciales son aquellos necesarios para vivir o compromisos fijos.</p>
                  </div>
                  
                  <div>
                    <label className="flex items-center text-sm font-medium text-gray-400 mb-1">
                      <input
                        type="checkbox"
                        checked={newExpenseIsFlexible}
                        onChange={() => setNewExpenseIsFlexible(!newExpenseIsFlexible)}
                        className="mr-2 rounded bg-[#21222d] border-gray-700 text-blue-500 focus:ring-blue-500"
                      />
                      ¿Es un gasto flexible?
                    </label>
                    <p className="text-xs text-gray-500">Los gastos flexibles son aquellos que puedes ajustar o posponer si es necesario.</p>
                  </div>
                </div>
                
                <div className="mb-3">
                  <label className="block text-sm font-medium text-gray-400 mb-1">Icono</label>
                  <div className="flex flex-wrap gap-2">
                    {Object.entries(icons).map(([key, icon]) => (
                      <button
                        key={key}
                        onClick={() => setSelectedIcon(key)}
                        className={`w-10 h-10 rounded-full flex items-center justify-center ${
                          selectedIcon === key 
                            ? 'bg-blue-500 text-white' 
                            : 'bg-[#21222d] text-gray-400 hover:bg-gray-700'
                        }`}
                      >
                        {icon}
                      </button>
                    ))}
                  </div>
                </div>
                
                <div className="flex justify-end gap-2">
                  <button
                    onClick={() => setIsAddingExpense(false)}
                    className="px-4 py-2 text-gray-300 bg-gray-700 hover:bg-gray-600 rounded-lg"
                  >
                    Cancelar
                  </button>
                  <button
                    onClick={handleAddExpense}
                    className="px-4 py-2 text-white bg-blue-600 hover:bg-blue-500 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed"
                    disabled={!newExpenseName || !newExpenseAmount || parseFloat(newExpenseAmount) <= 0}
                  >
                    Guardar
                  </button>
                </div>
              </div>
            )}
            
            <div className="overflow-hidden rounded-xl bg-[#1e1f2c] border border-gray-700">
              <div className="grid grid-cols-12 bg-[#2a2b38] p-3 border-b border-gray-700 text-sm font-medium text-gray-400">
                <div className="col-span-1 md:col-span-1">#</div>
                <div className="col-span-3 md:col-span-3">Concepto</div>
                <div className="col-span-2 md:col-span-1 text-center hidden md:block">Esencial</div>
                <div className="col-span-2 md:col-span-1 text-center hidden md:block">Flexible</div>
                <div className="col-span-2 md:col-span-1 text-right">Monto</div>
                <div className="col-span-3 md:col-span-2 text-right">Saldo</div>
                <div className="col-span-3 md:col-span-2 text-center">Estado</div>
                <div className="col-span-1 md:col-span-1 text-center">Acciones</div>
              </div>
              
              <div className="max-h-[600px] overflow-y-auto">
                {filteredExpenses.length > 0 ? (
                  filteredExpenses.map((expense, index) => {
                    // Find the real index from the original array
                    const realIndex = plannedExpenses.findIndex(e => e.id === expense.id);
                    return (
                    <div
                      key={expense.id}
                      draggable
                      onDragStart={() => handleDragStart(realIndex)}
                      onDragOver={(e) => handleDragOver(e, realIndex)}
                      onDragEnd={handleDragEnd}
                      className={`grid grid-cols-12 p-3 border-b border-gray-700 hover:bg-[#2a2b38] transition-colors ${
                        expense.status === 'pagado' || expense.status === 'cancelado' ? 'opacity-50' : ''
                      }`}
                    >
                      <div className="col-span-1 md:col-span-1 flex items-center">
                        <div className="bg-gray-700 w-6 h-6 rounded-md flex items-center justify-center text-xs">
                          {realIndex + 1}
                        </div>
                      </div>
                      <div className="col-span-3 md:col-span-3 flex items-center gap-2">
                        <div className="w-8 h-8 rounded-full bg-[#2e3259] flex items-center justify-center text-blue-300">
                          {getIconComponent(expense.icon || '🛒')}
                        </div>
                        <span className={expense.status === 'pagado' || expense.status === 'cancelado' ? 'line-through' : 'font-medium'}>
                          {expense.description}
                        </span>
                      </div>
                      <div className="col-span-2 md:col-span-1 text-center hidden md:flex items-center justify-center">
                        <span className={`rounded-full w-6 h-6 flex items-center justify-center ${
                          expense.is_essential 
                            ? 'bg-yellow-500/30 text-yellow-500' 
                            : 'bg-gray-700 text-gray-500'
                        }`}>
                          {expense.is_essential ? '✓' : ''}
                        </span>
                      </div>
                      <div className="col-span-2 md:col-span-1 text-center hidden md:flex items-center justify-center">
                        <span className={`rounded-full w-6 h-6 flex items-center justify-center ${
                          expense.is_flexible 
                            ? 'bg-blue-500/30 text-blue-500' 
                            : 'bg-gray-700 text-gray-500'
                        }`}>
                          {expense.is_flexible ? '✓' : ''}
                        </span>
                      </div>
                      <div className="col-span-2 md:col-span-1 text-right self-center font-medium text-red-400">
                        ${expense.amount.toLocaleString()}
                      </div>
                      <div className="col-span-3 md:col-span-2 text-right self-center">
                        <span className={`font-medium ${
                          runningBalances[realIndex] >= 0 ? 'text-blue-400' : 'text-red-500'
                        }`}>
                          ${runningBalances[realIndex].toLocaleString()}
                        </span>
                        <div className="text-xs text-gray-500">
                          {((expense.amount / income) * 100).toFixed(0)}% del ingreso
                        </div>
                      </div>
                      <div className="col-span-3 md:col-span-2 text-center flex items-center justify-center">
                        <select
                          value={expense.status}
                          onChange={(e) => handleStatusChange(expense.id, e.target.value as any)}
                          className={`bg-[#21222d] border border-gray-700 rounded-lg p-1 text-xs font-medium ${
                            statusOptions.find(o => o.value === expense.status)?.color || 'text-gray-300'
                          }`}
                        >
                          {statusOptions.map(option => (
                            <option key={option.value} value={option.value} className="text-white">
                              {option.label}
                            </option>
                          ))}
                        </select>
                      </div>
                      <div className="col-span-1 md:col-span-1 flex items-center justify-center gap-1">
                        <div className="flex flex-col md:flex-row items-center">
                          <button
                            onClick={() => handleMoveExpense(realIndex, 'up')}
                            disabled={realIndex === 0}
                            className={`w-6 h-6 rounded-md flex items-center justify-center ${
                              realIndex === 0 ? 'text-gray-700' : 'text-gray-400 hover:bg-gray-700'
                            }`}
                            title="Subir prioridad"
                          >
                            <ChevronUp className="w-4 h-4" />
                          </button>
                          
                          <button
                            onClick={() => handleMoveExpense(realIndex, 'down')}
                            disabled={realIndex === plannedExpenses.length - 1}
                            className={`w-6 h-6 rounded-md flex items-center justify-center ${
                              realIndex === plannedExpenses.length - 1 ? 'text-gray-700' : 'text-gray-400 hover:bg-gray-700'
                            }`}
                            title="Bajar prioridad"
                          >
                            <ChevronDown className="w-4 h-4" />
                          </button>
                          
                          <button
                            onClick={() => handleDeleteExpense(expense.id)}
                            className="w-6 h-6 rounded-md flex items-center justify-center text-gray-400 hover:bg-red-900/50 hover:text-red-300"
                            title="Eliminar"
                          >
                            <X className="w-4 h-4" />
                          </button>
                        </div>
                      </div>
                      
                      {index < filteredExpenses.length - 1 && (
                        <div className="col-span-12 flex justify-center text-gray-600 my-1">
                          <ArrowDown className="w-4 h-4" />
                        </div>
                      )}
                    </div>
                  )})
                ) : (
                  <div className="p-8 text-center text-gray-500">
                    <ShoppingCart className="w-12 h-12 mx-auto mb-4 opacity-30" />
                    <p className="font-medium">No hay gastos planificados</p>
                    <p className="text-sm mt-1">Agrega tu primer gasto planificado para comenzar</p>
                  </div>
                )}
              </div>
            </div>
            
            <div className="mt-4 p-3 rounded-lg bg-[#2a2b38] text-gray-300 text-sm">
              <div className="flex flex-wrap gap-2 md:gap-4">
                <div className="flex items-center gap-1">
                  <ArrowUpDown className="w-4 h-4 text-blue-400" />
                  <p><strong>Tip:</strong> Arrastra para reordenar</p>
                </div>
                <div className="flex items-center gap-1">
                  <div className="w-4 h-4 rounded-full bg-yellow-500/30 flex items-center justify-center text-yellow-500 text-xs">✓</div>
                  <p>Gasto esencial</p>
                </div>
                <div className="flex items-center gap-1">
                  <div className="w-4 h-4 rounded-full bg-blue-500/30 flex items-center justify-center text-blue-500 text-xs">✓</div>
                  <p>Gasto flexible</p>
                </div>
              </div>
              <div className="mt-2 text-xs text-gray-400">
                <p>💡 <strong>Tip:</strong> Cuando marques un gasto como "pagado", se convertirá automáticamente en una transacción real.</p>
              </div>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
}