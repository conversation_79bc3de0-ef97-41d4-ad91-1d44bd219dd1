import React, { useState, useRef } from 'react';
import { X, Upload, Download, FileText, AlertCircle, CheckCircle } from 'lucide-react';
import { useTheme } from '../lib/ThemeContext';
import { useTransactionContext } from '../lib/TransactionContext';
import { Transaction } from '../lib/types';
import { useToast } from './Toast';
import { format } from 'date-fns';

interface ImportExportModalProps {
  isOpen: boolean;
  onClose: () => void;
}

interface ImportResult {
  success: number;
  errors: string[];
  total: number;
}

export function ImportExportModal({ isOpen, onClose }: ImportExportModalProps) {
  const { theme } = useTheme();
  const { transactions, addTransaction } = useTransactionContext();
  const { showSuccess, showError } = useToast();
  
  const [activeTab, setActiveTab] = useState<'import' | 'export'>('export');
  const [isProcessing, setIsProcessing] = useState(false);
  const [importResult, setImportResult] = useState<ImportResult | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Export transactions to CSV
  const handleExport = () => {
    try {
      if (transactions.length === 0) {
        showError('Sin datos', 'No hay transacciones para exportar');
        return;
      }

      // Create CSV content
      const headers = ['Fecha', 'Descripción', 'Tipo', 'Categoría', 'Monto', 'Icono'];
      const csvContent = [
        headers.join(','),
        ...transactions.map(t => [
          t.date,
          `"${t.description.replace(/"/g, '""')}"`, // Escape quotes
          t.type === 'income' ? 'Ingreso' : 'Gasto',
          t.category,
          t.amount,
          t.icon || ''
        ].join(','))
      ].join('\n');

      // Create and download file
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', `transacciones_${format(new Date(), 'yyyy-MM-dd')}.csv`);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      showSuccess('Exportación exitosa', `Se exportaron ${transactions.length} transacciones`);
    } catch (error) {
      console.error('Error exporting transactions:', error);
      showError('Error de exportación', 'No se pudo exportar el archivo');
    }
  };

  // Parse CSV content
  const parseCSV = (content: string): any[] => {
    const lines = content.split('\n').filter(line => line.trim());
    if (lines.length < 2) return [];

    const headers = lines[0].split(',').map(h => h.trim().toLowerCase());
    const data = [];

    for (let i = 1; i < lines.length; i++) {
      const values = [];
      let current = '';
      let inQuotes = false;

      // Parse CSV line handling quoted values
      for (let j = 0; j < lines[i].length; j++) {
        const char = lines[i][j];
        if (char === '"') {
          inQuotes = !inQuotes;
        } else if (char === ',' && !inQuotes) {
          values.push(current.trim());
          current = '';
        } else {
          current += char;
        }
      }
      values.push(current.trim());

      if (values.length >= 5) {
        const row: any = {};
        headers.forEach((header, index) => {
          if (values[index] !== undefined) {
            row[header] = values[index].replace(/^"|"$/g, ''); // Remove quotes
          }
        });
        data.push(row);
      }
    }

    return data;
  };

  // Import transactions from CSV
  const handleImport = async (file: File) => {
    setIsProcessing(true);
    setImportResult(null);

    try {
      const content = await file.text();
      const data = parseCSV(content);
      
      if (data.length === 0) {
        showError('Archivo vacío', 'El archivo no contiene datos válidos');
        setIsProcessing(false);
        return;
      }

      const result: ImportResult = {
        success: 0,
        errors: [],
        total: data.length
      };

      // Process each row
      for (let i = 0; i < data.length; i++) {
        const row = data[i];
        
        try {
          // Map CSV columns to transaction fields
          const transaction = {
            description: row.descripción || row.description || `Transacción importada ${i + 1}`,
            date: row.fecha || row.date || new Date().toISOString().split('T')[0],
            type: (row.tipo || row.type || '').toLowerCase().includes('ingreso') || 
                  (row.tipo || row.type || '').toLowerCase().includes('income') ? 'income' : 'expense',
            amount: parseFloat((row.monto || row.amount || '0').toString().replace(/[,$]/g, '')),
            category: row.categoría || row.category || 'otros',
            icon: row.icono || row.icon || '💸'
          };

          // Validate transaction
          if (!transaction.description || transaction.amount <= 0) {
            result.errors.push(`Fila ${i + 2}: Datos inválidos (descripción o monto)`);
            continue;
          }

          // Add transaction
          const addResult = await addTransaction(transaction);
          if (addResult.success) {
            result.success++;
          } else {
            result.errors.push(`Fila ${i + 2}: ${addResult.error || 'Error desconocido'}`);
          }
        } catch (error) {
          result.errors.push(`Fila ${i + 2}: Error de formato`);
        }
      }

      setImportResult(result);
      
      if (result.success > 0) {
        showSuccess('Importación completada', `Se importaron ${result.success} de ${result.total} transacciones`);
      } else {
        showError('Importación fallida', 'No se pudo importar ninguna transacción');
      }
    } catch (error) {
      console.error('Error importing transactions:', error);
      showError('Error de importación', 'No se pudo procesar el archivo');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      if (file.type !== 'text/csv' && !file.name.endsWith('.csv')) {
        showError('Formato inválido', 'Por favor selecciona un archivo CSV');
        return;
      }
      handleImport(file);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className={`rounded-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto transition-colors duration-200 ${
        theme === 'dark' 
          ? 'bg-[#21222d] border border-[#2a2a3d]' 
          : 'bg-white border border-slate-200 shadow-xl'
      }`}>
        {/* Header */}
        <div className="flex justify-between items-center p-6 border-b border-gray-600">
          <h2 className={`text-xl font-bold transition-colors duration-200 ${
            theme === 'dark' ? 'text-white' : 'text-slate-900'
          }`}>
            Importar/Exportar Transacciones
          </h2>
          <button
            onClick={onClose}
            className={`p-1 rounded-lg transition-colors duration-200 ${
              theme === 'dark'
                ? 'text-gray-400 hover:text-white hover:bg-gray-700'
                : 'text-slate-400 hover:text-slate-600 hover:bg-slate-100'
            }`}
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Tabs */}
        <div className="flex border-b border-gray-600">
          <button
            onClick={() => setActiveTab('export')}
            className={`flex-1 py-3 px-4 text-sm font-medium transition-colors duration-200 ${
              activeTab === 'export'
                ? theme === 'dark'
                  ? 'text-blue-400 border-b-2 border-blue-400 bg-blue-900/20'
                  : 'text-blue-600 border-b-2 border-blue-600 bg-blue-50'
                : theme === 'dark'
                  ? 'text-gray-400 hover:text-gray-300'
                  : 'text-slate-500 hover:text-slate-700'
            }`}
          >
            <Download className="w-4 h-4 inline mr-2" />
            Exportar
          </button>
          <button
            onClick={() => setActiveTab('import')}
            className={`flex-1 py-3 px-4 text-sm font-medium transition-colors duration-200 ${
              activeTab === 'import'
                ? theme === 'dark'
                  ? 'text-blue-400 border-b-2 border-blue-400 bg-blue-900/20'
                  : 'text-blue-600 border-b-2 border-blue-600 bg-blue-50'
                : theme === 'dark'
                  ? 'text-gray-400 hover:text-gray-300'
                  : 'text-slate-500 hover:text-slate-700'
            }`}
          >
            <Upload className="w-4 h-4 inline mr-2" />
            Importar
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          {activeTab === 'export' ? (
            <div className="space-y-4">
              <div className={`p-4 rounded-lg border transition-colors duration-200 ${
                theme === 'dark' 
                  ? 'bg-[#2a2b38] border-gray-600' 
                  : 'bg-slate-50 border-slate-200'
              }`}>
                <h3 className={`text-lg font-semibold mb-2 transition-colors duration-200 ${
                  theme === 'dark' ? 'text-white' : 'text-slate-900'
                }`}>
                  Exportar a CSV
                </h3>
                <p className={`text-sm mb-4 transition-colors duration-200 ${
                  theme === 'dark' ? 'text-gray-400' : 'text-slate-600'
                }`}>
                  Descarga todas tus transacciones en formato CSV para usar en Excel, Google Sheets u otras aplicaciones.
                </p>
                <div className={`text-sm mb-4 transition-colors duration-200 ${
                  theme === 'dark' ? 'text-gray-300' : 'text-slate-700'
                }`}>
                  <strong>Transacciones disponibles:</strong> {transactions.length}
                </div>
                <button
                  onClick={handleExport}
                  disabled={transactions.length === 0}
                  className={`w-full py-3 px-4 rounded-lg transition-colors duration-200 flex items-center justify-center gap-2 ${
                    transactions.length === 0
                      ? 'bg-gray-500 cursor-not-allowed'
                      : 'bg-green-500 hover:bg-green-600'
                  } text-white`}
                >
                  <Download className="w-4 h-4" />
                  Exportar Transacciones
                </button>
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              <div className={`p-4 rounded-lg border transition-colors duration-200 ${
                theme === 'dark' 
                  ? 'bg-[#2a2b38] border-gray-600' 
                  : 'bg-slate-50 border-slate-200'
              }`}>
                <h3 className={`text-lg font-semibold mb-2 transition-colors duration-200 ${
                  theme === 'dark' ? 'text-white' : 'text-slate-900'
                }`}>
                  Importar desde CSV
                </h3>
                <p className={`text-sm mb-4 transition-colors duration-200 ${
                  theme === 'dark' ? 'text-gray-400' : 'text-slate-600'
                }`}>
                  Sube un archivo CSV con tus transacciones. El archivo debe tener las columnas: Fecha, Descripción, Tipo, Categoría, Monto.
                </p>

                <input
                  ref={fileInputRef}
                  type="file"
                  accept=".csv"
                  onChange={handleFileSelect}
                  className="hidden"
                />

                <button
                  onClick={() => fileInputRef.current?.click()}
                  disabled={isProcessing}
                  className={`w-full py-3 px-4 rounded-lg border-2 border-dashed transition-colors duration-200 flex items-center justify-center gap-2 ${
                    isProcessing
                      ? 'border-gray-500 text-gray-500 cursor-not-allowed'
                      : theme === 'dark'
                        ? 'border-gray-600 text-gray-400 hover:border-blue-500 hover:text-blue-400'
                        : 'border-slate-300 text-slate-600 hover:border-blue-500 hover:text-blue-600'
                  }`}
                >
                  {isProcessing ? (
                    <>
                      <div className="w-4 h-4 border-2 border-gray-500 border-t-transparent rounded-full animate-spin"></div>
                      Procesando...
                    </>
                  ) : (
                    <>
                      <Upload className="w-4 h-4" />
                      Seleccionar archivo CSV
                    </>
                  )}
                </button>

                {/* Import Result */}
                {importResult && (
                  <div className="mt-4 space-y-2">
                    <div className={`p-3 rounded-lg flex items-center gap-2 ${
                      importResult.success > 0 
                        ? 'bg-green-900/30 text-green-400' 
                        : 'bg-red-900/30 text-red-400'
                    }`}>
                      {importResult.success > 0 ? (
                        <CheckCircle className="w-4 h-4" />
                      ) : (
                        <AlertCircle className="w-4 h-4" />
                      )}
                      <span className="text-sm">
                        {importResult.success} de {importResult.total} transacciones importadas exitosamente
                      </span>
                    </div>

                    {importResult.errors.length > 0 && (
                      <div className={`p-3 rounded-lg transition-colors duration-200 ${
                        theme === 'dark' 
                          ? 'bg-red-900/20 border border-red-800' 
                          : 'bg-red-50 border border-red-200'
                      }`}>
                        <h4 className="text-sm font-medium text-red-400 mb-2">
                          Errores encontrados:
                        </h4>
                        <div className="max-h-32 overflow-y-auto">
                          {importResult.errors.slice(0, 10).map((error, index) => (
                            <p key={index} className="text-xs text-red-400 mb-1">
                              {error}
                            </p>
                          ))}
                          {importResult.errors.length > 10 && (
                            <p className="text-xs text-red-400">
                              ... y {importResult.errors.length - 10} errores más
                            </p>
                          )}
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </div>

              {/* Format Example */}
              <div className={`p-4 rounded-lg border transition-colors duration-200 ${
                theme === 'dark' 
                  ? 'bg-[#2a2b38] border-gray-600' 
                  : 'bg-slate-50 border-slate-200'
              }`}>
                <h4 className={`text-sm font-medium mb-2 transition-colors duration-200 ${
                  theme === 'dark' ? 'text-white' : 'text-slate-900'
                }`}>
                  Formato esperado del CSV:
                </h4>
                <div className={`text-xs font-mono p-2 rounded border transition-colors duration-200 ${
                  theme === 'dark' 
                    ? 'bg-[#21222d] border-gray-700 text-gray-300' 
                    : 'bg-white border-slate-200 text-slate-700'
                }`}>
                  Fecha,Descripción,Tipo,Categoría,Monto<br/>
                  2024-01-15,"Compra supermercado",Gasto,alimentacion,150.50<br/>
                  2024-01-16,"Salario enero",Ingreso,salario,3000.00
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
