import { useState, useEffect, useCallback } from 'react';

interface BeforeInstallPromptEvent extends Event {
  readonly platforms: string[];
  readonly userChoice: Promise<{
    outcome: 'accepted' | 'dismissed';
    platform: string;
  }>;
  prompt(): Promise<void>;
}

interface PWAState {
  isInstallable: boolean;
  isInstalled: boolean;
  isOnline: boolean;
  isUpdateAvailable: boolean;
  isLoading: boolean;
}

export function usePWA() {
  const [state, setState] = useState<PWAState>({
    isInstallable: false,
    isInstalled: false,
    isOnline: navigator.onLine,
    isUpdateAvailable: false,
    isLoading: true
  });

  const [deferredPrompt, setDeferredPrompt] = useState<BeforeInstallPromptEvent | null>(null);
  const [registration, setRegistration] = useState<ServiceWorkerRegistration | null>(null);

  // Check if app is installed
  const checkIfInstalled = useCallback(() => {
    const isStandalone = window.matchMedia('(display-mode: standalone)').matches;
    const isInWebAppiOS = (window.navigator as any).standalone === true;
    const isInstalled = isStandalone || isInWebAppiOS;
    
    setState(prev => ({ ...prev, isInstalled }));
    return isInstalled;
  }, []);

  // Install the PWA
  const installPWA = useCallback(async () => {
    if (!deferredPrompt) {
      console.warn('PWA: Install prompt not available');
      return false;
    }

    try {
      await deferredPrompt.prompt();
      const { outcome } = await deferredPrompt.userChoice;
      
      if (outcome === 'accepted') {
        console.log('PWA: User accepted the install prompt');
        setDeferredPrompt(null);
        setState(prev => ({ ...prev, isInstallable: false, isInstalled: true }));
        return true;
      } else {
        console.log('PWA: User dismissed the install prompt');
        return false;
      }
    } catch (error) {
      console.error('PWA: Error during installation', error);
      return false;
    }
  }, [deferredPrompt]);

  // Update the service worker
  const updateServiceWorker = useCallback(async () => {
    if (!registration) {
      console.warn('PWA: No service worker registration available');
      return false;
    }

    try {
      await registration.update();
      
      if (registration.waiting) {
        // Tell the waiting service worker to skip waiting
        registration.waiting.postMessage({ type: 'SKIP_WAITING' });
        
        // Reload the page to activate the new service worker
        window.location.reload();
        return true;
      }
      
      return false;
    } catch (error) {
      console.error('PWA: Error updating service worker', error);
      return false;
    }
  }, [registration]);

  // Register service worker
  const registerServiceWorker = useCallback(async () => {
    if ('serviceWorker' in navigator) {
      try {
        const reg = await navigator.serviceWorker.register('/sw.js');
        console.log('PWA: Service worker registered', reg);
        
        setRegistration(reg);

        // Check for updates
        reg.addEventListener('updatefound', () => {
          const newWorker = reg.installing;
          if (newWorker) {
            newWorker.addEventListener('statechange', () => {
              if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                console.log('PWA: New service worker available');
                setState(prev => ({ ...prev, isUpdateAvailable: true }));
              }
            });
          }
        });

        // Listen for controlling service worker changes
        navigator.serviceWorker.addEventListener('controllerchange', () => {
          console.log('PWA: Service worker controller changed');
          window.location.reload();
        });

        return reg;
      } catch (error) {
        console.error('PWA: Service worker registration failed', error);
        return null;
      }
    } else {
      console.warn('PWA: Service workers not supported');
      return null;
    }
  }, []);

  // Request notification permission
  const requestNotificationPermission = useCallback(async () => {
    if ('Notification' in window) {
      try {
        const permission = await Notification.requestPermission();
        console.log('PWA: Notification permission', permission);
        return permission === 'granted';
      } catch (error) {
        console.error('PWA: Error requesting notification permission', error);
        return false;
      }
    }
    return false;
  }, []);

  // Subscribe to push notifications
  const subscribeToPush = useCallback(async () => {
    if (!registration) {
      console.warn('PWA: No service worker registration for push subscription');
      return null;
    }

    try {
      const subscription = await registration.pushManager.subscribe({
        userVisibleOnly: true,
        applicationServerKey: process.env.REACT_APP_VAPID_PUBLIC_KEY
      });

      console.log('PWA: Push subscription created', subscription);
      
      // Send subscription to server
      await fetch('/api/push-subscription', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(subscription)
      });

      return subscription;
    } catch (error) {
      console.error('PWA: Error subscribing to push notifications', error);
      return null;
    }
  }, [registration]);

  // Add to home screen (iOS)
  const addToHomeScreen = useCallback(() => {
    // For iOS, we can only show instructions
    if (/iPad|iPhone|iPod/.test(navigator.userAgent)) {
      alert('Para instalar esta app en iOS:\n1. Toca el botón de compartir\n2. Selecciona "Agregar a pantalla de inicio"');
      return true;
    }
    
    // For other platforms, use the install prompt
    return installPWA();
  }, [installPWA]);

  // Check network status
  const checkNetworkStatus = useCallback(() => {
    setState(prev => ({ ...prev, isOnline: navigator.onLine }));
  }, []);

  // Initialize PWA features
  useEffect(() => {
    const initializePWA = async () => {
      setState(prev => ({ ...prev, isLoading: true }));

      // Check if already installed
      checkIfInstalled();

      // Register service worker
      await registerServiceWorker();

      // Set up event listeners
      const handleBeforeInstallPrompt = (e: Event) => {
        e.preventDefault();
        console.log('PWA: beforeinstallprompt event fired');
        setDeferredPrompt(e as BeforeInstallPromptEvent);
        setState(prev => ({ ...prev, isInstallable: true }));
      };

      const handleAppInstalled = () => {
        console.log('PWA: App was installed');
        setDeferredPrompt(null);
        setState(prev => ({ 
          ...prev, 
          isInstallable: false, 
          isInstalled: true 
        }));
      };

      // Listen for install events
      window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
      window.addEventListener('appinstalled', handleAppInstalled);

      // Listen for network changes
      window.addEventListener('online', checkNetworkStatus);
      window.addEventListener('offline', checkNetworkStatus);

      setState(prev => ({ ...prev, isLoading: false }));

      // Cleanup
      return () => {
        window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
        window.removeEventListener('appinstalled', handleAppInstalled);
        window.removeEventListener('online', checkNetworkStatus);
        window.removeEventListener('offline', checkNetworkStatus);
      };
    };

    initializePWA();
  }, [checkIfInstalled, registerServiceWorker, checkNetworkStatus]);

  // Periodic sync registration
  const registerPeriodicSync = useCallback(async () => {
    if (!registration) return false;

    try {
      // @ts-ignore - Periodic Background Sync is experimental
      if ('periodicSync' in registration) {
        // @ts-ignore
        await registration.periodicSync.register('daily-sync', {
          minInterval: 24 * 60 * 60 * 1000 // 24 hours
        });
        console.log('PWA: Periodic sync registered');
        return true;
      }
    } catch (error) {
      console.error('PWA: Error registering periodic sync', error);
    }
    return false;
  }, [registration]);

  // Background sync for offline actions
  const requestBackgroundSync = useCallback(async (tag: string) => {
    if (!registration) return false;

    try {
      // @ts-ignore - Background Sync API
      if ('sync' in registration) {
        // @ts-ignore
        await registration.sync.register(tag);
        console.log('PWA: Background sync requested', tag);
        return true;
      }
    } catch (error) {
      console.error('PWA: Error requesting background sync', error);
    }
    return false;
  }, [registration]);

  return {
    ...state,
    installPWA,
    updateServiceWorker,
    requestNotificationPermission,
    subscribeToPush,
    addToHomeScreen,
    registerPeriodicSync,
    requestBackgroundSync,
    checkIfInstalled,
    checkNetworkStatus
  };
}
