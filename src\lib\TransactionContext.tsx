import React, { createContext, useContext } from 'react';
import { useTransactions, PlannedExpense } from '../hooks/useTransactions';
import { Transaction } from './types';

interface TransactionContextType {
  transactions: Transaction[];
  plannedExpenses: PlannedExpense[];
  loading: boolean;
  error: string | null;
  addTransaction: (transaction: Omit<Transaction, 'id'>) => Promise<any>;
  addPlannedExpense: (expense: Omit<PlannedExpense, 'id'>) => Promise<any>;
  updatePlannedExpense: (id: string, updates: Partial<PlannedExpense>) => Promise<any>;
  deletePlannedExpense: (id: string) => Promise<any>;
  convertToRealTransaction: (plannedExpenseId: string) => Promise<any>;
  updateTransaction: (id: string, updates: Partial<Transaction>) => Promise<any>;
  deleteTransaction: (id: string) => Promise<any>;
  refetch: () => Promise<void>;
}

const TransactionContext = createContext<TransactionContextType | undefined>(undefined);

export function TransactionProvider({ children }: { children: React.ReactNode }) {
  const transactionData = useTransactions();
  
  return (
    <TransactionContext.Provider value={transactionData}>
      {children}
    </TransactionContext.Provider>
  );
}

export function useTransactionContext() {
  const context = useContext(TransactionContext);
  if (context === undefined) {
    throw new Error('useTransactionContext must be used within a TransactionProvider');
  }
  return context;
}