import React, { createContext, useContext } from 'react';
import { useTransactions } from '../hooks/useTransactions';

interface TransactionContextType {
  transactions: any[];
  loading: boolean;
  error: string | null;
  addTransaction: (transaction: any) => Promise<any>;
  deleteTransaction: (id: string) => Promise<any>;
  refetch: () => Promise<void>;
}

const TransactionContext = createContext<TransactionContextType | undefined>(undefined);

export function TransactionProvider({ children }: { children: React.ReactNode }) {
  const transactionData = useTransactions();
  
  return (
    <TransactionContext.Provider value={transactionData}>
      {children}
    </TransactionContext.Provider>
  );
}

export function useTransactionContext() {
  const context = useContext(TransactionContext);
  if (context === undefined) {
    throw new Error('useTransactionContext must be used within a TransactionProvider');
  }
  return context;
}