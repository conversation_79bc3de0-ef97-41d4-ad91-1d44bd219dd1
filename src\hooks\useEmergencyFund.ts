import { useState, useEffect } from 'react';
import { supabase } from '../lib/supabaseClient';
import { useAuth } from '../lib/AuthContext';
import { useToast } from '../components/Toast';
import { Transaction } from '../lib/types';

export interface EmergencyFundConfig {
  id: string;
  user_id: string;
  monthly_expenses: number;
  coverage_months: number;
  created_at: string;
  updated_at: string;
}

export interface EmergencyFundMovement extends Transaction {
  saldo_resultante?: number;
  medio?: 'transferencia' | 'efectivo' | 'otro';
  motivo?: string;
}

export interface EmergencyFundData {
  fondoInicial: number;
  fondoCompleto: number;
  totalFondo: number;
  metaFondoInicial: number;
  metaFondoCompleto: number;
  porcentajeFondoInicial: number;
  porcentajeFondoCompleto: number;
  mesesActuales: number;
  config: EmergencyFundConfig | null;
  movimientos: EmergencyFundMovement[];
}

const EMERGENCY_FUND_CATEGORIES = {
  BABY_STEP_1: 'fondo_emergencia_inicial',
  BABY_STEP_3: 'fondo_emergencia_completo'
};

export function useEmergencyFund() {
  const [config, setConfig] = useState<EmergencyFundConfig | null>(null);
  const [movimientos, setMovimientos] = useState<EmergencyFundMovement[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { user } = useAuth();
  const { showError, showSuccess } = useToast();

  // Fetch emergency fund configuration
  const fetchConfig = async () => {
    if (!user) return null;

    try {
      const { data, error: supabaseError } = await supabase
        .from('emergency_fund_config')
        .select('*')
        .eq('user_id', user.id)
        .maybeSingle();

      if (supabaseError && supabaseError.code !== 'PGRST116') {
        // If table doesn't exist, return default config
        if (supabaseError.code === '42P01') {
          console.warn('emergency_fund_config table does not exist. Using default configuration.');
          return {
            id: 'default',
            user_id: user.id,
            monthly_expenses: 3000,
            coverage_months: 3,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          };
        }
        throw supabaseError;
      }

      return data;
    } catch (err) {
      console.error('Error fetching emergency fund config:', err);
      // Return default config if there's an error
      return {
        id: 'default',
        user_id: user?.id || '',
        monthly_expenses: 3000,
        coverage_months: 3,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };
    }
  };

  // Fetch emergency fund movements
  const fetchMovements = async () => {
    if (!user) return [];

    try {
      const { data, error: supabaseError } = await supabase
        .from('transactions')
        .select('*')
        .eq('user_id', user.id)
        .in('category', [EMERGENCY_FUND_CATEGORIES.BABY_STEP_1, EMERGENCY_FUND_CATEGORIES.BABY_STEP_3])
        .order('date', { ascending: false });

      if (supabaseError) {
        throw supabaseError;
      }

      return data || [];
    } catch (err) {
      console.error('Error fetching emergency fund movements:', err);
      showError('Error de conexión', 'No se pudieron cargar los movimientos del fondo');
      return [];
    }
  };

  // Initialize or update configuration
  const updateConfig = async (updates: Partial<Pick<EmergencyFundConfig, 'monthly_expenses' | 'coverage_months'>>) => {
    if (!user) {
      return { success: false, error: 'Usuario no autenticado' };
    }

    try {
      setError(null);

      // If table doesn't exist, just update local config
      if (config?.id === 'default') {
        const updatedConfig = {
          ...config,
          ...updates,
          updated_at: new Date().toISOString()
        };
        setConfig(updatedConfig);
        showSuccess('Configuración actualizada', 'Los cambios se aplicaron localmente');
        return { success: true };
      }

      if (config && config.id !== 'default') {
        // Update existing config
        const { data, error: supabaseError } = await supabase
          .from('emergency_fund_config')
          .update(updates)
          .eq('id', config.id)
          .select()
          .single();

        if (supabaseError) throw supabaseError;

        setConfig(data);
        showSuccess('Configuración actualizada', 'Los cambios se guardaron correctamente');
      } else {
        // Create new config
        const { data, error: supabaseError } = await supabase
          .from('emergency_fund_config')
          .insert({
            user_id: user.id,
            monthly_expenses: updates.monthly_expenses || 3000,
            coverage_months: updates.coverage_months || 3
          })
          .select()
          .single();

        if (supabaseError) {
          // If table doesn't exist, use local config
          if (supabaseError.code === '42P01') {
            const localConfig = {
              id: 'default',
              user_id: user.id,
              monthly_expenses: updates.monthly_expenses || 3000,
              coverage_months: updates.coverage_months || 3,
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            };
            setConfig(localConfig);
            showSuccess('Configuración creada', 'Configuración guardada localmente (configura la base de datos para persistencia)');
            return { success: true };
          }
          throw supabaseError;
        }

        setConfig(data);
        showSuccess('Configuración creada', 'La configuración del fondo se guardó correctamente');
      }

      return { success: true };
    } catch (err) {
      console.error('Error updating emergency fund config:', err);
      showError('Error al guardar', 'No se pudo actualizar la configuración');
      const errorMessage = err instanceof Error ? err.message : 'Error al actualizar configuración';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    }
  };

  // Add movement to emergency fund
  const addMovement = async (movement: {
    tipo: 'aporte' | 'retiro';
    monto: number;
    medio: 'transferencia' | 'efectivo' | 'otro';
    motivo: string;
  }) => {
    if (!user) {
      return { success: false, error: 'Usuario no autenticado' };
    }

    try {
      setError(null);

      // Calculate current totals
      const currentData = calculateEmergencyFundData();
      const currentTotal = currentData.totalFondo;
      
      // Calculate new total after movement
      const newTotal = movement.tipo === 'aporte' 
        ? currentTotal + movement.monto 
        : currentTotal - movement.monto;

      if (newTotal < 0) {
        showError('Saldo insuficiente', 'No puedes retirar más dinero del que tienes en el fondo');
        return { success: false, error: 'Saldo insuficiente' };
      }

      // Determine which category to use based on current progress
      let category = EMERGENCY_FUND_CATEGORIES.BABY_STEP_1;
      if (currentData.fondoInicial >= currentData.metaFondoInicial) {
        category = EMERGENCY_FUND_CATEGORIES.BABY_STEP_3;
      }

      const { data, error: supabaseError } = await supabase
        .from('transactions')
        .insert({
          description: movement.motivo || `${movement.tipo === 'aporte' ? 'Aporte' : 'Retiro'} fondo de emergencia`,
          date: new Date().toISOString().split('T')[0],
          type: movement.tipo === 'aporte' ? 'expense' : 'income', // Counter-intuitive but correct for fund tracking
          amount: movement.monto,
          category,
          icon: movement.tipo === 'aporte' ? '🛡️' : '💸',
          user_id: user.id
        })
        .select()
        .single();

      if (supabaseError) throw supabaseError;

      // Refresh movements
      await fetchData();
      
      showSuccess(
        movement.tipo === 'aporte' ? 'Aporte registrado' : 'Retiro registrado',
        'El movimiento se guardó correctamente'
      );
      
      return { success: true, data };
    } catch (err) {
      console.error('Error adding emergency fund movement:', err);
      showError('Error al guardar', 'No se pudo registrar el movimiento');
      const errorMessage = err instanceof Error ? err.message : 'Error al agregar movimiento';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    }
  };

  // Calculate emergency fund data
  const calculateEmergencyFundData = (): EmergencyFundData => {
    const metaFondoInicial = 1000;
    const metaFondoCompleto = (config?.monthly_expenses || 3000) * (config?.coverage_months || 3);

    // Calculate totals from movements
    let fondoInicial = 0;
    let fondoCompleto = 0;

    movimientos.forEach(movement => {
      const amount = movement.type === 'expense' ? movement.amount : -movement.amount; // Reverse for fund accounting
      
      if (movement.category === EMERGENCY_FUND_CATEGORIES.BABY_STEP_1) {
        fondoInicial += amount;
      } else if (movement.category === EMERGENCY_FUND_CATEGORIES.BABY_STEP_3) {
        fondoCompleto += amount;
      }
    });

    // Ensure non-negative values
    fondoInicial = Math.max(0, fondoInicial);
    fondoCompleto = Math.max(0, fondoCompleto);

    const totalFondo = fondoInicial + fondoCompleto;
    const porcentajeFondoInicial = Math.min(100, Math.round((fondoInicial / metaFondoInicial) * 100));
    const porcentajeFondoCompleto = Math.min(100, Math.round((totalFondo / metaFondoCompleto) * 100));
    const mesesActuales = (config?.monthly_expenses || 3000) > 0 ? totalFondo / (config?.monthly_expenses || 3000) : 0;

    return {
      fondoInicial,
      fondoCompleto,
      totalFondo,
      metaFondoInicial,
      metaFondoCompleto,
      porcentajeFondoInicial,
      porcentajeFondoCompleto,
      mesesActuales,
      config,
      movimientos: movimientos.map((m, index) => ({
        ...m,
        saldo_resultante: totalFondo // Simplified - in real app would calculate running balance
      }))
    };
  };

  // Fetch all data
  const fetchData = async () => {
    if (!user) {
      setConfig(null);
      setMovimientos([]);
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const [configData, movementsData] = await Promise.all([
        fetchConfig(),
        fetchMovements()
      ]);

      setConfig(configData);
      setMovimientos(movementsData);
    } catch (err) {
      console.error('Error fetching emergency fund data:', err);
      setError(err instanceof Error ? err.message : 'Error desconocido');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (user) {
      fetchData();
    }
  }, [user]);

  return {
    config,
    movimientos,
    loading,
    error,
    updateConfig,
    addMovement,
    calculateEmergencyFundData,
    refetch: fetchData
  };
}