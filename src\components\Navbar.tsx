import { Link } from 'react-router-dom';
import { Wallet, Home, Pie<PERSON>hart, DollarSign, Calculator, TrendingUp, ChevronDown, LogOut, User } from 'lucide-react';
import { useState } from 'react';
import { ThemeToggle } from './ThemeToggle';
import { useAuth } from '../lib/AuthContext';
import { useTheme } from '../lib/ThemeContext';

export function Navbar() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isToolsOpen, setIsToolsOpen] = useState(false);
  const { user, signOut } = useAuth();
  const { theme } = useTheme();

  const handleSignOut = async () => {
    await signOut();
  };

  return (
    <nav className={`border-b sticky top-0 z-20 transition-colors duration-200 backdrop-blur-sm ${
      theme === 'dark'
        ? 'bg-[#21222d]/95 border-[#2a2a3d]'
        : 'bg-white/95 border-slate-200 shadow-sm'
    }`}>
      <div className="container mx-auto px-4">
        <div className="flex justify-between items-center h-16">
          <Link to="/" className="flex items-center space-x-2">
            <Wallet className="h-6 w-6 text-[#f9769d]" />
            <span className={`font-bold text-xl transition-colors duration-200 ${
              theme === 'dark' ? 'text-white' : 'text-slate-900'
            }`}>Finanzas Personales</span>
          </Link>
          
          <div className="hidden md:flex items-center space-x-8">
            <Link
              to="/"
              className={`flex items-center space-x-1 transition-colors font-medium hover:scale-105 ${
                theme === 'dark'
                  ? 'text-gray-200 hover:text-white'
                  : 'text-slate-600 hover:text-slate-900'
              }`}
            >
              <Home className="h-5 w-5" />
              <span>Inicio</span>
            </Link>

            <Link
              to="/patrimonio"
              className={`flex items-center space-x-1 transition-colors font-medium hover:scale-105 ${
                theme === 'dark'
                  ? 'text-gray-200 hover:text-white'
                  : 'text-slate-600 hover:text-slate-900'
              }`}
            >
              <PieChart className="h-5 w-5" />
              <span>Patrimonio</span>
            </Link>

            <Link
              to="/budgets"
              className={`flex items-center space-x-1 transition-colors font-medium hover:scale-105 ${
                theme === 'dark'
                  ? 'text-gray-200 hover:text-white'
                  : 'text-slate-600 hover:text-slate-900'
              }`}
            >
              <TrendingUp className="h-5 w-5" />
              <span>Presupuestos</span>
            </Link>

            <Link
              to="/flujo-gasto"
              className={`flex items-center space-x-1 transition-colors font-medium hover:scale-105 ${
                theme === 'dark'
                  ? 'text-gray-200 hover:text-white'
                  : 'text-slate-600 hover:text-slate-900'
              }`}
            >
              <DollarSign className="h-5 w-5" />
              <span>Gastos</span>
            </Link>
            
            {/* Herramientas Dropdown */}
            <div className="relative">
              <button 
                onClick={() => setIsToolsOpen(!isToolsOpen)}
                className="flex items-center space-x-1 text-gray-200 hover:text-white transition-colors font-medium hover:scale-105"
              >
                <Calculator className="h-5 w-5" />
                <span>Herramientas</span>
                <ChevronDown className={`h-4 w-4 transition-transform ${isToolsOpen ? 'rotate-180' : ''}`} />
              </button>
              
              {isToolsOpen && (
                <div className="absolute top-full mt-2 w-48 bg-[#2a2b38] rounded-lg shadow-xl z-30 p-2 animate-fadeIn border border-[#3f415a]">
                  <Link 
                    to="/fondo-emergencia" 
                    className="block px-3 py-2 rounded-md text-gray-200 hover:bg-[#32334a] hover:text-white transition-colors"
                    onClick={() => setIsToolsOpen(false)}
                  >
                    🛡️ Fondo Emergencia
                  </Link>
                  <Link 
                    to="/vida-rica" 
                    className="block px-3 py-2 rounded-md text-gray-200 hover:bg-[#32334a] hover:text-white transition-colors"
                    onClick={() => setIsToolsOpen(false)}
                  >
                    💖 Vida Rica
                  </Link>
                  <Link 
                    to="/12-semanas" 
                    className="block px-3 py-2 rounded-md text-gray-200 hover:bg-[#32334a] hover:text-white transition-colors"
                    onClick={() => setIsToolsOpen(false)}
                  >
                    📅 12 Semanas
                  </Link>
                  <Link 
                    to="/calculadora-fire" 
                    className="block px-3 py-2 rounded-md text-gray-200 hover:bg-[#32334a] hover:text-white transition-colors"
                    onClick={() => setIsToolsOpen(false)}
                  >
                    🔥 Calculadora FIRE
                  </Link>
                  <Link 
                    to="/ingresos-pasivos" 
                    className="block px-3 py-2 rounded-md text-gray-200 hover:bg-[#32334a] hover:text-white transition-colors"
                    onClick={() => setIsToolsOpen(false)}
                  >
                    ✨ Ingresos Pasivos
                  </Link>
                </div>
              )}
            </div>

            <Link 
              to="/reports" 
              className="flex items-center space-x-1 text-gray-200 hover:text-white transition-colors font-medium hover:scale-105"
            >
              <TrendingUp className="h-5 w-5" />
              <span>Reportes</span>
            </Link>

            {/* User Menu */}
            <div className="flex items-center space-x-3 border-l border-gray-600 pl-6">
              <div className="flex items-center space-x-1 text-gray-300 text-sm">
                <User className="h-5 w-5" />
                <span className="hidden lg:block">{user?.email?.split('@')[0]}</span>
              </div>
              <button 
                onClick={handleSignOut}
                className="flex items-center space-x-1 text-gray-200 hover:text-red-400 transition-colors font-medium hover:scale-105"
              >
                <LogOut className="h-5 w-5" />
                <span className="hidden lg:block">Salir</span>
              </button>
            </div>

            <ThemeToggle />
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden flex items-center space-x-2">
            <ThemeToggle />
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className={`p-2 rounded-lg focus:outline-none transition-colors ${
                theme === 'dark'
                  ? 'text-gray-200 hover:text-white hover:bg-gray-700'
                  : 'text-slate-600 hover:text-slate-900 hover:bg-slate-100'
              }`}
            >
              <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                {isMenuOpen ? (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                ) : (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                )}
              </svg>
            </button>
          </div>
        </div>

        {/* Mobile menu */}
        {isMenuOpen && (
          <div className={`md:hidden pb-4 animate-fadeIn border-t transition-colors duration-200 ${
            theme === 'dark'
              ? 'bg-[#21222d] border-[#2a2a3d]'
              : 'bg-white border-slate-200'
          }`}>
            <div className="px-2 pt-4 space-y-1">
              {/* Main Navigation */}
              <div className="space-y-1">
                <Link
                  to="/"
                  className={`block py-3 flex items-center space-x-3 transition-colors font-medium rounded-lg px-3 ${
                    theme === 'dark'
                      ? 'text-gray-200 hover:text-white hover:bg-[#2a2b38]'
                      : 'text-slate-600 hover:text-slate-900 hover:bg-slate-100'
                  }`}
                  onClick={() => setIsMenuOpen(false)}
                >
                  <Home className="h-5 w-5" />
                  <span>Inicio</span>
                </Link>

                <Link
                  to="/transactions"
                  className={`block py-3 flex items-center space-x-3 transition-colors font-medium rounded-lg px-3 ${
                    theme === 'dark'
                      ? 'text-gray-200 hover:text-white hover:bg-[#2a2b38]'
                      : 'text-slate-600 hover:text-slate-900 hover:bg-slate-100'
                  }`}
                  onClick={() => setIsMenuOpen(false)}
                >
                  <DollarSign className="h-5 w-5" />
                  <span>Transacciones</span>
                </Link>

                <Link
                  to="/budgets"
                  className={`block py-3 flex items-center space-x-3 transition-colors font-medium rounded-lg px-3 ${
                    theme === 'dark'
                      ? 'text-gray-200 hover:text-white hover:bg-[#2a2b38]'
                      : 'text-slate-600 hover:text-slate-900 hover:bg-slate-100'
                  }`}
                  onClick={() => setIsMenuOpen(false)}
                >
                  <TrendingUp className="h-5 w-5" />
                  <span>Presupuestos</span>
                </Link>

                <Link
                  to="/patrimonio"
                  className={`block py-3 flex items-center space-x-3 transition-colors font-medium rounded-lg px-3 ${
                    theme === 'dark'
                      ? 'text-gray-200 hover:text-white hover:bg-[#2a2b38]'
                      : 'text-slate-600 hover:text-slate-900 hover:bg-slate-100'
                  }`}
                  onClick={() => setIsMenuOpen(false)}
                >
                  <PieChart className="h-5 w-5" />
                  <span>Patrimonio</span>
                </Link>

                <Link
                  to="/flujo-gasto"
                  className={`block py-3 flex items-center space-x-3 transition-colors font-medium rounded-lg px-3 ${
                    theme === 'dark'
                      ? 'text-gray-200 hover:text-white hover:bg-[#2a2b38]'
                      : 'text-slate-600 hover:text-slate-900 hover:bg-slate-100'
                  }`}
                  onClick={() => setIsMenuOpen(false)}
            >
              <DollarSign className="h-5 w-5" />
              <span>Gastos</span>
            </Link>
              </div>

              {/* Tools Section */}
              <div className={`border-t my-4 pt-4 transition-colors duration-200 ${
                theme === 'dark' ? 'border-gray-600' : 'border-slate-200'
              }`}>
                <p className={`text-xs px-3 mb-3 font-medium transition-colors duration-200 ${
                  theme === 'dark' ? 'text-gray-400' : 'text-slate-500'
                }`}>
                  HERRAMIENTAS
                </p>
                <div className="space-y-1">
                  <Link
                    to="/fondo-emergencia"
                    className={`block py-3 flex items-center space-x-3 transition-colors font-medium rounded-lg px-3 ${
                      theme === 'dark'
                        ? 'text-gray-200 hover:text-white hover:bg-[#2a2b38]'
                        : 'text-slate-600 hover:text-slate-900 hover:bg-slate-100'
                    }`}
                    onClick={() => setIsMenuOpen(false)}
                  >
                    <span className="text-lg">🛡️</span>
                    <span>Fondo Emergencia</span>
                  </Link>
                  <Link
                    to="/vida-rica"
                    className={`block py-3 flex items-center space-x-3 transition-colors font-medium rounded-lg px-3 ${
                      theme === 'dark'
                        ? 'text-gray-200 hover:text-white hover:bg-[#2a2b38]'
                        : 'text-slate-600 hover:text-slate-900 hover:bg-slate-100'
                    }`}
                    onClick={() => setIsMenuOpen(false)}
                  >
                    <span className="text-lg">💖</span>
                    <span>Vida Rica</span>
                  </Link>

                  <Link
                    to="/12-semanas"
                    className={`block py-3 flex items-center space-x-3 transition-colors font-medium rounded-lg px-3 ${
                      theme === 'dark'
                        ? 'text-gray-200 hover:text-white hover:bg-[#2a2b38]'
                        : 'text-slate-600 hover:text-slate-900 hover:bg-slate-100'
                    }`}
                    onClick={() => setIsMenuOpen(false)}
                  >
                    <span className="text-lg">📅</span>
                    <span>12 Semanas</span>
                  </Link>

                  <Link
                    to="/calculadora-fire"
                    className={`block py-3 flex items-center space-x-3 transition-colors font-medium rounded-lg px-3 ${
                      theme === 'dark'
                        ? 'text-gray-200 hover:text-white hover:bg-[#2a2b38]'
                        : 'text-slate-600 hover:text-slate-900 hover:bg-slate-100'
                    }`}
                    onClick={() => setIsMenuOpen(false)}
                  >
                    <span className="text-lg">🔥</span>
                    <span>Calculadora FIRE</span>
                  </Link>

                  <Link
                    to="/ingresos-pasivos"
                    className={`block py-3 flex items-center space-x-3 transition-colors font-medium rounded-lg px-3 ${
                      theme === 'dark'
                        ? 'text-gray-200 hover:text-white hover:bg-[#2a2b38]'
                        : 'text-slate-600 hover:text-slate-900 hover:bg-slate-100'
                    }`}
                    onClick={() => setIsMenuOpen(false)}
                  >
                    <span className="text-lg">✨</span>
                    <span>Ingresos Pasivos</span>
                  </Link>
                </div>
              </div>

              {/* Reports Section */}
              <div className={`border-t my-4 pt-4 transition-colors duration-200 ${
                theme === 'dark' ? 'border-gray-600' : 'border-slate-200'
              }`}>
                <Link
                  to="/reports"
                  className={`block py-3 flex items-center space-x-3 transition-colors font-medium rounded-lg px-3 ${
                    theme === 'dark'
                      ? 'text-gray-200 hover:text-white hover:bg-[#2a2b38]'
                      : 'text-slate-600 hover:text-slate-900 hover:bg-slate-100'
                  }`}
                  onClick={() => setIsMenuOpen(false)}
                >
                  <TrendingUp className="h-5 w-5" />
                  <span>Reportes</span>
                </Link>
              </div>

              {/* User Section */}
              <div className={`border-t my-4 pt-4 transition-colors duration-200 ${
                theme === 'dark' ? 'border-gray-600' : 'border-slate-200'
              }`}>
                <div className={`py-3 flex items-center space-x-3 px-3 transition-colors duration-200 ${
                  theme === 'dark' ? 'text-gray-300' : 'text-slate-600'
                }`}>
                  <User className="h-5 w-5" />
                  <span className="text-sm truncate">{user?.email}</span>
                </div>
                <button
                  onClick={() => {
                    handleSignOut();
                    setIsMenuOpen(false);
                  }}
                  className={`block py-3 flex items-center space-x-3 w-full text-left transition-colors font-medium rounded-lg px-3 ${
                    theme === 'dark'
                      ? 'text-gray-200 hover:text-red-400 hover:bg-red-900/20'
                      : 'text-slate-600 hover:text-red-600 hover:bg-red-50'
                  }`}
                >
                  <LogOut className="h-5 w-5" />
                  <span>Cerrar Sesión</span>
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </nav>
  );
}