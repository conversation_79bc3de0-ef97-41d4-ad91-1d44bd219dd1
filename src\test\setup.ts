import '@testing-library/jest-dom';
import { vi, beforeAll, afterAll } from 'vitest';

// Mock environment variables for tests
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: vi.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(), // deprecated
    removeListener: vi.fn(), // deprecated
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  })),
});

// Mock ResizeObserver
global.ResizeObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}));

// Setup console.error to fail tests
const originalError = console.error;
beforeAll(() => {
  console.error = (...args: any[]) => {
    originalError(...args);
    throw new Error('Console error was called');
  };
});

afterAll(() => {
  console.error = originalError;
});