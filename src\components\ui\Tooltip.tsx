import React, { useState, useRef, useEffect } from 'react';
import { useTheme } from '../../lib/ThemeContext';

interface TooltipProps {
  content: string | React.ReactNode;
  children: React.ReactNode;
  position?: 'top' | 'bottom' | 'left' | 'right';
  delay?: number;
  disabled?: boolean;
  className?: string;
  maxWidth?: string;
}

export function Tooltip({
  content,
  children,
  position = 'top',
  delay = 500,
  disabled = false,
  className = '',
  maxWidth = 'max-w-xs'
}: TooltipProps) {
  const { theme } = useTheme();
  const [isVisible, setIsVisible] = useState(false);
  const [actualPosition, setActualPosition] = useState(position);
  const timeoutRef = useRef<NodeJS.Timeout>();
  const tooltipRef = useRef<HTMLDivElement>(null);
  const triggerRef = useRef<HTMLDivElement>(null);

  const showTooltip = () => {
    if (disabled) return;

    timeoutRef.current = setTimeout(() => {
      setIsVisible(true);
      updatePosition();
    }, delay);
  };

  const hideTooltip = () => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    setIsVisible(false);
  };

  const updatePosition = () => {
    if (!tooltipRef.current || !triggerRef.current) return;

    const triggerRect = triggerRef.current.getBoundingClientRect();
    const tooltipRect = tooltipRef.current.getBoundingClientRect();
    const viewport = {
      width: window.innerWidth,
      height: window.innerHeight
    };

    let newPosition = position;

    // Check if tooltip fits in the preferred position
    switch (position) {
      case 'top':
        if (triggerRect.top - tooltipRect.height < 10) {
          newPosition = 'bottom';
        }
        break;
      case 'bottom':
        if (triggerRect.bottom + tooltipRect.height > viewport.height - 10) {
          newPosition = 'top';
        }
        break;
      case 'left':
        if (triggerRect.left - tooltipRect.width < 10) {
          newPosition = 'right';
        }
        break;
      case 'right':
        if (triggerRect.right + tooltipRect.width > viewport.width - 10) {
          newPosition = 'left';
        }
        break;
    }

    setActualPosition(newPosition);
  };

  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  const getPositionClasses = () => {
    const baseClasses = 'absolute z-50 px-3 py-2 text-sm rounded-lg shadow-lg pointer-events-none';
    const colorClasses = theme === 'dark'
      ? 'bg-gray-800 text-white border border-gray-600'
      : 'bg-gray-900 text-white';

    const positionClasses = {
      top: 'bottom-full left-1/2 transform -translate-x-1/2 mb-2',
      bottom: 'top-full left-1/2 transform -translate-x-1/2 mt-2',
      left: 'right-full top-1/2 transform -translate-y-1/2 mr-2',
      right: 'left-full top-1/2 transform -translate-y-1/2 ml-2'
    };

    return `${baseClasses} ${colorClasses} ${positionClasses[actualPosition]} ${maxWidth}`;
  };

  const getArrowClasses = () => {
    const arrowColor = theme === 'dark' ? 'border-gray-800' : 'border-gray-900';

    const arrowClasses = {
      top: `absolute top-full left-1/2 transform -translate-x-1/2 border-4 border-transparent border-t-4 ${arrowColor}`,
      bottom: `absolute bottom-full left-1/2 transform -translate-x-1/2 border-4 border-transparent border-b-4 ${arrowColor}`,
      left: `absolute left-full top-1/2 transform -translate-y-1/2 border-4 border-transparent border-l-4 ${arrowColor}`,
      right: `absolute right-full top-1/2 transform -translate-y-1/2 border-4 border-transparent border-r-4 ${arrowColor}`
    };

    return arrowClasses[actualPosition];
  };

  return (
    <div
      ref={triggerRef}
      className="relative inline-block"
      onMouseEnter={showTooltip}
      onMouseLeave={hideTooltip}
      onFocus={showTooltip}
      onBlur={hideTooltip}
    >
      {children}

      {isVisible && content && (
        <div
          ref={tooltipRef}
          className={`${getPositionClasses()} ${className} animate-fadeIn`}
          role="tooltip"
          aria-hidden="false"
        >
          {content}
          <div className={getArrowClasses()} />
        </div>
      )}
    </div>
  );
}

// Help Icon with Tooltip
interface HelpTooltipProps {
  content: string | React.ReactNode;
  size?: 'sm' | 'md' | 'lg';
  position?: 'top' | 'bottom' | 'left' | 'right';
}

export function HelpTooltip({ content, size = 'sm', position = 'top' }: HelpTooltipProps) {
  const { theme } = useTheme();

  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-5 h-5',
    lg: 'w-6 h-6'
  };

  return (
    <Tooltip content={content} position={position} maxWidth="max-w-sm">
      <button
        type="button"
        className={`inline-flex items-center justify-center rounded-full transition-colors ${
          theme === 'dark'
            ? 'text-gray-400 hover:text-gray-200'
            : 'text-gray-500 hover:text-gray-700'
        }`}
        aria-label="Ayuda"
      >
        <svg
          className={sizeClasses[size]}
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
          />
        </svg>
      </button>
    </Tooltip>
  );
}

// Info Tooltip
interface InfoTooltipProps {
  title: string;
  description: string;
  children: React.ReactNode;
  position?: 'top' | 'bottom' | 'left' | 'right';
}

export function InfoTooltip({ title, description, children, position = 'top' }: InfoTooltipProps) {
  const content = (
    <div>
      <div className="font-semibold mb-1">{title}</div>
      <div className="text-xs opacity-90">{description}</div>
    </div>
  );

  return (
    <Tooltip content={content} position={position} maxWidth="max-w-sm">
      {children}
    </Tooltip>
  );
}

// Keyboard Shortcut Tooltip
interface ShortcutTooltipProps {
  shortcut: string;
  description: string;
  children: React.ReactNode;
}

export function ShortcutTooltip({ shortcut, description, children }: ShortcutTooltipProps) {
  const content = (
    <div className="text-center">
      <div className="text-xs opacity-90 mb-1">{description}</div>
      <div className="inline-flex items-center px-2 py-1 bg-gray-700 rounded text-xs font-mono">
        {shortcut}
      </div>
    </div>
  );

  return (
    <Tooltip content={content} position="bottom" maxWidth="max-w-xs">
      {children}
    </Tooltip>
  );
}

// Status Tooltip
interface StatusTooltipProps {
  status: 'success' | 'warning' | 'error' | 'info';
  message: string;
  children: React.ReactNode;
}

export function StatusTooltip({ status, message, children }: StatusTooltipProps) {
  const statusColors = {
    success: 'text-green-400',
    warning: 'text-yellow-400',
    error: 'text-red-400',
    info: 'text-blue-400'
  };

  const statusIcons = {
    success: '✓',
    warning: '⚠',
    error: '✗',
    info: 'ℹ'
  };

  const content = (
    <div className="flex items-center space-x-2">
      <span className={statusColors[status]}>{statusIcons[status]}</span>
      <span>{message}</span>
    </div>
  );

  return (
    <Tooltip content={content} position="top" maxWidth="max-w-sm">
      {children}
    </Tooltip>
  );
}

// Rich Tooltip with custom content
interface RichTooltipProps {
  children: React.ReactNode;
  trigger: React.ReactNode;
  position?: 'top' | 'bottom' | 'left' | 'right';
  className?: string;
}

export function RichTooltip({ children, trigger, position = 'top', className = '' }: RichTooltipProps) {
  return (
    <Tooltip
      content={children}
      position={position}
      maxWidth="max-w-md"
      className={className}
    >
      {trigger}
    </Tooltip>
  );
}