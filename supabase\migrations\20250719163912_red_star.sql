/*
  # Create transactions table

  1. New Tables
    - `transactions`
      - `id` (uuid, primary key)
      - `description` (text)
      - `date` (date)
      - `type` (text) - 'income' or 'expense'
      - `amount` (numeric)
      - `category` (text)
      - `icon` (text, nullable)
      - `created_at` (timestamp)
      - `updated_at` (timestamp)

  2. Security
    - Enable RLS on `transactions` table
    - Add policy for public access (temporary - will be restricted later with auth)
*/

CREATE TABLE IF NOT EXISTS transactions (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  description text NOT NULL,
  date date NOT NULL,
  type text NOT NULL CHECK (type IN ('income', 'expense')),
  amount numeric NOT NULL CHECK (amount > 0),
  category text NOT NULL,
  icon text,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

ALTER TABLE transactions ENABLE ROW LEVEL SECURITY;

-- Temporary policy to allow all operations for development
-- TODO: Replace with user-specific policies when authentication is implemented
CREATE POLICY "Allow all operations for now"
  ON transactions
  FOR ALL
  TO anon
  USING (true)
  WITH CHECK (true);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS transactions_date_idx ON transactions(date DESC);
CREATE INDEX IF NOT EXISTS transactions_type_idx ON transactions(type);
CREATE INDEX IF NOT EXISTS transactions_category_idx ON transactions(category);

-- Insert some sample data for Carlos
INSERT INTO transactions (description, date, type, amount, category, icon) VALUES
  ('Salario mensual', '2024-01-15', 'income', 5200.00, 'salario', 'S'),
  ('Compra supermercado', '2024-01-14', 'expense', 350.00, 'alimentacion', 'G'),
  ('Pago alquiler', '2024-01-01', 'expense', 850.00, 'vivienda', 'H'),
  ('Freelance proyecto web', '2024-01-10', 'income', 800.00, 'freelance', 'F'),
  ('Gasolina', '2024-01-12', 'expense', 45.00, 'transporte', 'T'),
  ('Netflix subscription', '2024-01-01', 'expense', 15.99, 'entretenimiento', 'N'),
  ('Seguro médico', '2024-01-01', 'expense', 120.00, 'salud', 'M'),
  ('Cena restaurante', '2024-01-13', 'expense', 75.00, 'entretenimiento', 'R'),
  ('Dividendos acciones', '2024-01-05', 'income', 150.00, 'inversiones', 'D'),
  ('Compra libros', '2024-01-08', 'expense', 85.00, 'educacion', 'L')
ON CONFLICT (id) DO NOTHING;