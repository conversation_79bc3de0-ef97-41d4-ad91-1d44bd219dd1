import { Card } from './ui/Card';
import { <PERSON><PERSON><PERSON>, Pie, Cell, ResponsiveContainer, Tooltip, LineChart, Line } from 'recharts';
import type { Mes } from '../lib/types';
import { useState, useEffect, useRef, useMemo } from 'react';
import { TooltipRoot, TooltipTrigger, TooltipContent } from './ui/Tooltip';
import { HelpCircle, Plus, Edit2, Trash2, TrendingUp, TrendingDown } from 'lucide-react';
import { usePatrimonio } from '../hooks/usePatrimonio';
import { LoadingSpinner } from './LoadingSpinner';

interface PatrimonioNetoPanelProps {
  mes: Mes;
}

export function PatrimonioNetoPanel({ mes }: PatrimonioNetoPanelProps) {
  const { assets, liabilities, loading, error, calculatePatrimonio } = usePatrimonio();
  const [showAddModal, setShowAddModal] = useState<'asset' | 'liability' | null>(null);
  const [animatedPatrimonio, setAnimatedPatrimonio] = useState(0);
  const [animatedPercentages, setAnimatedPercentages] = useState({
    activos: Array(3).fill(0),
    pasivos: Array(2).fill(0),
  });

  // Animation refs
  const animationStartedRef = useRef(false);
  
  // Calculate real patrimonio data
  const patrimonioData = useMemo(() => calculatePatrimonio(), [assets, liabilities]);
  
  const { 
    totalActivos, 
    totalPasivos, 
    patrimonioNeto, 
    distribucionActivos, 
    distribucionPasivos 
  } = patrimonioData;

  // Sparkline data (last 6 months)
  const patrimonioTrend = useMemo(() => [
    { mes: 'Nov', valor: Math.max(0, patrimonioNeto - 800) },
    { mes: 'Dic', valor: Math.max(0, patrimonioNeto - 600) },
    { mes: 'Ene', valor: Math.max(0, patrimonioNeto - 500) },
    { mes: 'Feb', valor: Math.max(0, patrimonioNeto - 300) },
    { mes: 'Mar', valor: Math.max(0, patrimonioNeto - 200) },
    { mes: 'Abr', valor: patrimonioNeto },
  ], [patrimonioNeto]);
  
  const cambioMensual = useMemo(() => {
    if (patrimonioTrend.length >= 2) {
      return patrimonioTrend[patrimonioTrend.length - 1].valor - patrimonioTrend[patrimonioTrend.length - 2].valor;
    }
    return 0;
  }, [patrimonioTrend]);
  
  // Animate the patrimonio and percentages when component is visible
  useEffect(() => {
    if (!animationStartedRef.current && !loading) {
      animationStartedRef.current = true;
      
      // Animate patrimonio
      const targetValue = Math.abs(patrimonioNeto);
      const patrimonioInterval = setInterval(() => {
        setAnimatedPatrimonio(prev => {
          if (prev < targetValue) {
            return Math.min(prev + Math.max(100, targetValue / 50), targetValue);
          }
          clearInterval(patrimonioInterval);
          return targetValue;
        });
      }, 30);
      
      // Animate active percentages
      const percentageInterval = setInterval(() => {
        setAnimatedPercentages(prev => {
          const newActivos = [...prev.activos];
          const newPasivos = [...prev.pasivos];
          
          let completed = true;
          
          // Update activos
          distribucionActivos.forEach((item, index) => {
            if (newActivos[index] < item.value) {
              newActivos[index] = Math.min(newActivos[index] + 1, item.value);
              completed = false;
            }
          });
          
          // Update pasivos
          distribucionPasivos.forEach((item, index) => {
            if (newPasivos[index] < item.value) {
              newPasivos[index] = Math.min(newPasivos[index] + 1, item.value);
              completed = false;
            }
          });
          
          if (completed) {
            clearInterval(percentageInterval);
          }
          
          return {
            activos: newActivos,
            pasivos: newPasivos
          };
        });
      }, 20);
      
      return () => {
        clearInterval(patrimonioInterval);
        clearInterval(percentageInterval);
      };
    }
  }, [loading, patrimonioNeto, distribucionActivos, distribucionPasivos]);

  // Create animated data for charts
  const animatedActivosData = distribucionActivos.map((item, index) => ({
    ...item,
    value: animatedPercentages.activos[index]
  }));
  
  const animatedPasivosData = distribucionPasivos.map((item, index) => ({
    ...item,
    value: animatedPercentages.pasivos[index]
  }));

  // Custom label for donut charts
  const renderCustomizedLabel = ({ cx, cy, name, percent }) => {
    return (
      <text 
        x={cx} 
        y={cy} 
        dy={0} 
        textAnchor="middle" 
        fill="#ffffff"
        className="percentage-label"
      >
        {`${(percent * 100).toFixed(0)}%`}
      </text>
    );
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <LoadingSpinner size="lg" text="Cargando patrimonio..." />
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-900/30 border border-red-800 text-red-300 p-6 rounded-lg">
        <p className="font-medium">Error al cargar patrimonio:</p>
        <p className="text-sm">{error}</p>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
      <Card className="lg:col-span-1 hover:shadow-lg transition-shadow duration-300">
        <div className="p-6 relative overflow-hidden">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-xl font-semibold flex items-center gap-2">
              Resumen
              <TooltipRoot>
                <TooltipTrigger>
                  <HelpCircle className="h-4 w-4 text-gray-400 hover:text-gray-300 transition-colors" />
                </TooltipTrigger>
                <TooltipContent>
                  <p className="font-medium">El patrimonio neto es la diferencia entre lo que tienes (activos) y lo que debes (pasivos).</p>
                </TooltipContent>
              </TooltipRoot>
            </h3>
            <button
              onClick={() => setShowAddModal('asset')}
              className="p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded-lg transition-colors"
              title="Agregar activo o pasivo"
            >
              <Plus className="h-4 w-4" />
            </button>
          </div>
          
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="font-semibold text-base">Total Activos:</span>
              <span className="text-lg font-semibold">${totalActivos.toLocaleString()}</span>
            </div>
            
            <div className="flex justify-between items-center">
              <span className="font-semibold text-base">Total Pasivos:</span>
              <span className="text-lg font-semibold">${totalPasivos.toLocaleString()}</span>
            </div>
            
            <div className="pt-2 border-t border-gray-700">
              <div className="flex justify-between items-center">
                <span className="font-semibold text-base">Patrimonio Neto:</span>
                <span className={`text-lg font-bold ${patrimonioNeto >= 0 ? 'text-[#10B981]' : 'text-red-400'}`}>
                  ${animatedPatrimonio.toLocaleString()}
                </span>
              </div>
              <div className="flex justify-between items-center mt-2">
                <span className="font-semibold text-base">Cambio Mensual:</span>
                <div className="flex items-center">
                  {cambioMensual >= 0 ? (
                    <TrendingUp className="h-4 w-4 text-green-400 mr-1" />
                  ) : (
                    <TrendingDown className="h-4 w-4 text-red-400 mr-1" />
                  )}
                  <span className={`text-lg font-bold ${cambioMensual >= 0 ? 'text-[#3B82F6]' : 'text-red-400'}`}>
                    {cambioMensual >= 0 ? '+' : ''}${cambioMensual.toLocaleString()}
                  </span>
                </div>
              </div>
            </div>

            {patrimonioTrend.some(item => item.valor > 0) && (
              <div className="mt-2 h-[60px]">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={patrimonioTrend}>
                    <Line 
                      type="monotone" 
                      dataKey="valor" 
                      stroke={patrimonioNeto >= 0 ? "#3B82F6" : "#EF4444"} 
                      strokeWidth={2}
                      dot={false}
                      animationDuration={1500}
                    />
                  </LineChart>
                </ResponsiveContainer>
              </div>
            )}
            
            {assets.length === 0 && liabilities.length === 0 ? (
              <div className="mt-4 p-3 rounded-lg text-sm bg-[#1e1e2d] border border-[#2a2a3d] text-yellow-300">
                <p className="font-medium">💡 <span className="font-semibold">Comienza:</span> Agrega tus activos y pasivos para ver tu patrimonio neto real.</p>
              </div>
            ) : (
              <div className="mt-4 p-3 rounded-lg text-sm bg-[#1e1e2d] border border-[#2a2a3d] text-blue-300">
                <p className="font-medium">
                  {patrimonioNeto >= 0 ? '📈' : '📉'} 
                  <span className="font-semibold"> Estado:</span> 
                  {patrimonioNeto >= 0 
                    ? ' Tu patrimonio neto es positivo, ¡vas por buen camino!'
                    : ' Tu patrimonio neto es negativo. Considera reducir pasivos o incrementar activos.'
                  }
                </p>
              </div>
            )}
          </div>
        </div>
      </Card>

      <Card className="lg:col-span-1 hover:shadow-lg transition-shadow duration-300">
        <div className="p-6">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-xl font-semibold">Distribución de Activos</h3>
            <button
              onClick={() => setShowAddModal('asset')}
              className="p-1 text-gray-400 hover:text-white hover:bg-gray-700 rounded transition-colors"
              title="Agregar activo"
            >
              <Plus className="h-4 w-4" />
            </button>
          </div>
          <div className="h-[300px] relative">
            {distribucionActivos.length > 0 ? (
              <>
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={animatedActivosData}
                      dataKey="value"
                      nameKey="name"
                      cx="50%"
                      cy="50%"
                      innerRadius={60}
                      outerRadius={80}
                      label={renderCustomizedLabel}
                      labelLine={false}
                      animationDuration={0}
                      paddingAngle={2}
                      stroke="none"
                    >
                      {animatedActivosData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip formatter={(value) => `${value.toFixed(1)}%`} />
                  </PieChart>
                </ResponsiveContainer>
                
                <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-center">
                  <p className="text-sm font-semibold text-gray-300">Activos</p>
                  <p className="text-xl font-bold">${totalActivos.toLocaleString()}</p>
                </div>
              </>
            ) : (
              <div className="flex items-center justify-center h-full">
                <div className="text-center text-gray-500">
                  <p className="font-medium">No hay activos registrados</p>
                  <p className="text-sm mt-1">Agrega tu primer activo para comenzar</p>
                </div>
              </div>
            )}
            
            {/* Legend */}
            {distribucionActivos.length > 0 && (
              <div className="absolute bottom-0 left-0 right-0 flex flex-col space-y-2 px-4">
                {distribucionActivos.map((entry, index) => (
                  <div key={index} className="flex items-center">
                    <div className="w-3 h-3 rounded-full mr-2" style={{ backgroundColor: entry.color }}></div>
                    <span className="font-semibold">{entry.name}</span>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </Card>

      <Card className="lg:col-span-1 hover:shadow-lg transition-shadow duration-300">
        <div className="p-6">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-xl font-semibold">Distribución de Pasivos</h3>
            <button
              onClick={() => setShowAddModal('liability')}
              className="p-1 text-gray-400 hover:text-white hover:bg-gray-700 rounded transition-colors"
              title="Agregar pasivo"
            >
              <Plus className="h-4 w-4" />
            </button>
          </div>
          <div className="h-[300px] relative">
            {distribucionPasivos.length > 0 ? (
              <>
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={animatedPasivosData}
                      dataKey="value"
                      nameKey="name"
                      cx="50%"
                      cy="50%"
                      innerRadius={60}
                      outerRadius={80}
                      label={renderCustomizedLabel}
                      labelLine={false}
                      animationDuration={0}
                      paddingAngle={2}
                      stroke="none"
                    >
                      {animatedPasivosData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip formatter={(value) => `${value.toFixed(1)}%`} />
                  </PieChart>
                </ResponsiveContainer>
                
                <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-center">
                  <p className="text-sm font-semibold text-gray-300">Pasivos</p>
                  <p className="text-xl font-bold">${totalPasivos.toLocaleString()}</p>
                </div>
              </>
            ) : (
              <div className="flex items-center justify-center h-full">
                <div className="text-center text-gray-500">
                  <p className="font-medium">No hay pasivos registrados</p>
                  <p className="text-sm mt-1">¡Perfecto! No tienes deudas</p>
                </div>
              </div>
            )}
            
            {/* Legend */}
            {distribucionPasivos.length > 0 && (
              <div className="absolute bottom-0 left-0 right-0 flex flex-col space-y-2 px-4">
                {distribucionPasivos.map((entry, index) => (
                  <div key={index} className="flex items-center">
                    <div className="w-3 h-3 rounded-full mr-2" style={{ backgroundColor: entry.color }}></div>
                    <span className="font-semibold">{entry.name}</span>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </Card>
      
      {/* Modals for adding assets/liabilities would go here */}
      {/* This would be implemented in a separate component */}
    </div>
  );
}