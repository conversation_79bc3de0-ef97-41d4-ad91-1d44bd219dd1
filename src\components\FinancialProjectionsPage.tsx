import React, { useState, useMemo } from 'react';
import { TrendingUp, TrendingDown, Calendar, Target, AlertTriangle, BarChart3, LineChart } from 'lucide-react';
import { useTheme } from '../lib/ThemeContext';
import { useTransactionContext } from '../lib/TransactionContext';
import { FadeIn, SlideIn } from './ui/LoadingStates';
import { InfoTooltip, HelpTooltip } from './ui/Tooltip';
import { format, addMonths, startOfMonth, endOfMonth, parseISO, isWithinInterval } from 'date-fns';
import { es } from 'date-fns/locale';

interface ProjectionScenario {
  id: string;
  name: string;
  description: string;
  incomeGrowth: number; // Percentage per year
  expenseGrowth: number; // Percentage per year
  additionalIncome: number; // Monthly
  additionalExpenses: number; // Monthly
  oneTimeExpenses: Array<{
    date: string;
    amount: number;
    description: string;
  }>;
  oneTimeIncomes: Array<{
    date: string;
    amount: number;
    description: string;
  }>;
}

interface MonthlyProjection {
  month: string;
  date: Date;
  income: number;
  expenses: number;
  balance: number;
  cumulativeBalance: number;
  scenario: string;
}

const defaultScenarios: ProjectionScenario[] = [
  {
    id: 'conservative',
    name: 'Conservador',
    description: 'Proyección conservadora con crecimiento mínimo',
    incomeGrowth: 3,
    expenseGrowth: 4,
    additionalIncome: 0,
    additionalExpenses: 0,
    oneTimeExpenses: [],
    oneTimeIncomes: []
  },
  {
    id: 'realistic',
    name: 'Realista',
    description: 'Proyección basada en tendencias actuales',
    incomeGrowth: 5,
    expenseGrowth: 3,
    additionalIncome: 0,
    additionalExpenses: 0,
    oneTimeExpenses: [],
    oneTimeIncomes: []
  },
  {
    id: 'optimistic',
    name: 'Optimista',
    description: 'Proyección optimista con mejoras significativas',
    incomeGrowth: 8,
    expenseGrowth: 2,
    additionalIncome: 0,
    additionalExpenses: 0,
    oneTimeExpenses: [],
    oneTimeIncomes: []
  }
];

export function FinancialProjectionsPage() {
  const { theme } = useTheme();
  const { transactions } = useTransactionContext();
  
  const [selectedScenario, setSelectedScenario] = useState<string>('realistic');
  const [projectionMonths, setProjectionMonths] = useState<number>(12);
  const [scenarios, setScenarios] = useState<ProjectionScenario[]>(defaultScenarios);
  const [showScenarioEditor, setShowScenarioEditor] = useState(false);

  // Calculate historical averages
  const historicalData = useMemo(() => {
    const last6Months = Array.from({ length: 6 }, (_, i) => {
      const date = addMonths(new Date(), -i - 1);
      const start = startOfMonth(date);
      const end = endOfMonth(date);
      
      const monthTransactions = transactions.filter(t => 
        isWithinInterval(parseISO(t.date), { start, end })
      );
      
      const income = monthTransactions
        .filter(t => t.type === 'income')
        .reduce((sum, t) => sum + t.amount, 0);
      
      const expenses = monthTransactions
        .filter(t => t.type === 'expense')
        .reduce((sum, t) => sum + t.amount, 0);
      
      return {
        month: format(date, 'MMM yyyy', { locale: es }),
        date,
        income,
        expenses,
        balance: income - expenses
      };
    }).reverse();

    const avgIncome = last6Months.reduce((sum, m) => sum + m.income, 0) / last6Months.length;
    const avgExpenses = last6Months.reduce((sum, m) => sum + m.expenses, 0) / last6Months.length;
    const avgBalance = avgIncome - avgExpenses;

    return {
      months: last6Months,
      avgIncome,
      avgExpenses,
      avgBalance,
      incomeGrowthRate: calculateGrowthRate(last6Months.map(m => m.income)),
      expenseGrowthRate: calculateGrowthRate(last6Months.map(m => m.expenses))
    };
  }, [transactions]);

  // Calculate growth rate from historical data
  const calculateGrowthRate = (values: number[]): number => {
    if (values.length < 2) return 0;
    
    const firstValue = values[0];
    const lastValue = values[values.length - 1];
    
    if (firstValue === 0) return 0;
    
    const monthlyGrowth = Math.pow(lastValue / firstValue, 1 / (values.length - 1)) - 1;
    return monthlyGrowth * 12 * 100; // Convert to annual percentage
  };

  // Generate projections for selected scenario
  const projections = useMemo((): MonthlyProjection[] => {
    const scenario = scenarios.find(s => s.id === selectedScenario);
    if (!scenario) return [];

    const projectionData: MonthlyProjection[] = [];
    let cumulativeBalance = 0;

    for (let i = 0; i < projectionMonths; i++) {
      const projectionDate = addMonths(new Date(), i + 1);
      const monthsFromNow = i + 1;
      
      // Calculate growth factors
      const incomeGrowthFactor = Math.pow(1 + scenario.incomeGrowth / 100, monthsFromNow / 12);
      const expenseGrowthFactor = Math.pow(1 + scenario.expenseGrowth / 100, monthsFromNow / 12);
      
      // Base projections
      let projectedIncome = historicalData.avgIncome * incomeGrowthFactor + scenario.additionalIncome;
      let projectedExpenses = historicalData.avgExpenses * expenseGrowthFactor + scenario.additionalExpenses;
      
      // Add one-time events
      const monthKey = format(projectionDate, 'yyyy-MM');
      
      scenario.oneTimeIncomes.forEach(event => {
        if (event.date.startsWith(monthKey)) {
          projectedIncome += event.amount;
        }
      });
      
      scenario.oneTimeExpenses.forEach(event => {
        if (event.date.startsWith(monthKey)) {
          projectedExpenses += event.amount;
        }
      });
      
      const balance = projectedIncome - projectedExpenses;
      cumulativeBalance += balance;
      
      projectionData.push({
        month: format(projectionDate, 'MMM yyyy', { locale: es }),
        date: projectionDate,
        income: projectedIncome,
        expenses: projectedExpenses,
        balance,
        cumulativeBalance,
        scenario: scenario.name
      });
    }

    return projectionData;
  }, [selectedScenario, projectionMonths, scenarios, historicalData]);

  // Calculate projection insights
  const insights = useMemo(() => {
    if (projections.length === 0) return null;

    const totalProjectedIncome = projections.reduce((sum, p) => sum + p.income, 0);
    const totalProjectedExpenses = projections.reduce((sum, p) => sum + p.expenses, 0);
    const finalBalance = projections[projections.length - 1]?.cumulativeBalance || 0;
    
    const positiveMonths = projections.filter(p => p.balance > 0).length;
    const negativeMonths = projections.filter(p => p.balance < 0).length;
    
    const worstMonth = projections.reduce((worst, current) => 
      current.balance < worst.balance ? current : worst
    );
    
    const bestMonth = projections.reduce((best, current) => 
      current.balance > best.balance ? current : best
    );

    return {
      totalProjectedIncome,
      totalProjectedExpenses,
      finalBalance,
      positiveMonths,
      negativeMonths,
      worstMonth,
      bestMonth,
      avgMonthlyBalance: finalBalance / projections.length,
      breakEvenPoint: projections.findIndex(p => p.cumulativeBalance > 0)
    };
  }, [projections]);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div className="flex items-center gap-3">
          <div>
            <h1 className={`text-3xl font-bold transition-colors duration-200 ${
              theme === 'dark' ? 'text-white' : 'text-slate-900'
            }`}>
              Proyecciones Financieras
            </h1>
            <p className={`text-sm mt-1 transition-colors duration-200 ${
              theme === 'dark' ? 'text-gray-400' : 'text-slate-500'
            }`}>
              Planifica tu futuro financiero con diferentes escenarios
            </p>
          </div>
          <HelpTooltip content="Las proyecciones financieras te ayudan a planificar tu futuro basándose en tus patrones históricos y diferentes escenarios de crecimiento." />
        </div>
      </div>

      {/* Controls */}
      <div className={`rounded-xl p-4 transition-colors duration-200 ${
        theme === 'dark' 
          ? 'bg-[#21222d] border border-[#2a2a3d]' 
          : 'bg-slate-50 border border-slate-200'
      }`}>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {/* Scenario Selection */}
          <div>
            <label className={`block text-sm font-medium mb-2 transition-colors duration-200 ${
              theme === 'dark' ? 'text-gray-300' : 'text-slate-700'
            }`}>
              Escenario
            </label>
            <select
              value={selectedScenario}
              onChange={(e) => setSelectedScenario(e.target.value)}
              className={`w-full px-3 py-2 rounded-lg border transition-colors duration-200 ${
                theme === 'dark'
                  ? 'bg-[#2a2b38] border-gray-600 text-white'
                  : 'bg-white border-slate-300 text-slate-900'
              }`}
            >
              {scenarios.map(scenario => (
                <option key={scenario.id} value={scenario.id}>
                  {scenario.name}
                </option>
              ))}
            </select>
          </div>

          {/* Projection Period */}
          <div>
            <label className={`block text-sm font-medium mb-2 transition-colors duration-200 ${
              theme === 'dark' ? 'text-gray-300' : 'text-slate-700'
            }`}>
              Período (meses)
            </label>
            <select
              value={projectionMonths}
              onChange={(e) => setProjectionMonths(parseInt(e.target.value))}
              className={`w-full px-3 py-2 rounded-lg border transition-colors duration-200 ${
                theme === 'dark'
                  ? 'bg-[#2a2b38] border-gray-600 text-white'
                  : 'bg-white border-slate-300 text-slate-900'
              }`}
            >
              <option value={6}>6 meses</option>
              <option value={12}>1 año</option>
              <option value={24}>2 años</option>
              <option value={36}>3 años</option>
              <option value={60}>5 años</option>
            </select>
          </div>

          {/* Actions */}
          <div className="flex items-end">
            <button
              onClick={() => setShowScenarioEditor(true)}
              className="w-full px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg transition-colors duration-200"
            >
              Personalizar Escenario
            </button>
          </div>
        </div>
      </div>

      {/* Historical Data Summary */}
      <div className={`rounded-xl p-6 transition-colors duration-200 ${
        theme === 'dark' 
          ? 'bg-[#21222d] border border-[#2a2a3d]' 
          : 'bg-slate-50 border border-slate-200'
      }`}>
        <h3 className={`text-lg font-semibold mb-4 transition-colors duration-200 ${
          theme === 'dark' ? 'text-white' : 'text-slate-900'
        }`}>
          Datos Históricos (Últimos 6 meses)
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <InfoTooltip title="Ingreso Promedio" description="Promedio mensual de ingresos en los últimos 6 meses">
            <div className={`p-4 rounded-lg transition-colors duration-200 ${
              theme === 'dark' 
                ? 'bg-[#2a2b38] border border-gray-600' 
                : 'bg-white border border-slate-200'
            }`}>
              <div className="flex items-center">
                <div className="p-2 bg-green-500 rounded-lg">
                  <TrendingUp className="w-5 h-5 text-white" />
                </div>
                <div className="ml-3">
                  <p className={`text-sm transition-colors duration-200 ${
                    theme === 'dark' ? 'text-gray-400' : 'text-slate-500'
                  }`}>
                    Ingreso Promedio
                  </p>
                  <p className="text-lg font-bold text-green-500">
                    ${historicalData.avgIncome.toLocaleString()}
                  </p>
                </div>
              </div>
            </div>
          </InfoTooltip>

          <div className={`p-4 rounded-lg transition-colors duration-200 ${
            theme === 'dark' 
              ? 'bg-[#2a2b38] border border-gray-600' 
              : 'bg-white border border-slate-200'
          }`}>
            <div className="flex items-center">
              <div className="p-2 bg-red-500 rounded-lg">
                <TrendingDown className="w-5 h-5 text-white" />
              </div>
              <div className="ml-3">
                <p className={`text-sm transition-colors duration-200 ${
                  theme === 'dark' ? 'text-gray-400' : 'text-slate-500'
                }`}>
                  Gasto Promedio
                </p>
                <p className="text-lg font-bold text-red-500">
                  ${historicalData.avgExpenses.toLocaleString()}
                </p>
              </div>
            </div>
          </div>

          <div className={`p-4 rounded-lg transition-colors duration-200 ${
            theme === 'dark' 
              ? 'bg-[#2a2b38] border border-gray-600' 
              : 'bg-white border border-slate-200'
          }`}>
            <div className="flex items-center">
              <div className={`p-2 rounded-lg ${historicalData.avgBalance >= 0 ? 'bg-blue-500' : 'bg-orange-500'}`}>
                <BarChart3 className="w-5 h-5 text-white" />
              </div>
              <div className="ml-3">
                <p className={`text-sm transition-colors duration-200 ${
                  theme === 'dark' ? 'text-gray-400' : 'text-slate-500'
                }`}>
                  Balance Promedio
                </p>
                <p className={`text-lg font-bold ${
                  historicalData.avgBalance >= 0 ? 'text-blue-500' : 'text-orange-500'
                }`}>
                  ${historicalData.avgBalance.toLocaleString()}
                </p>
              </div>
            </div>
          </div>

          <div className={`p-4 rounded-lg transition-colors duration-200 ${
            theme === 'dark' 
              ? 'bg-[#2a2b38] border border-gray-600' 
              : 'bg-white border border-slate-200'
          }`}>
            <div className="flex items-center">
              <div className="p-2 bg-purple-500 rounded-lg">
                <LineChart className="w-5 h-5 text-white" />
              </div>
              <div className="ml-3">
                <p className={`text-sm transition-colors duration-200 ${
                  theme === 'dark' ? 'text-gray-400' : 'text-slate-500'
                }`}>
                  Tendencia Ingresos
                </p>
                <p className={`text-lg font-bold ${
                  historicalData.incomeGrowthRate >= 0 ? 'text-green-500' : 'text-red-500'
                }`}>
                  {historicalData.incomeGrowthRate >= 0 ? '+' : ''}{historicalData.incomeGrowthRate.toFixed(1)}%
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Projection Results */}
      {insights && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Summary Cards */}
          <div className="space-y-4">
            <h3 className={`text-lg font-semibold transition-colors duration-200 ${
              theme === 'dark' ? 'text-white' : 'text-slate-900'
            }`}>
              Resumen de Proyección - {scenarios.find(s => s.id === selectedScenario)?.name}
            </h3>

            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div className={`p-4 rounded-lg transition-colors duration-200 ${
                theme === 'dark'
                  ? 'bg-[#21222d] border border-[#2a2a3d]'
                  : 'bg-slate-50 border border-slate-200'
              }`}>
                <div className="flex items-center justify-between">
                  <div>
                    <p className={`text-sm transition-colors duration-200 ${
                      theme === 'dark' ? 'text-gray-400' : 'text-slate-500'
                    }`}>
                      Balance Final
                    </p>
                    <p className={`text-xl font-bold ${
                      insights.finalBalance >= 0 ? 'text-green-500' : 'text-red-500'
                    }`}>
                      ${insights.finalBalance.toLocaleString()}
                    </p>
                  </div>
                  <div className={`p-2 rounded-lg ${
                    insights.finalBalance >= 0 ? 'bg-green-500' : 'bg-red-500'
                  }`}>
                    {insights.finalBalance >= 0 ? (
                      <TrendingUp className="w-5 h-5 text-white" />
                    ) : (
                      <TrendingDown className="w-5 h-5 text-white" />
                    )}
                  </div>
                </div>
              </div>

              <div className={`p-4 rounded-lg transition-colors duration-200 ${
                theme === 'dark'
                  ? 'bg-[#21222d] border border-[#2a2a3d]'
                  : 'bg-slate-50 border border-slate-200'
              }`}>
                <div className="flex items-center justify-between">
                  <div>
                    <p className={`text-sm transition-colors duration-200 ${
                      theme === 'dark' ? 'text-gray-400' : 'text-slate-500'
                    }`}>
                      Balance Promedio
                    </p>
                    <p className={`text-xl font-bold ${
                      insights.avgMonthlyBalance >= 0 ? 'text-blue-500' : 'text-orange-500'
                    }`}>
                      ${insights.avgMonthlyBalance.toLocaleString()}
                    </p>
                  </div>
                  <div className={`p-2 rounded-lg ${
                    insights.avgMonthlyBalance >= 0 ? 'bg-blue-500' : 'bg-orange-500'
                  }`}>
                    <BarChart3 className="w-5 h-5 text-white" />
                  </div>
                </div>
              </div>

              <div className={`p-4 rounded-lg transition-colors duration-200 ${
                theme === 'dark'
                  ? 'bg-[#21222d] border border-[#2a2a3d]'
                  : 'bg-slate-50 border border-slate-200'
              }`}>
                <div className="flex items-center justify-between">
                  <div>
                    <p className={`text-sm transition-colors duration-200 ${
                      theme === 'dark' ? 'text-gray-400' : 'text-slate-500'
                    }`}>
                      Meses Positivos
                    </p>
                    <p className="text-xl font-bold text-green-500">
                      {insights.positiveMonths}/{projections.length}
                    </p>
                  </div>
                  <div className="p-2 bg-green-500 rounded-lg">
                    <Calendar className="w-5 h-5 text-white" />
                  </div>
                </div>
              </div>

              <div className={`p-4 rounded-lg transition-colors duration-200 ${
                theme === 'dark'
                  ? 'bg-[#21222d] border border-[#2a2a3d]'
                  : 'bg-slate-50 border border-slate-200'
              }`}>
                <div className="flex items-center justify-between">
                  <div>
                    <p className={`text-sm transition-colors duration-200 ${
                      theme === 'dark' ? 'text-gray-400' : 'text-slate-500'
                    }`}>
                      Meses Negativos
                    </p>
                    <p className="text-xl font-bold text-red-500">
                      {insights.negativeMonths}/{projections.length}
                    </p>
                  </div>
                  <div className="p-2 bg-red-500 rounded-lg">
                    <AlertTriangle className="w-5 h-5 text-white" />
                  </div>
                </div>
              </div>
            </div>

            {/* Key Insights */}
            <div className={`p-4 rounded-lg transition-colors duration-200 ${
              theme === 'dark'
                ? 'bg-[#21222d] border border-[#2a2a3d]'
                : 'bg-slate-50 border border-slate-200'
            }`}>
              <h4 className={`font-medium mb-3 transition-colors duration-200 ${
                theme === 'dark' ? 'text-white' : 'text-slate-900'
              }`}>
                Puntos Clave
              </h4>

              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className={theme === 'dark' ? 'text-gray-400' : 'text-slate-600'}>
                    Mejor mes:
                  </span>
                  <span className="text-green-500 font-medium">
                    {insights.bestMonth.month} (+${insights.bestMonth.balance.toLocaleString()})
                  </span>
                </div>

                <div className="flex justify-between">
                  <span className={theme === 'dark' ? 'text-gray-400' : 'text-slate-600'}>
                    Peor mes:
                  </span>
                  <span className="text-red-500 font-medium">
                    {insights.worstMonth.month} (${insights.worstMonth.balance.toLocaleString()})
                  </span>
                </div>

                {insights.breakEvenPoint >= 0 && (
                  <div className="flex justify-between">
                    <span className={theme === 'dark' ? 'text-gray-400' : 'text-slate-600'}>
                      Punto de equilibrio:
                    </span>
                    <span className="text-blue-500 font-medium">
                      Mes {insights.breakEvenPoint + 1}
                    </span>
                  </div>
                )}

                <div className="flex justify-between">
                  <span className={theme === 'dark' ? 'text-gray-400' : 'text-slate-600'}>
                    Total ingresos proyectados:
                  </span>
                  <span className="text-green-500 font-medium">
                    ${insights.totalProjectedIncome.toLocaleString()}
                  </span>
                </div>

                <div className="flex justify-between">
                  <span className={theme === 'dark' ? 'text-gray-400' : 'text-slate-600'}>
                    Total gastos proyectados:
                  </span>
                  <span className="text-red-500 font-medium">
                    ${insights.totalProjectedExpenses.toLocaleString()}
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Scenario Comparison */}
          <div className="space-y-4">
            <h3 className={`text-lg font-semibold transition-colors duration-200 ${
              theme === 'dark' ? 'text-white' : 'text-slate-900'
            }`}>
              Comparación de Escenarios
            </h3>

            <div className="space-y-3">
              {scenarios.map((scenario) => {
                // Calculate quick projection for comparison
                const quickProjection = projectionMonths * (
                  (historicalData.avgIncome * Math.pow(1 + scenario.incomeGrowth / 100, 1) + scenario.additionalIncome) -
                  (historicalData.avgExpenses * Math.pow(1 + scenario.expenseGrowth / 100, 1) + scenario.additionalExpenses)
                );

                return (
                  <div
                    key={scenario.id}
                    className={`p-4 rounded-lg border-2 transition-all duration-200 cursor-pointer ${
                      selectedScenario === scenario.id
                        ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                        : theme === 'dark'
                          ? 'border-[#2a2a3d] bg-[#21222d] hover:border-gray-600'
                          : 'border-slate-200 bg-white hover:border-slate-300'
                    }`}
                    onClick={() => setSelectedScenario(scenario.id)}
                  >
                    <div className="flex justify-between items-start mb-2">
                      <div>
                        <h4 className={`font-medium transition-colors duration-200 ${
                          theme === 'dark' ? 'text-white' : 'text-slate-900'
                        }`}>
                          {scenario.name}
                        </h4>
                        <p className={`text-sm transition-colors duration-200 ${
                          theme === 'dark' ? 'text-gray-400' : 'text-slate-600'
                        }`}>
                          {scenario.description}
                        </p>
                      </div>
                      <div className="text-right">
                        <p className={`text-lg font-bold ${
                          quickProjection >= 0 ? 'text-green-500' : 'text-red-500'
                        }`}>
                          ${quickProjection.toLocaleString()}
                        </p>
                        <p className={`text-xs transition-colors duration-200 ${
                          theme === 'dark' ? 'text-gray-500' : 'text-slate-400'
                        }`}>
                          {projectionMonths} meses
                        </p>
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-4 text-xs">
                      <div>
                        <span className={theme === 'dark' ? 'text-gray-400' : 'text-slate-500'}>
                          Crecimiento ingresos:
                        </span>
                        <span className="text-green-500 font-medium ml-1">
                          +{scenario.incomeGrowth}%
                        </span>
                      </div>
                      <div>
                        <span className={theme === 'dark' ? 'text-gray-400' : 'text-slate-500'}>
                          Crecimiento gastos:
                        </span>
                        <span className="text-red-500 font-medium ml-1">
                          +{scenario.expenseGrowth}%
                        </span>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
