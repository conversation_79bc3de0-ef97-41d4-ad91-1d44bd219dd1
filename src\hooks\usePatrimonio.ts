import { useState, useEffect } from 'react';
import { supabase } from '../lib/supabaseClient';
import { useAuth } from '../lib/AuthContext';
import { useToast } from '../components/Toast';

export interface Asset {
  id: string;
  name: string;
  value: number;
  category: 'liquid' | 'investment' | 'real_estate';
  user_id: string;
  created_at: string;
  updated_at: string;
}

export interface Liability {
  id: string;
  name: string;
  value: number;
  category: 'credit_card' | 'loan' | 'mortgage';
  user_id: string;
  created_at: string;
  updated_at: string;
}

export interface PatrimonioData {
  totalActivos: number;
  totalPasivos: number;
  patrimonioNeto: number;
  distribucionActivos: Array<{ name: string; value: number; color: string }>;
  distribucionPasivos: Array<{ name: string; value: number; color: string }>;
}

export function usePatrimonio() {
  const [assets, setAssets] = useState<Asset[]>([]);
  const [liabilities, setLiabilities] = useState<Liability[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { user } = useAuth();
  const { showError, showSuccess } = useToast();

  // Fetch assets and liabilities
  const fetchData = async () => {
    if (!user) {
      setAssets([]);
      setLiabilities([]);
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      // Fetch assets
      const { data: assetsData, error: assetsError } = await supabase
        .from('assets')
        .select('*')
        .order('created_at', { ascending: false });

      if (assetsError) throw assetsError;

      // Fetch liabilities
      const { data: liabilitiesData, error: liabilitiesError } = await supabase
        .from('liabilities')
        .select('*')
        .order('created_at', { ascending: false });

      if (liabilitiesError) throw liabilitiesError;

      setAssets(assetsData || []);
      setLiabilities(liabilitiesData || []);
    } catch (err) {
      console.error('Error fetching patrimonio data:', err);
      showError('Error de conexión', 'No se pudieron cargar los datos de patrimonio');
      setError(err instanceof Error ? err.message : 'Error desconocido');
    } finally {
      setLoading(false);
    }
  };

  // Add asset
  const addAsset = async (asset: Omit<Asset, 'id' | 'user_id' | 'created_at' | 'updated_at'>) => {
    if (!user) {
      return { success: false, error: 'Usuario no autenticado' };
    }

    try {
      setError(null);

      const { data, error: supabaseError } = await supabase
        .from('assets')
        .insert({
          name: asset.name,
          value: asset.value,
          category: asset.category,
          user_id: user.id
        })
        .select()
        .single();

      if (supabaseError) throw supabaseError;

      setAssets(prev => [data, ...prev]);
      showSuccess('Activo agregado', 'El activo se guardó correctamente');
      
      return { success: true, data };
    } catch (err) {
      console.error('Error adding asset:', err);
      showError('Error al guardar', 'No se pudo agregar el activo');
      const errorMessage = err instanceof Error ? err.message : 'Error al agregar activo';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    }
  };

  // Add liability
  const addLiability = async (liability: Omit<Liability, 'id' | 'user_id' | 'created_at' | 'updated_at'>) => {
    if (!user) {
      return { success: false, error: 'Usuario no autenticado' };
    }

    try {
      setError(null);

      const { data, error: supabaseError } = await supabase
        .from('liabilities')
        .insert({
          name: liability.name,
          value: liability.value,
          category: liability.category,
          user_id: user.id
        })
        .select()
        .single();

      if (supabaseError) throw supabaseError;

      setLiabilities(prev => [data, ...prev]);
      showSuccess('Pasivo agregado', 'El pasivo se guardó correctamente');
      
      return { success: true, data };
    } catch (err) {
      console.error('Error adding liability:', err);
      showError('Error al guardar', 'No se pudo agregar el pasivo');
      const errorMessage = err instanceof Error ? err.message : 'Error al agregar pasivo';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    }
  };

  // Update asset
  const updateAsset = async (id: string, updates: Partial<Omit<Asset, 'id' | 'user_id' | 'created_at' | 'updated_at'>>) => {
    if (!user) {
      return { success: false, error: 'Usuario no autenticado' };
    }

    try {
      setError(null);

      const { data, error: supabaseError } = await supabase
        .from('assets')
        .update(updates)
        .eq('id', id)
        .select()
        .single();

      if (supabaseError) throw supabaseError;

      setAssets(prev => prev.map(asset => asset.id === id ? data : asset));
      showSuccess('Activo actualizado', 'Los cambios se guardaron correctamente');
      
      return { success: true, data };
    } catch (err) {
      console.error('Error updating asset:', err);
      showError('Error al actualizar', 'No se pudo actualizar el activo');
      const errorMessage = err instanceof Error ? err.message : 'Error al actualizar activo';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    }
  };

  // Update liability
  const updateLiability = async (id: string, updates: Partial<Omit<Liability, 'id' | 'user_id' | 'created_at' | 'updated_at'>>) => {
    if (!user) {
      return { success: false, error: 'Usuario no autenticado' };
    }

    try {
      setError(null);

      const { data, error: supabaseError } = await supabase
        .from('liabilities')
        .update(updates)
        .eq('id', id)
        .select()
        .single();

      if (supabaseError) throw supabaseError;

      setLiabilities(prev => prev.map(liability => liability.id === id ? data : liability));
      showSuccess('Pasivo actualizado', 'Los cambios se guardaron correctamente');
      
      return { success: true, data };
    } catch (err) {
      console.error('Error updating liability:', err);
      showError('Error al actualizar', 'No se pudo actualizar el pasivo');
      const errorMessage = err instanceof Error ? err.message : 'Error al actualizar pasivo';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    }
  };

  // Delete asset
  const deleteAsset = async (id: string) => {
    if (!user) {
      return { success: false, error: 'Usuario no autenticado' };
    }

    try {
      setError(null);

      const { error: supabaseError } = await supabase
        .from('assets')
        .delete()
        .eq('id', id);

      if (supabaseError) throw supabaseError;

      setAssets(prev => prev.filter(asset => asset.id !== id));
      showSuccess('Activo eliminado', 'El activo se eliminó correctamente');
      
      return { success: true };
    } catch (err) {
      console.error('Error deleting asset:', err);
      showError('Error al eliminar', 'No se pudo eliminar el activo');
      const errorMessage = err instanceof Error ? err.message : 'Error al eliminar activo';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    }
  };

  // Delete liability
  const deleteLiability = async (id: string) => {
    if (!user) {
      return { success: false, error: 'Usuario no autenticado' };
    }

    try {
      setError(null);

      const { error: supabaseError } = await supabase
        .from('liabilities')
        .delete()
        .eq('id', id);

      if (supabaseError) throw supabaseError;

      setLiabilities(prev => prev.filter(liability => liability.id !== id));
      showSuccess('Pasivo eliminado', 'El pasivo se eliminó correctamente');
      
      return { success: true };
    } catch (err) {
      console.error('Error deleting liability:', err);
      showError('Error al eliminar', 'No se pudo eliminar el pasivo');
      const errorMessage = err instanceof Error ? err.message : 'Error al eliminar pasivo';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    }
  };

  // Calculate patrimonio data
  const calculatePatrimonio = (): PatrimonioData => {
    const totalActivos = assets.reduce((sum, asset) => sum + asset.value, 0);
    const totalPasivos = liabilities.reduce((sum, liability) => sum + liability.value, 0);
    const patrimonioNeto = totalActivos - totalPasivos;

    // Group assets by category
    const assetsByCategory = assets.reduce((acc, asset) => {
      acc[asset.category] = (acc[asset.category] || 0) + asset.value;
      return acc;
    }, {} as Record<string, number>);

    // Group liabilities by category
    const liabilitiesByCategory = liabilities.reduce((acc, liability) => {
      acc[liability.category] = (acc[liability.category] || 0) + liability.value;
      return acc;
    }, {} as Record<string, number>);

    // Create distribution arrays
    const categoryLabels = {
      liquid: 'Líquido',
      investment: 'Inversión',
      real_estate: 'Inmuebles',
      credit_card: 'Tarjetas',
      loan: 'Préstamos',
      mortgage: 'Hipoteca'
    };

    const categoryColors = {
      liquid: '#2dd4bf',
      investment: '#22d3ee',
      real_estate: '#38bdf8',
      credit_card: '#f9769d',
      loan: '#f472b6',
      mortgage: '#d946ef'
    };

    const distribucionActivos = Object.entries(assetsByCategory).map(([category, value]) => ({
      name: categoryLabels[category],
      value: totalActivos > 0 ? (value / totalActivos) * 100 : 0,
      color: categoryColors[category]
    }));

    const distribucionPasivos = Object.entries(liabilitiesByCategory).map(([category, value]) => ({
      name: categoryLabels[category],
      value: totalPasivos > 0 ? (value / totalPasivos) * 100 : 0,
      color: categoryColors[category]
    }));

    return {
      totalActivos,
      totalPasivos,
      patrimonioNeto,
      distribucionActivos,
      distribucionPasivos
    };
  };

  useEffect(() => {
    if (user) {
      fetchData();
    }
  }, [user]);

  return {
    assets,
    liabilities,
    loading,
    error,
    addAsset,
    addLiability,
    updateAsset,
    updateLiability,
    deleteAsset,
    deleteLiability,
    calculatePatrimonio,
    refetch: fetchData
  };
}