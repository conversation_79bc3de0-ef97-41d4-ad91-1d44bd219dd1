import React, { forwardRef, useEffect, useRef } from 'react';
import { useTheme } from '../../lib/ThemeContext';
import { useFocusManagement, useAriaAttributes, useFocusVisible } from '../../hooks/useAccessibility';

// Accessible Button Component
interface AccessibleButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'danger' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  isLoading?: boolean;
  loadingText?: string;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
}

export const AccessibleButton = forwardRef<HTMLButtonElement, AccessibleButtonProps>(
  ({ 
    children, 
    variant = 'primary', 
    size = 'md', 
    isLoading = false,
    loadingText,
    leftIcon,
    rightIcon,
    disabled,
    className = '',
    ...props 
  }, ref) => {
    const { theme } = useTheme();
    const { createAriaProps } = useAriaAttributes();
    const isFocusVisible = useFocusVisible();

    const baseClasses = `
      inline-flex items-center justify-center font-medium rounded-lg
      transition-all duration-200 focus:outline-none
      ${isFocusVisible ? 'focus:ring-2 focus:ring-offset-2' : ''}
      disabled:opacity-50 disabled:cursor-not-allowed
      button-press ripple
    `;

    const sizeClasses = {
      sm: 'px-3 py-1.5 text-sm',
      md: 'px-4 py-2 text-sm',
      lg: 'px-6 py-3 text-base'
    };

    const variantClasses = {
      primary: theme === 'dark'
        ? 'bg-blue-600 hover:bg-blue-700 text-white focus:ring-blue-500'
        : 'bg-blue-600 hover:bg-blue-700 text-white focus:ring-blue-500',
      secondary: theme === 'dark'
        ? 'bg-gray-600 hover:bg-gray-700 text-white focus:ring-gray-500'
        : 'bg-gray-200 hover:bg-gray-300 text-gray-900 focus:ring-gray-500',
      danger: theme === 'dark'
        ? 'bg-red-600 hover:bg-red-700 text-white focus:ring-red-500'
        : 'bg-red-600 hover:bg-red-700 text-white focus:ring-red-500',
      ghost: theme === 'dark'
        ? 'hover:bg-gray-700 text-gray-300 focus:ring-gray-500'
        : 'hover:bg-gray-100 text-gray-700 focus:ring-gray-500'
    };

    const ariaProps = createAriaProps({
      disabled: disabled || isLoading,
      label: isLoading ? loadingText : undefined
    });

    return (
      <button
        ref={ref}
        className={`${baseClasses} ${sizeClasses[size]} ${variantClasses[variant]} ${className}`}
        disabled={disabled || isLoading}
        {...ariaProps}
        {...props}
      >
        {isLoading && (
          <svg className="animate-spin -ml-1 mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24">
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" />
            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
          </svg>
        )}
        {!isLoading && leftIcon && <span className="mr-2">{leftIcon}</span>}
        <span>{isLoading ? (loadingText || 'Cargando...') : children}</span>
        {!isLoading && rightIcon && <span className="ml-2">{rightIcon}</span>}
      </button>
    );
  }
);

AccessibleButton.displayName = 'AccessibleButton';

// Accessible Input Component
interface AccessibleInputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label: string;
  error?: string;
  helpText?: string;
  isRequired?: boolean;
}

export const AccessibleInput = forwardRef<HTMLInputElement, AccessibleInputProps>(
  ({ label, error, helpText, isRequired, className = '', id, ...props }, ref) => {
    const { theme } = useTheme();
    const { generateId, createAriaProps } = useAriaAttributes();
    const isFocusVisible = useFocusVisible();

    const inputId = id || generateId('input');
    const errorId = error ? generateId('error') : undefined;
    const helpId = helpText ? generateId('help') : undefined;

    const ariaProps = createAriaProps({
      required: isRequired,
      invalid: !!error,
      describedBy: [errorId, helpId].filter(Boolean).join(' ') || undefined
    });

    return (
      <div className="space-y-1">
        <label
          htmlFor={inputId}
          className={`block text-sm font-medium transition-colors duration-200 ${
            theme === 'dark' ? 'text-gray-300' : 'text-slate-700'
          }`}
        >
          {label}
          {isRequired && <span className="text-red-500 ml-1" aria-label="requerido">*</span>}
        </label>
        
        <input
          ref={ref}
          id={inputId}
          className={`
            w-full px-3 py-2 rounded-lg border transition-all duration-200
            ${isFocusVisible ? 'focus:ring-2 focus:ring-offset-2 focus:ring-blue-500' : ''}
            focus:outline-none
            ${error 
              ? 'border-red-500 focus:border-red-500' 
              : theme === 'dark'
                ? 'bg-[#2a2b38] border-gray-600 text-white focus:border-blue-500'
                : 'bg-white border-slate-300 text-slate-900 focus:border-blue-500'
            }
            ${className}
          `}
          {...ariaProps}
          {...props}
        />
        
        {helpText && (
          <p
            id={helpId}
            className={`text-sm transition-colors duration-200 ${
              theme === 'dark' ? 'text-gray-400' : 'text-slate-600'
            }`}
          >
            {helpText}
          </p>
        )}
        
        {error && (
          <p
            id={errorId}
            className="text-sm text-red-500"
            role="alert"
            aria-live="polite"
          >
            {error}
          </p>
        )}
      </div>
    );
  }
);

AccessibleInput.displayName = 'AccessibleInput';

// Accessible Modal Component
interface AccessibleModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  children: React.ReactNode;
  size?: 'sm' | 'md' | 'lg' | 'xl';
}

export function AccessibleModal({ isOpen, onClose, title, children, size = 'md' }: AccessibleModalProps) {
  const { theme } = useTheme();
  const { trapFocus } = useFocusManagement();
  const { generateId } = useAriaAttributes();
  const modalRef = useRef<HTMLDivElement>(null);

  const titleId = generateId('modal-title');
  const contentId = generateId('modal-content');

  useEffect(() => {
    if (isOpen && modalRef.current) {
      const cleanup = trapFocus(modalRef.current);
      return cleanup;
    }
  }, [isOpen, trapFocus]);

  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isOpen) {
        onClose();
      }
    };

    document.addEventListener('keydown', handleEscape);
    return () => document.removeEventListener('keydown', handleEscape);
  }, [isOpen, onClose]);

  if (!isOpen) return null;

  const sizeClasses = {
    sm: 'max-w-md',
    md: 'max-w-lg',
    lg: 'max-w-2xl',
    xl: 'max-w-4xl'
  };

  return (
    <div
      className="fixed inset-0 z-50 overflow-y-auto"
      role="dialog"
      aria-modal="true"
      aria-labelledby={titleId}
      aria-describedby={contentId}
    >
      {/* Backdrop */}
      <div
        className="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
        onClick={onClose}
        aria-hidden="true"
      />
      
      {/* Modal */}
      <div className="flex min-h-full items-center justify-center p-4">
        <div
          ref={modalRef}
          className={`
            relative w-full ${sizeClasses[size]} rounded-xl shadow-xl
            transition-all duration-200 animate-scaleIn
            ${theme === 'dark' 
              ? 'bg-[#21222d] border border-[#2a2a3d]' 
              : 'bg-white border border-slate-200'
            }
          `}
        >
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-gray-600">
            <h2
              id={titleId}
              className={`text-xl font-bold transition-colors duration-200 ${
                theme === 'dark' ? 'text-white' : 'text-slate-900'
              }`}
            >
              {title}
            </h2>
            <AccessibleButton
              variant="ghost"
              size="sm"
              onClick={onClose}
              aria-label="Cerrar modal"
            >
              <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </AccessibleButton>
          </div>
          
          {/* Content */}
          <div id={contentId} className="p-6">
            {children}
          </div>
        </div>
      </div>
    </div>
  );
}

// Skip Links Component
export function SkipLinks() {
  const { theme } = useTheme();

  return (
    <div className="sr-only focus-within:not-sr-only">
      <a
        href="#main-content"
        className={`
          fixed top-4 left-4 z-50 px-4 py-2 rounded-lg font-medium
          transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2
          ${theme === 'dark'
            ? 'bg-blue-600 text-white focus:ring-blue-500'
            : 'bg-blue-600 text-white focus:ring-blue-500'
          }
        `}
      >
        Saltar al contenido principal
      </a>
      <a
        href="#main-navigation"
        className={`
          fixed top-16 left-4 z-50 px-4 py-2 rounded-lg font-medium
          transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2
          ${theme === 'dark'
            ? 'bg-blue-600 text-white focus:ring-blue-500'
            : 'bg-blue-600 text-white focus:ring-blue-500'
          }
        `}
      >
        Saltar a la navegación
      </a>
    </div>
  );
}
