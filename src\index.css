@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  font-family: 'Lato', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* Light mode styles */
.light body {
  @apply bg-white text-slate-900;
}

/* Dark mode styles */
.dark body {
  @apply bg-[#171821] text-white;
}

/* Global text styling */
.light p, .light span, .light div {
  @apply text-slate-700;
}

.light h1, .light h2, .light h3, .light h4, .light h5, .light h6 {
  @apply font-bold text-slate-900;
}

.dark p, .dark span, .dark div {
  @apply text-gray-100;
}

.dark h1, .dark h2, .dark h3, .dark h4, .dark h5, .dark h6 {
  @apply font-bold text-white;
}

/* Chart number contrast enhancements */
.recharts-pie text {
  font-weight: 700 !important;
  font-size: 16px !important;
  fill: white !important;
}

/* Percentage labels in pie charts */
.percentage-label {
  font-weight: 700;
  font-size: 18px;
  fill: white;
}

/* Light mode theme */
.light {
  --background: #ffffff;
  --card-bg: #f8fafc;
  --card-border: #e2e8f0;
  --pink: #f9769d;
  --blue: #96a7ff;
  --green: #0aefff;
  --text-primary: #1e293b;
  --text-secondary: #64748b;
  --navbar-bg: #ffffff;
  --navbar-border: #e2e8f0;
}

/* Dark mode theme */
.dark {
  --background: #171821;
  --card-bg: #21222d;
  --card-border: #2a2a3d;
  --pink: #f9769d;
  --blue: #96a7ff;
  --green: #0aefff;
  --text-primary: #ffffff;
  --text-secondary: rgba(255, 255, 255, 0.7);
  --navbar-bg: #21222d;
  --navbar-border: #2a2a3d;
}

/* Card styling */
.light .dashboard-card {
  @apply bg-slate-50 rounded-xl p-4 shadow-sm border border-slate-200;
}

.dark .dashboard-card {
  @apply bg-[#21222d] rounded-xl p-4 shadow-lg;
}

/* Progress indicators */
.progress-circle {
  @apply rounded-full overflow-hidden relative;
}

.progress-circle--income {
  stroke: var(--pink);
}

.progress-circle--expense {
  stroke: var(--blue);
}

.progress-circle--bonus {
  stroke: var(--blue);
}

/* Stat cards */
.stat-card {
  @apply flex flex-col items-start;
}

.stat-value {
  @apply text-2xl font-bold text-white;
}

.stat-label {
  @apply text-sm text-gray-400;
}

/* Chart enhancements */
.recharts-cartesian-grid-horizontal line,
.recharts-cartesian-grid-vertical line {
  stroke: rgba(255, 255, 255, 0.1);
}

.recharts-text {
  fill: white !important;
  font-weight: 500 !important;
  font-size: 12px !important;
}

/* Better tooltip contrast */
.recharts-tooltip-wrapper .recharts-default-tooltip {
  background-color: #21222d !important;
  border-color: #2e2f3e !important;
  border-radius: 8px !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.25) !important;
}

.recharts-tooltip-wrapper .recharts-default-tooltip .recharts-tooltip-label,
.recharts-tooltip-wrapper .recharts-default-tooltip .recharts-tooltip-item-list .recharts-tooltip-item {
  color: white !important;
  font-weight: 600 !important;
}

/* Enhanced legend readability */
.recharts-legend-item-text {
  color: white !important;
  font-weight: 500 !important;
}

/* Chart transitions */
.recharts-pie-sector {
  transition: opacity 300ms;
}

.recharts-pie-sector:hover {
  opacity: 0.8;
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.animate-fadeIn {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes scaleIn {
  from { 
    transform: scale(0.95); 
    opacity: 0; 
  }
  to { 
    transform: scale(1); 
    opacity: 1; 
  }
}

.animate-scaleIn {
  animation: scaleIn 0.3s ease-out;
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

.animate-slideInRight {
  animation: slideInRight 0.3s ease-out;
}

/* Custom dashboard header */
.dashboard-header {
  @apply flex justify-between items-center mb-6;
}

.search-bar {
  @apply bg-[#21222d] rounded-full px-4 py-2 flex items-center;
}

.search-bar input {
  @apply bg-transparent border-none outline-none text-white placeholder-gray-500 w-full;
}

/* Modern pill buttons */
.modern-pill {
  @apply px-4 py-2 rounded-full text-sm font-medium transition-all duration-300;
}

.modern-pill.selected {
  @apply bg-[#f9769d] text-white font-bold shadow-md;
}

.dark .modern-pill:not(.selected) {
  @apply bg-[#21222d] text-gray-300 hover:bg-[#2a2b38];
}

.light .modern-pill:not(.selected) {
  @apply bg-slate-100 text-slate-600 hover:bg-slate-200 border border-slate-200;
}

/* Dashboard stats */
.dashboard-stats {
  @apply grid grid-cols-3 gap-4 mb-6;
}

.dark .stat-card {
  @apply bg-[#21222d] rounded-xl p-4 relative overflow-hidden;
}

.light .stat-card {
  @apply bg-slate-50 rounded-xl p-4 relative overflow-hidden border border-slate-200 shadow-sm;
}

.stat-card__progress {
  @apply absolute right-4 top-1/2 -translate-y-1/2 w-12 h-12;
}

/* Improved card layouts */
.card-grid {
  @apply grid grid-cols-1 lg:grid-cols-3 gap-6;
}

/* Chart legends */
.chart-legend {
  @apply flex space-x-4 justify-center mt-4;
}

.legend-item {
  @apply flex items-center;
}

.legend-color {
  @apply w-3 h-3 rounded-full mr-2;
}

.legend-label {
  @apply text-sm text-gray-300 font-medium;
}