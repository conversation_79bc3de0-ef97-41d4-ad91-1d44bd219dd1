@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  font-family: 'Lato', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* Light mode styles */
.light body {
  @apply bg-white text-slate-900;
}

/* Dark mode styles */
.dark body {
  @apply bg-[#171821] text-white;
}

/* Global text styling */
.light p, .light span, .light div {
  @apply text-slate-700;
}

.light h1, .light h2, .light h3, .light h4, .light h5, .light h6 {
  @apply font-bold text-slate-900;
}

.dark p, .dark span, .dark div {
  @apply text-gray-100;
}

.dark h1, .dark h2, .dark h3, .dark h4, .dark h5, .dark h6 {
  @apply font-bold text-white;
}

/* Chart number contrast enhancements */
.recharts-pie text {
  font-weight: 700 !important;
  font-size: 16px !important;
  fill: white !important;
}

/* Percentage labels in pie charts */
.percentage-label {
  font-weight: 700;
  font-size: 18px;
  fill: white;
}

/* Light mode theme */
.light {
  --background: #ffffff;
  --card-bg: #f8fafc;
  --card-border: #e2e8f0;
  --pink: #f9769d;
  --blue: #96a7ff;
  --green: #0aefff;
  --text-primary: #1e293b;
  --text-secondary: #64748b;
  --navbar-bg: #ffffff;
  --navbar-border: #e2e8f0;
}

/* Dark mode theme */
.dark {
  --background: #171821;
  --card-bg: #21222d;
  --card-border: #2a2a3d;
  --pink: #f9769d;
  --blue: #96a7ff;
  --green: #0aefff;
  --text-primary: #ffffff;
  --text-secondary: rgba(255, 255, 255, 0.7);
  --navbar-bg: #21222d;
  --navbar-border: #2a2a3d;
}

/* Card styling */
.light .dashboard-card {
  @apply bg-slate-50 rounded-xl p-4 shadow-sm border border-slate-200;
}

.dark .dashboard-card {
  @apply bg-[#21222d] rounded-xl p-4 shadow-lg;
}

/* Progress indicators */
.progress-circle {
  @apply rounded-full overflow-hidden relative;
}

.progress-circle--income {
  stroke: var(--pink);
}

.progress-circle--expense {
  stroke: var(--blue);
}

.progress-circle--bonus {
  stroke: var(--blue);
}

/* Stat cards */
.stat-card {
  @apply flex flex-col items-start;
}

.stat-value {
  @apply text-2xl font-bold text-white;
}

.stat-label {
  @apply text-sm text-gray-400;
}

/* Chart enhancements */
.recharts-cartesian-grid-horizontal line,
.recharts-cartesian-grid-vertical line {
  stroke: rgba(255, 255, 255, 0.1);
}

.recharts-text {
  fill: white !important;
  font-weight: 500 !important;
  font-size: 12px !important;
}

/* Better tooltip contrast */
.recharts-tooltip-wrapper .recharts-default-tooltip {
  background-color: #21222d !important;
  border-color: #2e2f3e !important;
  border-radius: 8px !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.25) !important;
}

.recharts-tooltip-wrapper .recharts-default-tooltip .recharts-tooltip-label,
.recharts-tooltip-wrapper .recharts-default-tooltip .recharts-tooltip-item-list .recharts-tooltip-item {
  color: white !important;
  font-weight: 600 !important;
}

/* Enhanced legend readability */
.recharts-legend-item-text {
  color: white !important;
  font-weight: 500 !important;
}

/* Chart transitions */
.recharts-pie-sector {
  transition: opacity 300ms;
}

.recharts-pie-sector:hover {
  opacity: 0.8;
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.animate-fadeIn {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes scaleIn {
  from { 
    transform: scale(0.95); 
    opacity: 0; 
  }
  to { 
    transform: scale(1); 
    opacity: 1; 
  }
}

.animate-scaleIn {
  animation: scaleIn 0.3s ease-out;
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

.animate-slideInRight {
  animation: slideInRight 0.3s ease-out;
}

/* Custom dashboard header */
.dashboard-header {
  @apply flex justify-between items-center mb-6;
}

.search-bar {
  @apply bg-[#21222d] rounded-full px-4 py-2 flex items-center;
}

.search-bar input {
  @apply bg-transparent border-none outline-none text-white placeholder-gray-500 w-full;
}

/* Modern pill buttons */
.modern-pill {
  @apply px-4 py-2 rounded-full text-sm font-medium transition-all duration-300;
}

.modern-pill.selected {
  @apply bg-[#f9769d] text-white font-bold shadow-md;
}

.dark .modern-pill:not(.selected) {
  @apply bg-[#21222d] text-gray-300 hover:bg-[#2a2b38];
}

.light .modern-pill:not(.selected) {
  @apply bg-slate-100 text-slate-600 hover:bg-slate-200 border border-slate-200;
}

/* Dashboard stats */
.dashboard-stats {
  @apply grid grid-cols-3 gap-4 mb-6;
}

.dark .stat-card {
  @apply bg-[#21222d] rounded-xl p-4 relative overflow-hidden;
}

.light .stat-card {
  @apply bg-slate-50 rounded-xl p-4 relative overflow-hidden border border-slate-200 shadow-sm;
}

.stat-card__progress {
  @apply absolute right-4 top-1/2 -translate-y-1/2 w-12 h-12;
}

/* Improved card layouts */
.card-grid {
  @apply grid grid-cols-1 lg:grid-cols-3 gap-6;
}

/* Chart legends */
.chart-legend {
  @apply flex space-x-4 justify-center mt-4;
}

.legend-item {
  @apply flex items-center;
}

.legend-color {
  @apply w-3 h-3 rounded-full mr-2;
}

.legend-label {
  @apply text-sm text-gray-300 font-medium;
}

/* Additional Animations */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0,0,0);
  }
  40%, 43% {
    transform: translate3d(0, -8px, 0);
  }
  70% {
    transform: translate3d(0, -4px, 0);
  }
  90% {
    transform: translate3d(0, -2px, 0);
  }
}

@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }
  10%, 30%, 50%, 70%, 90% {
    transform: translateX(-2px);
  }
  20%, 40%, 60%, 80% {
    transform: translateX(2px);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes wiggle {
  0%, 7% {
    transform: rotateZ(0);
  }
  15% {
    transform: rotateZ(-15deg);
  }
  20% {
    transform: rotateZ(10deg);
  }
  25% {
    transform: rotateZ(-10deg);
  }
  30% {
    transform: rotateZ(6deg);
  }
  35% {
    transform: rotateZ(-4deg);
  }
  40%, 100% {
    transform: rotateZ(0);
  }
}

/* Animation Classes */
.animate-slideInUp {
  animation: slideInUp 0.4s ease-out;
}

.animate-slideInDown {
  animation: slideInDown 0.4s ease-out;
}

.animate-slideInLeft {
  animation: slideInLeft 0.4s ease-out;
}

.animate-bounce {
  animation: bounce 1s infinite;
}

.animate-shake {
  animation: shake 0.5s ease-in-out;
}

.animate-pulse-custom {
  animation: pulse 2s infinite;
}

.animate-wiggle {
  animation: wiggle 1s ease-in-out;
}

/* Hover Effects */
.hover-lift {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.hover-lift:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.hover-scale {
  transition: transform 0.2s ease;
}

.hover-scale:hover {
  transform: scale(1.02);
}

.hover-glow {
  transition: box-shadow 0.3s ease;
}

.hover-glow:hover {
  box-shadow: 0 0 20px rgba(249, 118, 157, 0.3);
}

/* Loading States */
.skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

.dark .skeleton {
  background: linear-gradient(90deg, #2a2a3d 25%, #3a3a4d 50%, #2a2a3d 75%);
  background-size: 200% 100%;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Microinteractions */
.button-press {
  transition: transform 0.1s ease;
}

.button-press:active {
  transform: scale(0.98);
}

.ripple {
  position: relative;
  overflow: hidden;
}

.ripple::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transform: translate(-50%, -50%);
  transition: width 0.6s, height 0.6s;
}

.ripple:active::before {
  width: 300px;
  height: 300px;
}

/* Progress bar animation for notifications */
@keyframes shrink {
  from {
    width: 100%;
  }
  to {
    width: 0%;
  }
}

/* Accessibility Styles */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.focus-within\:not-sr-only:focus-within {
  position: static;
  width: auto;
  height: auto;
  padding: inherit;
  margin: inherit;
  overflow: visible;
  clip: auto;
  white-space: normal;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .button-primary {
    border: 2px solid;
  }

  .card {
    border: 2px solid;
  }

  .input {
    border: 2px solid;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* Focus visible styles */
.focus-visible:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Skip links */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: #3b82f6;
  color: white;
  padding: 8px;
  text-decoration: none;
  border-radius: 4px;
  z-index: 1000;
}

.skip-link:focus {
  top: 6px;
}

/* Improved focus indicators */
button:focus-visible,
input:focus-visible,
select:focus-visible,
textarea:focus-visible,
a:focus-visible {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Better color contrast for dark mode */
.dark {
  --text-contrast-high: #ffffff;
  --text-contrast-medium: #e5e7eb;
  --text-contrast-low: #9ca3af;
  --border-contrast: #4b5563;
}

.light {
  --text-contrast-high: #111827;
  --text-contrast-medium: #374151;
  --text-contrast-low: #6b7280;
  --border-contrast: #d1d5db;
}