/* Responsive Design Avanzado - Optimizaciones para tablets y ultra-wide */

/* Tablets en orientación landscape (1024px - 1366px) */
@media (min-width: 1024px) and (max-width: 1366px) {
  /* Dashboard optimizado para tablets */
  .dashboard-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }
  
  .dashboard-stats {
    grid-template-columns: repeat(4, 1fr);
  }
  
  /* Formularios más compactos */
  .form-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  /* Navegación optimizada */
  .navbar-mobile {
    display: none;
  }
  
  .navbar-desktop {
    display: flex;
  }
  
  /* Cards más pequeñas pero legibles */
  .card-compact {
    padding: 1rem;
  }
  
  .card-compact h3 {
    font-size: 1.1rem;
  }
  
  /* Tablas responsivas */
  .table-responsive {
    font-size: 0.9rem;
  }
  
  .table-responsive th,
  .table-responsive td {
    padding: 0.75rem 0.5rem;
  }
}

/* Pantallas ultra-wide (1920px+) */
@media (min-width: 1920px) {
  /* Container principal más ancho pero limitado */
  .main-container {
    max-width: 1800px;
    margin: 0 auto;
    padding: 0 2rem;
  }
  
  /* Dashboard en 3 columnas */
  .dashboard-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
  }
  
  /* Stats en una sola fila */
  .dashboard-stats {
    grid-template-columns: repeat(6, 1fr);
  }
  
  /* Sidebar más ancho */
  .sidebar-wide {
    width: 280px;
  }
  
  /* Formularios en 3 columnas */
  .form-grid-wide {
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
  }
  
  /* Gráficos más grandes */
  .chart-container-wide {
    height: 500px;
  }
  
  /* Tablas con más columnas visibles */
  .table-wide {
    font-size: 1rem;
  }
  
  .table-wide th,
  .table-wide td {
    padding: 1rem;
  }
  
  /* Cards en grid más denso */
  .cards-grid-wide {
    grid-template-columns: repeat(4, 1fr);
    gap: 1.5rem;
  }
}

/* Pantallas 4K (2560px+) */
@media (min-width: 2560px) {
  /* Container aún más ancho */
  .main-container {
    max-width: 2400px;
  }
  
  /* Dashboard en 4 columnas */
  .dashboard-grid {
    grid-template-columns: repeat(4, 1fr);
  }
  
  /* Texto más grande para legibilidad */
  .text-4k {
    font-size: 1.1rem;
  }
  
  .heading-4k {
    font-size: 2.5rem;
  }
  
  /* Cards más grandes */
  .card-4k {
    padding: 2rem;
  }
  
  /* Gráficos extra grandes */
  .chart-4k {
    height: 600px;
  }
}

/* Tablets en orientación portrait (768px - 1023px) */
@media (min-width: 768px) and (max-width: 1023px) {
  /* Dashboard en 2 columnas */
  .dashboard-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  /* Stats en 2 filas */
  .dashboard-stats {
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }
  
  /* Formularios optimizados */
  .form-tablet {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  /* Navegación híbrida */
  .navbar-tablet {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  /* Modales más grandes */
  .modal-tablet {
    width: 90%;
    max-width: 600px;
  }
  
  /* Botones más grandes para touch */
  .btn-tablet {
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
  }
}

/* Optimizaciones para pantallas táctiles */
@media (hover: none) and (pointer: coarse) {
  /* Botones más grandes */
  .btn-touch {
    min-height: 44px;
    min-width: 44px;
    padding: 0.75rem 1rem;
  }
  
  /* Enlaces más espaciados */
  .nav-touch a {
    padding: 1rem;
    margin: 0.25rem 0;
  }
  
  /* Inputs más grandes */
  .input-touch {
    padding: 1rem;
    font-size: 1rem;
  }
  
  /* Hover states deshabilitados */
  .no-hover:hover {
    transform: none;
    box-shadow: none;
  }
}

/* Optimizaciones para pantallas de alta densidad */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  /* Iconos más nítidos */
  .icon-hd {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
  
  /* Bordes más finos */
  .border-hd {
    border-width: 0.5px;
  }
  
  /* Sombras más suaves */
  .shadow-hd {
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }
}

/* Modo landscape para móviles */
@media (max-width: 767px) and (orientation: landscape) {
  /* Header más compacto */
  .header-landscape {
    height: 60px;
    padding: 0.5rem 1rem;
  }
  
  /* Navegación horizontal */
  .nav-landscape {
    flex-direction: row;
    justify-content: space-around;
    padding: 0.5rem;
  }
  
  /* Contenido principal ajustado */
  .main-landscape {
    padding: 1rem;
    height: calc(100vh - 120px);
    overflow-y: auto;
  }
  
  /* Modales más pequeños */
  .modal-landscape {
    width: 95%;
    max-height: 80vh;
    overflow-y: auto;
  }
}

/* Optimizaciones para impresión */
@media print {
  /* Ocultar elementos no necesarios */
  .no-print {
    display: none !important;
  }
  
  /* Colores para impresión */
  .print-friendly {
    background: white !important;
    color: black !important;
    box-shadow: none !important;
  }
  
  /* Tablas optimizadas */
  .table-print {
    border-collapse: collapse;
    width: 100%;
  }
  
  .table-print th,
  .table-print td {
    border: 1px solid #000;
    padding: 0.5rem;
    font-size: 0.9rem;
  }
  
  /* Gráficos como imágenes */
  .chart-print {
    page-break-inside: avoid;
  }
  
  /* Saltos de página */
  .page-break {
    page-break-before: always;
  }
}

/* Animaciones reducidas para usuarios que prefieren menos movimiento */
@media (prefers-reduced-motion: reduce) {
  .reduce-motion * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Alto contraste para accesibilidad */
@media (prefers-contrast: high) {
  .high-contrast {
    border: 2px solid;
    background: white;
    color: black;
  }
  
  .high-contrast-button {
    border: 3px solid black;
    background: white;
    color: black;
  }
  
  .high-contrast-button:hover {
    background: black;
    color: white;
  }
}

/* Tema oscuro del sistema */
@media (prefers-color-scheme: dark) {
  .system-dark {
    background-color: #1a1a1a;
    color: #ffffff;
  }
  
  .system-dark .card {
    background-color: #2d2d2d;
    border-color: #404040;
  }
  
  .system-dark .input {
    background-color: #2d2d2d;
    border-color: #404040;
    color: #ffffff;
  }
}

/* Utilidades responsive */
.container-responsive {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

@media (min-width: 640px) {
  .container-responsive {
    padding: 0 1.5rem;
  }
}

@media (min-width: 1024px) {
  .container-responsive {
    padding: 0 2rem;
  }
}

@media (min-width: 1920px) {
  .container-responsive {
    max-width: 1800px;
    padding: 0 3rem;
  }
}

/* Grid responsive avanzado */
.grid-responsive {
  display: grid;
  gap: 1rem;
  grid-template-columns: 1fr;
}

@media (min-width: 640px) {
  .grid-responsive {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .grid-responsive {
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
  }
}

@media (min-width: 1920px) {
  .grid-responsive {
    grid-template-columns: repeat(4, 1fr);
  }
}
