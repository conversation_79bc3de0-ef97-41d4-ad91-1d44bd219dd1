import { useState, useEffect } from 'react';
import { Card } from './ui/Card';
import { 
  HeartHandshake, CreditCard, Wallet, CalendarClock, Sparkles, 
  DollarSign, Gem, PartyPopper, Plane, Home, Heart, Briefcase, 
  Sailboat, GraduationCap, Target, Check, ShieldCheck, PiggyBank,
  Plus, ChevronDown, ChevronUp, BarChart, RefreshCw, Info,
  TrendingUp, Award, Zap, HelpCircle, Star, Compass, Palmtree,
  Edit, X, Trash2, BookOpen, LifeBuoy, ArrowRightLeft, Bell,
  Search, Coffee, CircleDollarSign, Download, FileText, Brain, 
  Timer, Clock, CloudOff, User, Users, Laptop, AlarmClock, PlusCircle, AlertCircle
} from 'lucide-react';
import { useTheme } from '../lib/ThemeContext';
import {
  GastoFeliz, NoPrioridad, DistribucionGastos,
  GastoReal, Alerta
} from '../lib/types';

// Mock datos de presupuesto mensual
const mockPresupuestoMensual = {
  ingresos: 5000,
  gastos: 3500,
  categorias: [
    { id: '1', nombre: 'Vivienda', monto: 850 },
    { id: '2', nombre: 'Alimentación', monto: 600 },
    { id: '3', nombre: 'Transporte', monto: 200 },
    { id: '4', nombre: 'Entretenimiento', monto: 350 },
    { id: '5', nombre: 'Educación', monto: 280 },
    { id: '6', nombre: 'Salud', monto: 120 },
    { id: '7', nombre: 'Ropa', monto: 150 },
    { id: '8', nombre: 'Restaurantes', monto: 200 },
    { id: '9', nombre: 'Suscripciones', monto: 45 },
    { id: '10', nombre: 'Clubes sociales', monto: 80 },
    { id: '11', nombre: 'Vacaciones', monto: 100 },
    { id: '12', nombre: 'Cursos de desarrollo personal', monto: 65 },
  ]
};

// Datos históricos para la tendencia
const historicalData = [
  { mes: 'Enero', vidaRica: 38, neutros: 32, noPrioritarios: 30 },
  { mes: 'Febrero', vidaRica: 35, neutros: 30, noPrioritarios: 35 },
  { mes: 'Marzo', vidaRica: 40, neutros: 28, noPrioritarios: 32 },
  { mes: 'Abril', vidaRica: 42, neutros: 26, noPrioritarios: 32 },
];

export function VidaRicaPage() {
  const { theme } = useTheme();
  const usuario = "Carlos";
  
  // Estados para la sección 1: Mi Definición de Vida Rica
  const [definicionVidaRica, setDefinicionVidaRica] = useState(
    "Poder irme a la playa cuando quiera, elegir mis proyectos sin presión, vivir sin estrés financiero y darle a mis hijas todo lo que necesitan sin mirar la cuenta."
  );
  const [editandoDefinicion, setEditandoDefinicion] = useState(false);
  
  // Estados para la sección 2: Mis Gastos Felices
  const [gastosfelices, setGastosFelices] = useState<GastoFeliz[]>([
    { 
      id: '1', 
      categoria: 'Escapadas a la playa', 
      presupuesto: 250,
      gastado: 100, 
      icono: <Palmtree className="w-5 h-5" /> 
    },
    { 
      id: '2', 
      categoria: 'Educación de mis hijas', 
      presupuesto: 350, 
      gastado: 300,
      icono: <GraduationCap className="w-5 h-5" /> 
    },
    { 
      id: '3', 
      categoria: 'Experiencias familiares', 
      presupuesto: 150,
      gastado: 120, 
      icono: <Users className="w-5 h-5" /> 
    },
    { 
      id: '4', 
      categoria: 'Herramientas de automatización IA', 
      presupuesto: 100,
      gastado: 75, 
      icono: <Laptop className="w-5 h-5" /> 
    },
    { 
      id: '5', 
      categoria: 'Inversión en productos propios', 
      presupuesto: 200,
      gastado: 125, 
      icono: <Briefcase className="w-5 h-5" /> 
    },
  ]);
  
  const [nuevaCategoria, setNuevaCategoria] = useState({
    nombre: '',
    presupuesto: ''
  });
  const [mostrarFormCategoria, setMostrarFormCategoria] = useState(false);
  
  // Estados para la sección 3: Mis Gastos que no me aportan
  const [noPrioridades, setNoPrioridades] = useState<NoPrioridad[]>([
    { id: '1', descripcion: 'Comer fuera sin propósito', checked: true },
    { id: '2', descripcion: 'Apps que no uso', checked: true },
    { id: '3', descripcion: 'Ropa innecesaria', checked: true },
    { id: '4', descripcion: 'Regalos por compromiso', checked: true },
    { id: '5', descripcion: 'Clubes sociales que no uso', checked: true },
  ]);
  
  const [nuevaNoPrioridad, setNuevaNoPrioridad] = useState('');
  const [mostrarFormNoPrioridad, setMostrarFormNoPrioridad] = useState(false);
  
  // Estados para la sección 4: Inteligencia Emocional del Dinero
  const [presupuestoTotal, setPresupuestoTotal] = useState(mockPresupuestoMensual.gastos);
  const [distribucionGastos, setDistribucionGastos] = useState<DistribucionGastos>({
    felices: 42,
    neutrales: 26,
    noDeseados: 32
  });
  
  // Nuevos estados para las secciones de Motivación y Legado y Visión de Futuro
  const [motivacionLegado, setMotivacionLegado] = useState({
    orgullo: "Crear un negocio con propósito y hacerlo funcionar",
    miedo: "Que me pase algo y no deje nada a mis hijas",
    legado: "Que aprendan a invertir y generar su propio dinero"
  });

  const [visionFuturo, setVisionFuturo] = useState([
    { id: '1', descripcion: 'Casa off-grid', icono: <Home className="w-5 h-5" /> },
    { id: '2', descripcion: 'Negocio en piloto automático', icono: <RefreshCw className="w-5 h-5" /> },
    { id: '3', descripcion: 'Tiempo libre diario con hijas', icono: <Users className="w-5 h-5" /> },
    { id: '4', descripcion: 'Viajes cada 2 meses sin revisar la cuenta', icono: <Plane className="w-5 h-5" /> },
  ]);
  
  // Nuevo estado para el mes anterior (para comparación)
  const [mesAnterior, setMesAnterior] = useState({
    vidaRica: 40,
    neutros: 28, 
    noPrioritarios: 32
  });
  
  // Estado para los gastos reales extraídos del presupuesto general
  const [gastosReales, setGastosReales] = useState<GastoReal[]>([]);
  
  // Estado para mostrar alertas y recomendaciones
  const [alertas, setAlertas] = useState<Alerta[]>([]);
  
  // Estado para el modal de reasignación de presupuesto
  const [mostrarModalReasignacion, setMostrarModalReasignacion] = useState(false);
  const [reasignacionEnProceso, setReasignacionEnProceso] = useState({
    desde: '',
    hacia: '',
    monto: ''
  });
  
  const [mostrarRecomendaciones, setMostrarRecomendaciones] = useState(false);
  
  // Calcular totales para gastos felices
  const totalPresupuesto = gastosfelices.reduce((sum, gasto) => sum + gasto.presupuesto, 0);
  const totalGastado = gastosfelices.reduce((sum, gasto) => sum + (gasto.gastado || 0), 0);
  const porcentajeGastado = Math.round((totalGastado / totalPresupuesto) * 100);
  
  // Calcular el porcentaje del presupuesto total dedicado a gastos felices
  const porcentajeGastosFelices = Math.round((totalGastado / presupuestoTotal) * 100);
  
  // Mensaje automático para gastos felices
  const getMensajeGastosFelices = () => {
    if (totalGastado <= totalPresupuesto) {
      return `✅ Gastaste $${totalGastado} de tus $${totalPresupuesto} felices este mes. ¡Disfrútalo sin culpa!`;
    } else {
      return `⚠️ Has excedido tu presupuesto de gastos felices por $${totalGastado - totalPresupuesto}. Revisa si hay algún ajuste que puedas hacer.`;
    }
  };
  
  // Mensaje para la inteligencia emocional del dinero
  const getMensajeEmocional = () => {
    const tendencia = distribucionGastos.felices - mesAnterior.vidaRica;
    
    if (tendencia > 0) {
      return `✅ ¡Genial! Este mes viviste un ${distribucionGastos.felices}% alineado a tu Vida Rica, un ${tendencia}% mejor que el mes anterior.`;
    } else if (tendencia === 0) {
      return `🎯 Carlos, este mes viviste un ${distribucionGastos.felices}% alineado a tu Vida Rica, igual que el mes anterior.`;
    } else {
      return `❌ Este mes viviste un ${distribucionGastos.felices}% alineado a tu Vida Rica, un ${Math.abs(tendencia)}% menos que el mes anterior. Ajustemos tu estrategia.`;
    }
  };
  
  // Inicializar datos de presupuesto
  useEffect(() => {
    // Simular carga de datos del presupuesto general
    const obtenerDatosPresupuesto = () => {
      // En un caso real, estos datos vendrían de la base de datos o de otro componente
      const gastosRealesMock: GastoReal[] = [
        { id: '1', categoria: 'Vivienda', monto: 850, fecha: '2023-04-01' },
        { id: '2', categoria: 'Alimentación', monto: 600, fecha: '2023-04-05' },
        { id: '3', categoria: 'Educación', monto: 280, fecha: '2023-04-10' },
        { id: '4', categoria: 'Restaurantes', monto: 200, fecha: '2023-04-15' },
        { id: '5', categoria: 'Vacaciones', monto: 100, fecha: '2023-04-20' },
        { id: '6', categoria: 'Clubes sociales', monto: 80, fecha: '2023-04-25' },
        { id: '7', categoria: 'Ropa', monto: 150, fecha: '2023-04-12' },
        { id: '8', categoria: 'Suscripciones', monto: 45, fecha: '2023-04-05' },
      ];
      
      setGastosReales(gastosRealesMock);
      
      // Actualizar gastos felices con datos reales
      const gastosActualizados = gastosfelices.map(gastoFeliz => {
        // Intentar hacer una correspondencia aproximada entre categorías
        let gastadoReal = 0;
        
        if (gastoFeliz.categoria.includes('playa') || gastoFeliz.categoria.includes('Vacaciones')) {
          gastadoReal = gastosRealesMock.find(g => g.categoria === 'Vacaciones')?.monto || 0;
        } else if (gastoFeliz.categoria.includes('Educación') || gastoFeliz.categoria.includes('hijas')) {
          gastadoReal = gastosRealesMock.find(g => g.categoria === 'Educación')?.monto || 0;
        } else if (gastoFeliz.categoria.includes('familiar')) {
          gastadoReal = (gastosRealesMock.find(g => g.categoria === 'Restaurantes')?.monto || 0) * 0.5;
        }
        
        return {
          ...gastoFeliz,
          gastado: gastadoReal > 0 ? gastadoReal : gastoFeliz.gastado
        };
      });
      
      setGastosFelices(gastosActualizados);
      
      // Actualizar distribución de gastos basado en datos reales
      const totalGastadoFeliz = gastosActualizados.reduce((sum, gasto) => sum + (gasto.gastado || 0), 0);
      const porcentajeFeliz = Math.round((totalGastadoFeliz / mockPresupuestoMensual.gastos) * 100);
      
      // Detectar gastos en no-prioridades
      const gastosNoDeseados = detectarGastosNoPrioridad(gastosRealesMock, noPrioridades);
      const porcentajeNoDeseados = Math.round((gastosNoDeseados / mockPresupuestoMensual.gastos) * 100);
      
      setDistribucionGastos({
        felices: porcentajeFeliz,
        noDeseados: porcentajeNoDeseados,
        neutrales: 100 - porcentajeFeliz - porcentajeNoDeseados
      });
    };
    
    obtenerDatosPresupuesto();
  }, []);
  
  // Efecto para generar alertas basadas en la comparación de datos
  useEffect(() => {
    // Función para generar alertas
    const generarAlertas = () => {
      const nuevasAlertas: Alerta[] = [];
      
      // 1. Detectar gastos felices subutilizados (menos del 50% gastado)
      const categoriasSubutilizadas = gastosfelices.filter(
        gasto => (gasto.gastado || 0) < gasto.presupuesto * 0.5
      );
      
      if (categoriasSubutilizadas.length > 0) {
        const categorias = categoriasSubutilizadas.map(g => g.categoria.split(' ')[0]).join(' o ');
        nuevasAlertas.push({
          id: 'subutilizado-1',
          tipo: 'subutilizado',
          mensaje: `🟡 Te queda presupuesto para disfrutar más en lo que amas: ${categorias}.`,
          categoria: categorias,
          icono: <Sparkles className="w-5 h-5" />
        });
      }
      
      // 2. Detectar gastos en categorías marcadas como no-prioridad
      const gastoEnNoPrioridades = detectarGastosNoPrioridadConDetalles(gastosReales, noPrioridades);
      
      if (gastoEnNoPrioridades.total > 20) {
        nuevasAlertas.push({
          id: 'noPrioridad-1',
          tipo: 'noPrioridad',
          mensaje: `⚠️ Este mes gastaste $${gastoEnNoPrioridades.total} en cosas que tú mismo dijiste que no te aportan valor: ${gastoEnNoPrioridades.categorias.join(', ')}.`,
          icono: <AlertCircle className="w-5 h-5" />,
          accion: 'revisar'
        });
      }
      
      // 3. Alerta por porcentaje de gastos felices bajo
      if (distribucionGastos.felices < 30) {
        nuevasAlertas.push({
          id: 'porcentajeBajo-1',
          tipo: 'porcentajeBajo',
          mensaje: `🎯 Estás usando el ${distribucionGastos.felices}% de tu dinero en lo que amas. El ideal es 30% o más.`,
          icono: <Target className="w-5 h-5" />,
          accion: 'reasignar'
        });
      }
      
      // 4. Recomendaciones personalizadas
      if (nuevasAlertas.length === 0) {
        nuevasAlertas.push({
          id: 'success-1',
          tipo: 'success',
          mensaje: `✨ ¡Excelente ${usuario}! Estás alineando tus gastos con tu vida rica.`,
          icono: <Check className="w-5 h-5" />
        });
      } else {
        nuevasAlertas.push({
          id: 'recomendacion-1',
          tipo: 'recomendacion',
          mensaje: "Reasigna parte de tu presupuesto desde gastos no deseados hacia los que realmente disfrutas.",
          icono: <ArrowRightLeft className="w-5 h-5" />,
          accion: 'reasignar'
        });
      }
      
      setAlertas(nuevasAlertas);
    };
    
    generarAlertas();
  }, [gastosReales, gastosfelices, noPrioridades, distribucionGastos.felices]);
  
  // Función para detectar gastos en categorías marcadas como no-prioridad
  const detectarGastosNoPrioridad = (gastos: GastoReal[], noPrioridades: NoPrioridad[]): number => {
    let total = 0;
    
    // Mapeo entre categorías de gastos y descripciones de no-prioridades
    const mapeos = [
      { gasto: 'Restaurantes', noPrioridad: 'Comer fuera sin propósito' },
      { gasto: 'Ropa', noPrioridad: 'Ropa innecesaria' },
      { gasto: 'Suscripciones', noPrioridad: 'Apps que no uso' },
      { gasto: 'Clubes sociales', noPrioridad: 'Clubes sociales que no uso' },
    ];
    
    gastos.forEach(gasto => {
      const mapeo = mapeos.find(m => gasto.categoria === m.gasto);
      if (mapeo) {
        const esNoPrioridad = noPrioridades.find(np => 
          np.descripcion === mapeo.noPrioridad && np.checked
        );
        
        if (esNoPrioridad) {
          total += gasto.monto;
        }
      }
    });
    
    return total;
  };
  
  // Función para detectar gastos en categorías marcadas como no-prioridad con detalles
  const detectarGastosNoPrioridadConDetalles = (gastos: GastoReal[], noPrioridades: NoPrioridad[]) => {
    let total = 0;
    const categorias: string[] = [];
    
    // Mapeo entre categorías de gastos y descripciones de no-prioridades
    const mapeos = [
      { gasto: 'Restaurantes', noPrioridad: 'Comer fuera sin propósito' },
      { gasto: 'Ropa', noPrioridad: 'Ropa innecesaria' },
      { gasto: 'Suscripciones', noPrioridad: 'Apps que no uso' },
      { gasto: 'Clubes sociales', noPrioridad: 'Clubes sociales que no uso' },
    ];
    
    gastos.forEach(gasto => {
      const mapeo = mapeos.find(m => gasto.categoria === m.gasto);
      if (mapeo) {
        const esNoPrioridad = noPrioridades.find(np => 
          np.descripcion === mapeo.noPrioridad && np.checked
        );
        
        if (esNoPrioridad) {
          total += gasto.monto;
          if (!categorias.includes(gasto.categoria)) {
            categorias.push(gasto.categoria);
          }
        }
      }
    });
    
    return { total, categorias };
  };
  
  // Función para actualizar la definición de vida rica
  const actualizarDefinicion = () => {
    // La definición ya se actualiza automáticamente con el estado
    setEditandoDefinicion(false);
  };
  
  // Función para actualizar el presupuesto de un gasto feliz
  const actualizarPresupuesto = (id: string, nuevoPresupuesto: number) => {
    setGastosFelices(gastosfelices.map(gasto => 
      gasto.id === id ? { ...gasto, presupuesto: nuevoPresupuesto } : gasto
    ));
  };
  
  // Función para actualizar el gasto real de un gasto feliz
  const actualizarGastado = (id: string, nuevoGastado: number) => {
    setGastosFelices(gastosfelices.map(gasto => 
      gasto.id === id ? { ...gasto, gastado: nuevoGastado } : gasto
    ));
  };
  
  // Función para agregar nueva categoría de gasto feliz
  const agregarCategoria = () => {
    if (nuevaCategoria.nombre && nuevaCategoria.presupuesto && parseFloat(nuevaCategoria.presupuesto) > 0) {
      const nuevoGasto: GastoFeliz = {
        id: Date.now().toString(),
        categoria: nuevaCategoria.nombre,
        presupuesto: parseFloat(nuevaCategoria.presupuesto),
        gastado: 0,
        icono: <Star className="w-5 h-5" />
      };
      
      setGastosFelices([...gastosfelices, nuevoGasto]);
      setNuevaCategoria({ nombre: '', presupuesto: '' });
      setMostrarFormCategoria(false);
    }
  };
  
  // Función para eliminar categoría de gasto feliz
  const eliminarCategoria = (id: string) => {
    setGastosFelices(gastosfelices.filter(gasto => gasto.id !== id));
  };
  
  // Función para agregar nueva no prioridad
  const agregarNoPrioridad = () => {
    if (nuevaNoPrioridad) {
      const nuevaNoP: NoPrioridad = {
        id: Date.now().toString(),
        descripcion: nuevaNoPrioridad,
        checked: true
      };
      
      setNoPrioridades([...noPrioridades, nuevaNoP]);
      setNuevaNoPrioridad('');
      setMostrarFormNoPrioridad(false);
    }
  };
  
  // Función para cambiar el estado de una no prioridad
  const toggleNoPrioridad = (id: string) => {
    setNoPrioridades(noPrioridades.map(noPrioridad => 
      noPrioridad.id === id ? { ...noPrioridad, checked: !noPrioridad.checked } : noPrioridad
    ));
  };
  
  // Función para eliminar no prioridad
  const eliminarNoPrioridad = (id: string) => {
    setNoPrioridades(noPrioridades.filter(noPrioridad => noPrioridad.id !== id));
  };
  
  // Función para reasignar presupuesto
  const reasignarPresupuesto = () => {
    if (
      reasignacionEnProceso.desde && 
      reasignacionEnProceso.hacia && 
      reasignacionEnProceso.monto && 
      parseFloat(reasignacionEnProceso.monto) > 0
    ) {
      // En un caso real, aquí se actualizaría el presupuesto en la base de datos
      // Por ahora, solo mostramos un mensaje de confirmación
      alert(`Se ha reasignado $${reasignacionEnProceso.monto} desde "${reasignacionEnProceso.desde}" hacia "${reasignacionEnProceso.hacia}"`);
      
      setMostrarModalReasignacion(false);
      setReasignacionEnProceso({
        desde: '',
        hacia: '',
        monto: ''
      });
      
      // Actualizamos localmente los gastos
      const montoReasignado = parseFloat(reasignacionEnProceso.monto);
      
      // Buscar la categoría de gasto feliz para aumentar su presupuesto
      setGastosFelices(gastosfelices.map(gasto => {
        if (gasto.categoria === reasignacionEnProceso.hacia) {
          return { ...gasto, presupuesto: gasto.presupuesto + montoReasignado };
        }
        return gasto;
      }));
      
      // Agregar una nueva alerta de éxito
      setAlertas([
        ...alertas,
        {
          id: `reasignacion-${Date.now()}`,
          tipo: 'success',
          mensaje: `✅ Has reasignado $${montoReasignado} de "${reasignacionEnProceso.desde}" a "${reasignacionEnProceso.hacia}"`,
          icono: <Check className="w-5 h-5" />
        }
      ]);
    }
  };
  
  // Obtener las categorías de gastos no-prioridad para la reasignación
  const obtenerCategoriasNoPrioridad = () => {
    const categorias = [];
    
    // Mapeo entre categorías de gastos y descripciones de no-prioridades
    const mapeos = [
      { gasto: 'Restaurantes', noPrioridad: 'Comer fuera sin propósito' },
      { gasto: 'Ropa', noPrioridad: 'Ropa innecesaria' },
      { gasto: 'Suscripciones', noPrioridad: 'Apps que no uso' },
      { gasto: 'Clubes sociales', noPrioridad: 'Clubes sociales que no uso' },
    ];
    
    noPrioridades.forEach(noPrioridad => {
      if (noPrioridad.checked) {
        const mapeo = mapeos.find(m => m.noPrioridad === noPrioridad.descripcion);
        if (mapeo) {
          categorias.push(mapeo.gasto);
        }
      }
    });
    
    return categorias;
  };
  
  // Función para editar elemento de la visión de futuro
  const editarVisionFuturo = (id: string, nuevaDescripcion: string) => {
    setVisionFuturo(visionFuturo.map(item => 
      item.id === id ? { ...item, descripcion: nuevaDescripcion } : item
    ));
  };
  
  // Función para editar motivación y legado
  const editarMotivacionLegado = (campo: 'orgullo' | 'miedo' | 'legado', nuevoValor: string) => {
    setMotivacionLegado({
      ...motivacionLegado,
      [campo]: nuevoValor
    });
  };
  
  return (
    <div className="space-y-8 pb-20">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6">
        <h1 className="text-3xl font-bold text-white">Mi Vida Rica</h1>
        <div className="flex items-center gap-3">
          <HeartHandshake className="w-6 h-6 text-pink-400" />
          <span className="text-lg text-gray-300 font-medium">Método de Ramit Sethi, FIRE y Dave Ramsey</span>
        </div>
      </div>
      
      {/* Nueva sección: Alertas y sincronización del presupuesto */}
      {alertas.length > 0 && (
        <div className="bg-[#1d1f30] rounded-xl p-5 border border-blue-900/30 animate-fadeIn">
          <div className="flex items-center mb-4">
            <Bell className="w-6 h-6 text-blue-400 mr-2" />
            <h2 className="text-xl font-semibold">Alertas y Recomendaciones</h2>
          </div>
          
          <div className="space-y-3">
            {alertas.map(alerta => (
              <div 
                key={alerta.id}
                className={`p-4 rounded-lg border flex items-start ${
                  alerta.tipo === 'subutilizado' ? 'bg-yellow-900/20 border-yellow-800/30' :
                  alerta.tipo === 'noPrioridad' ? 'bg-red-900/20 border-red-800/30' :
                  alerta.tipo === 'porcentajeBajo' ? 'bg-blue-900/20 border-blue-800/30' :
                  alerta.tipo === 'success' ? 'bg-green-900/20 border-green-800/30' :
                  'bg-purple-900/20 border-purple-800/30'
                }`}
              >
                <div className={`w-8 h-8 rounded-full flex items-center justify-center mr-3 ${
                  alerta.tipo === 'subutilizado' ? 'bg-yellow-800 text-yellow-300' :
                  alerta.tipo === 'noPrioridad' ? 'bg-red-800 text-red-300' :
                  alerta.tipo === 'porcentajeBajo' ? 'bg-blue-800 text-blue-300' :
                  alerta.tipo === 'success' ? 'bg-green-800 text-green-300' :
                  'bg-purple-800 text-purple-300'
                }`}>
                  {alerta.icono}
                </div>
                <div className="flex-1">
                  <p className={`font-medium ${
                    alerta.tipo === 'subutilizado' ? 'text-yellow-300' :
                    alerta.tipo === 'noPrioridad' ? 'text-red-300' :
                    alerta.tipo === 'porcentajeBajo' ? 'text-blue-300' :
                    alerta.tipo === 'success' ? 'text-green-300' :
                    'text-purple-300'
                  }`}>
                    {alerta.mensaje}
                  </p>
                  
                  {alerta.accion === 'reasignar' && (
                    <button
                      onClick={() => setMostrarModalReasignacion(true)}
                      className="mt-2 px-3 py-1.5 rounded-lg bg-purple-800 hover:bg-purple-700 text-white text-sm font-medium flex items-center"
                    >
                      <ArrowRightLeft className="w-4 h-4 mr-1" />
                      Reasignar presupuesto
                    </button>
                  )}
                  
                  {alerta.accion === 'revisar' && (
                    <button
                      onClick={() => {
                        // Ir a la sección de No Prioridades
                        document.getElementById('no-prioridades')?.scrollIntoView({ behavior: 'smooth' });
                      }}
                      className="mt-2 px-3 py-1.5 rounded-lg bg-red-800 hover:bg-red-700 text-white text-sm font-medium flex items-center"
                    >
                      <Search className="w-4 h-4 mr-1" />
                      Revisar no prioridades
                    </button>
                  )}
                </div>
              </div>
            ))}
          </div>
          
          <div className="mt-4 text-sm text-gray-400 flex items-center">
            <RefreshCw className="w-4 h-4 mr-1" />
            Actualizado con tu presupuesto mensual general.
          </div>
        </div>
      )}
      
      {/* Sección 6: Tarjeta Visual Mensual "Vida Rica" */}
      <Card>
        <div className="p-6">
          <div className="flex items-center mb-4">
            <Award className="w-6 h-6 text-amber-400 mr-2" />
            <h2 className="text-xl font-semibold">Tarjeta Mensual Vida Rica</h2>
          </div>
          
          <div className="bg-gradient-to-br from-indigo-900/40 to-purple-900/40 rounded-xl p-6 border border-indigo-800/30">
            <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
              <div className="md:col-span-2 flex flex-col justify-center items-center">
                <div className="relative h-52 w-52">
                  <div className="absolute inset-0 rounded-full bg-gradient-to-br from-blue-500/20 to-purple-500/20 animate-pulse-slow"></div>
                  
                  <svg className="w-full h-full" viewBox="0 0 36 36">
                    {/* Circle for no prioritarios */}
                    <path 
                      d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831" 
                      fill="none" 
                      stroke="#4B5563" 
                      strokeWidth="2"
                    />
                    <path 
                      d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831" 
                      fill="none" 
                      stroke="#EF4444" 
                      strokeWidth="2"
                      strokeDasharray={`${distribucionGastos.noDeseados}, 100`} 
                      strokeLinecap="round"
                    />
                    
                    {/* Circle for neutros */}
                    <path 
                      d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831" 
                      fill="none" 
                      stroke="#4B5563" 
                      strokeWidth="2"
                      strokeDashoffset={`-${distribucionGastos.noDeseados}`}
                    />
                    <path 
                      d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831" 
                      fill="none" 
                      stroke="#3B82F6" 
                      strokeWidth="2"
                      strokeDasharray={`${distribucionGastos.neutrales}, 100`} 
                      strokeDashoffset={`-${distribucionGastos.noDeseados}`}
                      strokeLinecap="round"
                    />
                    
                    {/* Circle for vida rica */}
                    <path 
                      d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831" 
                      fill="none" 
                      stroke="#4B5563" 
                      strokeWidth="2"
                      strokeDashoffset={`-${distribucionGastos.noDeseados + distribucionGastos.neutrales}`}
                    />
                    <path 
                      d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831" 
                      fill="none" 
                      stroke="#10B981" 
                      strokeWidth="2"
                      strokeDasharray={`${distribucionGastos.felices}, 100`} 
                      strokeDashoffset={`-${distribucionGastos.noDeseados + distribucionGastos.neutrales}`}
                      strokeLinecap="round"
                    />
                  </svg>
                  
                  <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-center">
                    <p className="text-white text-sm mb-1">Carlos, este mes viviste</p>
                    <p className="text-3xl font-bold text-green-400">{distribucionGastos.felices}%</p>
                    <p className="text-white text-sm font-medium">alineado a tu Vida Rica</p>
                  </div>
                </div>
                
                <div className="mt-6 grid grid-cols-3 gap-4 w-full">
                  <div className="text-center">
                    <div className="w-3 h-3 bg-green-500 rounded-full mx-auto mb-1"></div>
                    <p className="text-sm font-medium text-green-400">{distribucionGastos.felices}%</p>
                    <p className="text-xs text-gray-400">En lo que amas</p>
                  </div>
                  
                  <div className="text-center">
                    <div className="w-3 h-3 bg-blue-500 rounded-full mx-auto mb-1"></div>
                    <p className="text-sm font-medium text-blue-400">{distribucionGastos.neutrales}%</p>
                    <p className="text-xs text-gray-400">Gastos neutros</p>
                  </div>
                  
                  <div className="text-center">
                    <div className="w-3 h-3 bg-red-500 rounded-full mx-auto mb-1"></div>
                    <p className="text-sm font-medium text-red-400">{distribucionGastos.noDeseados}%</p>
                    <p className="text-xs text-gray-400">No prioritarios</p>
                  </div>
                </div>
              </div>
              
              <div className="md:col-span-3">
                <h3 className="text-lg font-medium mb-4 text-white">Evolución de tu Vida Rica</h3>
                
                <div className="h-[180px] bg-[#1e1f2c] rounded-lg p-4 mb-4">
                  <div className="h-full w-full flex items-end justify-between gap-1">
                    {historicalData.map((month, index) => (
                      <div key={index} className="flex-1 flex flex-col items-center">
                        <div className="w-full flex flex-col-reverse h-[140px]">
                          <div 
                            className="w-full bg-red-500" 
                            style={{height: `${month.noPrioritarios}%`}}
                          ></div>
                          <div 
                            className="w-full bg-blue-500" 
                            style={{height: `${month.neutros}%`}}
                          ></div>
                          <div 
                            className="w-full bg-green-500" 
                            style={{height: `${month.vidaRica}%`}}
                          ></div>
                        </div>
                        <span className="text-xs text-gray-400 mt-1">{month.mes}</span>
                      </div>
                    ))}
                  </div>
                </div>
                
                <div className="bg-[#1e1f2c] rounded-lg p-4">
                  <p className="font-medium text-lg mb-3">
                    {getMensajeEmocional()}
                  </p>
                  
                  <div className="flex flex-wrap gap-3 mt-4">
                    <button className="flex items-center gap-1 px-3 py-1.5 bg-purple-600 hover:bg-purple-500 rounded-lg text-white text-sm">
                      <Download className="w-4 h-4" />
                      Descargar Informe en PDF
                    </button>
                    
                    <button className="flex items-center gap-1 px-3 py-1.5 bg-blue-600 hover:bg-blue-500 rounded-lg text-white text-sm">
                      <FileText className="w-4 h-4" />
                      Sincronizar con Notion
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </Card>
      
      {/* Sección 1: Mi Definición de Vida Rica */}
      <Card>
        <div className="p-6">
          <div className="flex items-center mb-4">
            <div className="flex -space-x-2 mr-3">
              <Palmtree className="w-6 h-6 text-blue-400" />
              <Users className="w-6 h-6 text-pink-400" />
              <Laptop className="w-6 h-6 text-amber-400" />
            </div>
            <h2 className="text-xl font-semibold">Mi Definición de Vida Rica</h2>
          </div>
          
          <div className="bg-[#2a2b38] rounded-xl p-5">
            {editandoDefinicion ? (
              <div className="space-y-4">
                <textarea
                  value={definicionVidaRica}
                  onChange={(e) => setDefinicionVidaRica(e.target.value)}
                  className="w-full bg-[#21222d] border border-gray-700 rounded-lg p-3 text-white min-h-[120px]"
                  placeholder="¿Cómo defines tu vida rica ideal?"
                />
                
                <div className="flex justify-end">
                  <button
                    onClick={() => setEditandoDefinicion(false)}
                    className="px-4 py-2 border border-gray-600 rounded-lg text-gray-300 hover:bg-gray-700 mr-2"
                  >
                    Cancelar
                  </button>
                  <button
                    onClick={actualizarDefinicion}
                    className="px-4 py-2 bg-blue-600 hover:bg-blue-500 rounded-lg text-white"
                  >
                    Guardar
                  </button>
                </div>
              </div>
            ) : (
              <div className="relative">
                <p className="text-lg italic text-white leading-relaxed pl-8">
                  "{definicionVidaRica}"
                </p>
                <div className="absolute left-0 top-0 text-blue-400 text-2xl">
                  "
                </div>
                <div className="absolute right-0 bottom-0 text-blue-400 text-2xl">
                  „
                </div>
                <button
                  onClick={() => setEditandoDefinicion(true)}
                  className="mt-4 flex items-center text-blue-400 hover:text-blue-300 text-sm font-medium"
                >
                  <Edit className="w-4 h-4 mr-1" />
                  Actualizar mi visión de riqueza
                </button>
              </div>
            )}
          </div>
          
          <div className="mt-4 p-3 rounded-lg bg-[#212630] border border-blue-800/30 text-sm">
            <div className="flex items-start">
              <Info className="w-4 h-4 text-blue-400 mt-1 mr-2 flex-shrink-0" />
              <p className="text-gray-300">
                <span className="text-blue-300 font-medium">Tip:</span> Tu definición de vida rica es tu brújula financiera. Revísala cada 6 meses para asegurarte de que sigue alineada con tus valores.
              </p>
            </div>
          </div>
        </div>
      </Card>
      
      {/* Sección 2: Gastos Que Me Hacen Feliz */}
      <Card>
        <div className="p-6">
          <div className="flex items-center mb-6">
            <Sparkles className="w-6 h-6 text-amber-400 mr-2" />
            <h2 className="text-xl font-semibold">Gastos Que Me Hacen Feliz</h2>
          </div>
          
          <div className="bg-[#2a2b38] rounded-xl p-5 mb-6">
            <div className="flex justify-between items-center mb-4">
              <p className="text-base font-medium">Gastos que me hacen sentir rico y los disfruto conscientemente</p>
              <button 
                onClick={() => setMostrarFormCategoria(!mostrarFormCategoria)}
                className="text-sm font-medium text-amber-400 hover:text-amber-300 flex items-center"
              >
                <PlusCircle className="w-4 h-4 mr-1" />
                {mostrarFormCategoria ? 'Cancelar' : 'Agregar categoría'}
              </button>
            </div>
            
            {/* Formulario para agregar categoría */}
            {mostrarFormCategoria && (
              <div className="mb-4 p-4 bg-[#21222d] rounded-lg border border-gray-700 animate-fadeIn">
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-3">
                  <div>
                    <label className="block text-sm font-medium text-gray-400 mb-1">Nombre de la categoría</label>
                    <input
                      type="text"
                      value={nuevaCategoria.nombre}
                      onChange={(e) => setNuevaCategoria({...nuevaCategoria, nombre: e.target.value})}
                      className="w-full bg-[#2a2b38] border border-gray-700 rounded-lg p-2 text-white"
                      placeholder="Ej. Clases de baile"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-400 mb-1">Presupuesto mensual</label>
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <span className="text-gray-500">$</span>
                      </div>
                      <input
                        type="number"
                        value={nuevaCategoria.presupuesto}
                        onChange={(e) => setNuevaCategoria({...nuevaCategoria, presupuesto: e.target.value})}
                        className="w-full bg-[#2a2b38] border border-gray-700 rounded-lg p-2 pl-8 text-white"
                        placeholder="Ej. 150"
                      />
                    </div>
                  </div>
                </div>
                <div className="flex justify-end">
                  <button
                    onClick={agregarCategoria}
                    className="px-4 py-2 bg-amber-600 hover:bg-amber-500 rounded-lg text-white"
                    disabled={!nuevaCategoria.nombre || !nuevaCategoria.presupuesto || parseFloat(nuevaCategoria.presupuesto) <= 0}
                  >
                    Agregar
                  </button>
                </div>
              </div>
            )}
            
            {/* Tabla de gastos felices */}
            <div className="overflow-x-auto">
              <table className="w-full min-w-[600px]">
                <thead>
                  <tr className="border-b border-gray-700">
                    <th className="text-left py-2 font-bold text-gray-300">Categoría</th>
                    <th className="text-right py-2 font-bold text-gray-300">Presupuesto Ideal</th>
                    <th className="text-right py-2 font-bold text-gray-300">Gasto Real</th>
                    <th className="text-center py-2 font-bold text-gray-300">Estado</th>
                    <th className="text-center py-2 font-bold text-gray-300">Acciones</th>
                  </tr>
                </thead>
                <tbody>
                  {gastosfelices.map(gasto => (
                    <tr key={gasto.id} className="border-b border-gray-700 hover:bg-[#21222d] transition-colors">
                      <td className="py-3">
                        <div className="flex items-center">
                          <div className="w-8 h-8 rounded-full bg-amber-500/20 flex items-center justify-center text-amber-400 mr-2">
                            {gasto.icono}
                          </div>
                          <span className="font-medium">{gasto.categoria}</span>
                        </div>
                      </td>
                      <td className="py-3 text-right">
                        <div className="flex items-center justify-end">
                          <span className="font-semibold text-amber-300">${gasto.presupuesto}</span>
                          <button 
                            onClick={() => {
                              const nuevoPresupuesto = prompt("Nuevo presupuesto para " + gasto.categoria + ":", gasto.presupuesto.toString());
                              if (nuevoPresupuesto && !isNaN(parseFloat(nuevoPresupuesto)) && parseFloat(nuevoPresupuesto) > 0) {
                                actualizarPresupuesto(gasto.id, parseFloat(nuevoPresupuesto));
                              }
                            }}
                            className="ml-2 text-gray-400 hover:text-white"
                          >
                            ✏️
                          </button>
                        </div>
                      </td>
                      <td className="py-3 text-right">
                        <div className="flex items-center justify-end">
                          <span className="font-semibold text-blue-300">${gasto.gastado || 0}</span>
                          <button 
                            onClick={() => {
                              const nuevoGastado = prompt("Gasto real para " + gasto.categoria + ":", (gasto.gastado || 0).toString());
                              if (nuevoGastado && !isNaN(parseFloat(nuevoGastado)) && parseFloat(nuevoGastado) >= 0) {
                                actualizarGastado(gasto.id, parseFloat(nuevoGastado));
                              }
                            }}
                            className="ml-2 text-gray-400 hover:text-white"
                          >
                            ✏️
                          </button>
                        </div>
                      </td>
                      <td className="py-3 text-center">
                        <div className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          (gasto.gastado || 0) <= gasto.presupuesto * 0.5
                            ? 'bg-yellow-900/30 text-yellow-300'
                            : (gasto.gastado || 0) <= gasto.presupuesto
                              ? 'bg-green-900/30 text-green-400'
                              : 'bg-red-900/30 text-red-400'
                        }`}>
                          {(gasto.gastado || 0) <= gasto.presupuesto * 0.5
                            ? '⚠️'
                            : (gasto.gastado || 0) <= gasto.presupuesto
                              ? '✅'
                              : '⚠️'}
                        </div>
                      </td>
                      <td className="py-3 text-center">
                        <button
                          onClick={() => eliminarCategoria(gasto.id)}
                          className="w-7 h-7 rounded-full bg-red-900/20 flex items-center justify-center text-red-400 hover:bg-red-900/40 transition-colors"
                          title="Eliminar categoría"
                        >
                          <Trash2 className="w-4 h-4" />
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
                <tfoot>
                  <tr className="border-t border-gray-700 font-bold">
                    <td className="py-3">Total</td>
                    <td className="py-3 text-right text-amber-300">${totalPresupuesto}</td>
                    <td className="py-3 text-right text-blue-300">${totalGastado}</td>
                    <td colSpan={2} className="py-3 text-center">
                      <div className="w-full bg-gray-700 h-2 rounded-full overflow-hidden">
                        <div 
                          className={`h-full ${
                            totalGastado <= totalPresupuesto ? 'bg-green-500' : 'bg-red-500'
                          }`}
                          style={{ 
                            width: `${Math.min(100, (totalGastado / totalPresupuesto) * 100)}%`,
                            transition: 'width 0.5s ease-in-out'
                          }}
                        ></div>
                      </div>
                      <div className="text-xs mt-1 text-gray-400">
                        {porcentajeGastado}% utilizado
                      </div>
                    </td>
                  </tr>
                </tfoot>
              </table>
            </div>
          </div>
          
          <div className="p-4 bg-amber-900/20 border border-amber-800/30 rounded-xl">
            <p className="flex items-center text-amber-300 font-medium">
              <Gem className="w-5 h-5 mr-2 flex-shrink-0" />
              {getMensajeGastosFelices()}
            </p>
            <p className="text-sm text-gray-300 mt-2 ml-7">
              Estos gastos representan el {porcentajeGastosFelices}% de tu presupuesto total mensual.
            </p>
          </div>
        </div>
      </Card>
      
      {/* Sección 3: Gastos Que No Me Aportan */}
      <Card id="no-prioridades">
        <div className="p-6">
          <div className="flex items-center mb-6">
            <X className="w-6 h-6 text-red-400 mr-2" />
            <h2 className="text-xl font-semibold">Gastos Que No Me Aportan</h2>
          </div>
          
          <div className="bg-[#2a2b38] rounded-xl p-5">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-medium">🛑 No gasto en esto (y me siento bien)</h3>
              <button 
                onClick={() => setMostrarFormNoPrioridad(!mostrarFormNoPrioridad)}
                className="text-sm font-medium text-red-400 hover:text-red-300 flex items-center"
              >
                <PlusCircle className="w-4 h-4 mr-1" />
                {mostrarFormNoPrioridad ? 'Cancelar' : 'Agregar no prioridad'}
              </button>
            </div>
            
            {/* Formulario para agregar no prioridad */}
            {mostrarFormNoPrioridad && (
              <div className="mb-4 p-4 bg-[#21222d] rounded-lg border border-gray-700 animate-fadeIn">
                <div className="mb-3">
                  <label className="block text-sm font-medium text-gray-400 mb-1">Describe algo en lo que conscientemente decides no gastar</label>
                  <input
                    type="text"
                    value={nuevaNoPrioridad}
                    onChange={(e) => setNuevaNoPrioridad(e.target.value)}
                    className="w-full bg-[#2a2b38] border border-gray-700 rounded-lg p-2 text-white"
                    placeholder="Ej. Subscripciones que no uso"
                  />
                </div>
                <div className="flex justify-end">
                  <button
                    onClick={agregarNoPrioridad}
                    className="px-4 py-2 bg-red-600 hover:bg-red-500 rounded-lg text-white"
                    disabled={!nuevaNoPrioridad}
                  >
                    Agregar
                  </button>
                </div>
              </div>
            )}
            
            <div className="space-y-2">
              {noPrioridades.map(noPrioridad => {
                // Verificar si hay gastos en esta no-prioridad
                const hayGastos = (() => {
                  if (noPrioridad.descripcion === 'Comer fuera sin propósito') {
                    return gastosReales.some(g => g.categoria === 'Restaurantes');
                  } else if (noPrioridad.descripcion === 'Ropa innecesaria') {
                    return gastosReales.some(g => g.categoria === 'Ropa');
                  } else if (noPrioridad.descripcion === 'Apps que no uso') {
                    return gastosReales.some(g => g.categoria === 'Suscripciones');
                  } else if (noPrioridad.descripcion === 'Clubes sociales que no uso') {
                    return gastosReales.some(g => g.categoria === 'Clubes sociales');
                  }
                  return false;
                })();
                
                return (
                  <div 
                    key={noPrioridad.id}
                    className={`flex items-center justify-between p-3 ${
                      hayGastos && noPrioridad.checked 
                        ? 'bg-red-900/30 border border-red-800/50' 
                        : 'bg-[#21222d] border border-transparent'
                    } rounded-lg hover:bg-[#282a3a] transition-colors`}
                  >
                    <div className="flex items-center">
                      <div 
                        className={`w-6 h-6 rounded flex items-center justify-center mr-3 cursor-pointer ${
                          noPrioridad.checked 
                            ? 'bg-red-500 text-white' 
                            : 'bg-gray-700 text-gray-400'
                        }`}
                        onClick={() => toggleNoPrioridad(noPrioridad.id)}
                      >
                        {noPrioridad.checked && <Check className="w-4 h-4" />}
                      </div>
                      <span className={`${noPrioridad.checked ? 'text-white' : 'text-gray-400'}`}>
                        {noPrioridad.descripcion}
                        {hayGastos && noPrioridad.checked && (
                          <span className="text-red-300 text-sm ml-2">
                            (⚠️ Hay gastos en esta categoría)
                          </span>
                        )}
                      </span>
                    </div>
                    <button
                      onClick={() => eliminarNoPrioridad(noPrioridad.id)}
                      className="text-gray-400 hover:text-red-400"
                    >
                      <Trash2 className="w-4 h-4" />
                    </button>
                  </div>
                );
              })}
            </div>
            
            {noPrioridades.length === 0 && (
              <div className="text-center py-6 text-gray-500">
                <Coffee className="w-12 h-12 mx-auto mb-2 opacity-30" />
                <p>No hay elementos en la lista</p>
                <p className="text-sm">Agrega cosas en las que decides no gastar conscientemente</p>
              </div>
            )}
          </div>
          
          <div className="mt-4 p-4 bg-[#212630] border border-gray-700 rounded-lg">
            <p className="text-sm text-gray-300 italic">
              "Corta despiadadamente los gastos en cosas que no te importan, para poder gastar extravagantemente en las que sí." 
              <span className="block mt-1 text-right">— Ramit Sethi</span>
            </p>
          </div>
        </div>
      </Card>
      
      {/* Sección 4: Motivación y Legado */}
      <Card>
        <div className="p-6">
          <div className="flex items-center mb-6">
            <Brain className="w-6 h-6 text-purple-400 mr-2" />
            <h2 className="text-xl font-semibold">Motivación y Legado</h2>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="bg-gradient-to-br from-green-900/40 to-blue-900/40 rounded-xl p-5 border border-green-800/30">
              <div className="flex items-center mb-3">
                <Brain className="w-5 h-5 text-green-400 mr-2" />
                <h3 className="text-lg font-medium">🧠 Orgullo futuro</h3>
              </div>
              
              <div className="relative">
                <p className="text-white">{motivacionLegado.orgullo}</p>
                <button
                  onClick={() => {
                    const nuevoValor = prompt("Editar orgullo futuro:", motivacionLegado.orgullo);
                    if (nuevoValor) {
                      editarMotivacionLegado('orgullo', nuevoValor);
                    }
                  }}
                  className="mt-2 text-green-400 hover:text-green-300 text-sm"
                >
                  <Edit className="w-4 h-4" />
                </button>
              </div>
            </div>
            
            <div className="bg-gradient-to-br from-red-900/40 to-orange-900/40 rounded-xl p-5 border border-red-800/30">
              <div className="flex items-center mb-3">
                <Heart className="w-5 h-5 text-red-400 mr-2" />
                <h3 className="text-lg font-medium">💔 Miedo raíz</h3>
              </div>
              
              <div className="relative">
                <p className="text-white">{motivacionLegado.miedo}</p>
                <button
                  onClick={() => {
                    const nuevoValor = prompt("Editar miedo raíz:", motivacionLegado.miedo);
                    if (nuevoValor) {
                      editarMotivacionLegado('miedo', nuevoValor);
                    }
                  }}
                  className="mt-2 text-red-400 hover:text-red-300 text-sm"
                >
                  <Edit className="w-4 h-4" />
                </button>
              </div>
            </div>
            
            <div className="bg-gradient-to-br from-blue-900/40 to-purple-900/40 rounded-xl p-5 border border-blue-800/30">
              <div className="flex items-center mb-3">
                <Users className="w-5 h-5 text-blue-400 mr-2" />
                <h3 className="text-lg font-medium">👨‍👧 Legado deseado</h3>
              </div>
              
              <div className="relative">
                <p className="text-white">{motivacionLegado.legado}</p>
                <button
                  onClick={() => {
                    const nuevoValor = prompt("Editar legado deseado:", motivacionLegado.legado);
                    if (nuevoValor) {
                      editarMotivacionLegado('legado', nuevoValor);
                    }
                  }}
                  className="mt-2 text-blue-400 hover:text-blue-300 text-sm"
                >
                  <Edit className="w-4 h-4" />
                </button>
              </div>
            </div>
          </div>
          
          <div className="mt-6 p-4 bg-[#212630] border border-gray-700 rounded-lg">
            <p className="text-sm text-gray-300 flex items-start">
              <Info className="w-4 h-4 text-purple-400 mt-0.5 mr-2 flex-shrink-0" />
              <span>Conocer tus motivaciones profundas y miedos es clave para tomar mejores decisiones financieras. Dave Ramsey recomienda conectar siempre las finanzas con los valores y el legado que quieres dejar.</span>
            </p>
          </div>
        </div>
      </Card>
      
      {/* Sección 5: Visión de Futuro */}
      <Card>
        <div className="p-6">
          <div className="flex items-center mb-6">
            <Target className="w-6 h-6 text-blue-400 mr-2" />
            <h2 className="text-xl font-semibold">Visión de Futuro</h2>
          </div>
          
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
            {visionFuturo.map(item => (
              <div 
                key={item.id}
                className="bg-[#2a2b38] rounded-xl p-5 border border-blue-800/20 hover:border-blue-800/40 transition-all duration-300 hover:transform hover:scale-105"
              >
                <div className="w-12 h-12 rounded-full bg-blue-500/20 flex items-center justify-center text-blue-400 mb-3">
                  {item.icono}
                </div>
                
                <p className="text-lg font-medium text-white">{item.descripcion}</p>
                
                <button
                  onClick={() => {
                    const nuevaDescripcion = prompt("Editar visión:", item.descripcion);
                    if (nuevaDescripcion) {
                      editarVisionFuturo(item.id, nuevaDescripcion);
                    }
                  }}
                  className="mt-3 text-blue-400 hover:text-blue-300 text-sm"
                >
                  <Edit className="w-4 h-4" />
                </button>
              </div>
            ))}
          </div>
          
          <div className="mt-6 p-4 bg-blue-900/20 border border-blue-800/30 rounded-lg">
            <p className="text-base text-blue-300 font-medium flex items-center mb-2">
              <Target className="w-5 h-5 mr-2" />
              Principios FIRE: Libertad Financiera, Independencia, Retiro Temprano
            </p>
            <p className="text-sm text-gray-300 ml-7">
              Tu visión de futuro es tu "porqué" financiero. Un recordatorio diario de por qué estás ahorrando, invirtiendo y planificando con tanto cuidado.
            </p>
          </div>
        </div>
      </Card>
      
      {/* Modal de reasignación de presupuesto */}
      {mostrarModalReasignacion && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4 animate-fadeIn">
          <div className="bg-[#21222d] rounded-xl w-full max-w-md shadow-xl animate-scaleIn">
            <div className="p-6">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-semibold">Reasignar Presupuesto</h3>
                <button
                  onClick={() => setMostrarModalReasignacion(false)}
                  className="text-gray-500 hover:text-white"
                >
                  <X className="w-5 h-5" />
                </button>
              </div>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-1">
                    Quitar de
                  </label>
                  <select
                    value={reasignacionEnProceso.desde}
                    onChange={(e) => setReasignacionEnProceso({...reasignacionEnProceso, desde: e.target.value})}
                    className="w-full bg-[#2a2b38] border border-gray-700 rounded-lg p-2 text-white"
                  >
                    <option value="">Selecciona categoría</option>
                    {obtenerCategoriasNoPrioridad().map(categoria => (
                      <option key={categoria} value={categoria}>{categoria}</option>
                    ))}
                  </select>
                </div>
                
                <div className="flex items-center justify-center">
                  <ArrowRightLeft className="w-6 h-6 text-blue-400" />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-1">
                    Agregar a
                  </label>
                  <select
                    value={reasignacionEnProceso.hacia}
                    onChange={(e) => setReasignacionEnProceso({...reasignacionEnProceso, hacia: e.target.value})}
                    className="w-full bg-[#2a2b38] border border-gray-700 rounded-lg p-2 text-white"
                  >
                    <option value="">Selecciona categoría</option>
                    {gastosfelices.map(gasto => (
                      <option key={gasto.id} value={gasto.categoria}>{gasto.categoria}</option>
                    ))}
                  </select>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-1">
                    Monto a reasignar
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <span className="text-gray-500">$</span>
                    </div>
                    <input
                      type="number"
                      value={reasignacionEnProceso.monto}
                      onChange={(e) => setReasignacionEnProceso({...reasignacionEnProceso, monto: e.target.value})}
                      className="w-full bg-[#2a2b38] border border-gray-700 rounded-lg p-2 pl-8 text-white"
                      placeholder="Ej. 50"
                    />
                  </div>
                </div>
                
                <div className="p-3 bg-blue-900/20 rounded-lg border border-blue-800/30 text-sm">
                  <div className="flex items-start">
                    <Info className="w-4 h-4 text-blue-400 mt-0.5 mr-2 flex-shrink-0" />
                    <p className="text-gray-300">
                      Reasignar presupuesto te ayuda a alinear tu dinero con tus valores, disminuyendo lo que gastas en cosas que no valoras para aumentar lo que gastas en las que te hacen feliz.
                    </p>
                  </div>
                </div>
              </div>
              
              <div className="mt-6 flex justify-end gap-2">
                <button
                  onClick={() => setMostrarModalReasignacion(false)}
                  className="px-4 py-2 border border-gray-600 rounded-lg text-gray-300 hover:bg-gray-700"
                >
                  Cancelar
                </button>
                <button
                  onClick={reasignarPresupuesto}
                  className="px-4 py-2 bg-blue-600 hover:bg-blue-500 rounded-lg text-white"
                  disabled={
                    !reasignacionEnProceso.desde || 
                    !reasignacionEnProceso.hacia || 
                    !reasignacionEnProceso.monto || 
                    parseFloat(reasignacionEnProceso.monto) <= 0
                  }
                >
                  Reasignar
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}