import { Card } from './ui/Card';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, TooltipProps } from 'recharts';
import { ValueType, NameType } from 'recharts/types/component/DefaultTooltipContent';
import { useTransactionContext } from '../lib/TransactionContext';
import { calculateFinancialMetrics } from '../lib/calculations';
import { LoadingSpinner } from './LoadingSpinner';

// Datos simulados para patrimonio (hasta que tengamos datos reales)
const mockPatrimonioTrends = [
  { mes: 'Enero', valor: 50000 },
  { mes: 'Febrero', valor: 52000 },
  { mes: 'Marzo', valor: 53500 },
  { mes: 'Abril', valor: 55000 },
  { mes: 'Mayo', valor: 57000 },
  { mes: 'Junio', valor: 59000 },
];

// Helper function to format numbers
const formatNumber = (num: number): string => {
  return new Intl.NumberFormat('es-ES').format(Math.round(num));
};

// Custom tooltip component
const CustomTooltip = ({ active, payload, label }: TooltipProps<ValueType, NameType>) => {
  if (active && payload && payload.length) {
    return (
      <div className="bg-white p-3 border border-gray-200 shadow-md rounded-lg">
        <p className="font-bold">{label}</p>
        {payload.map((entry, index) => (
          <p key={index} className="text-sm" style={{ color: entry.color }}>
            {entry.name}: <span className="font-medium">${formatNumber(Number(entry.value))}</span>
          </p>
        ))}
      </div>
    );
  }
  return null;
};

export function Reports() {
  const { transactions, loading, error } = useTransactionContext();
  
  // Calculate real financial metrics
  const metrics = calculateFinancialMetrics(transactions);
  
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <LoadingSpinner size="lg" text="Cargando reportes..." />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <h1 className="text-3xl font-bold">Reportes Financieros</h1>
      
      {error && (
        <div className="bg-red-900/30 border border-red-800 text-red-300 p-4 rounded-lg">
          <p className="font-medium">Error al cargar datos:</p>
          <p className="text-sm">{error}</p>
        </div>
      )}
      
      <Card className="hover:shadow-lg transition-shadow duration-300">
        <div className="p-6">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-semibold">Tendencias Mensuales (Últimos 6 meses)</h3>
            <div className="text-sm text-gray-400">
              Basado en {transactions.length} transacciones
            </div>
          </div>
          <div className="h-[300px]">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart data={metrics.transaccionesPorMes} animationDuration={1500}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="mes" />
                <YAxis tickFormatter={(value) => `$${formatNumber(value)}`} />
                <Tooltip content={<CustomTooltip />} />
                <Line 
                  type="monotone" 
                  dataKey="ingresos" 
                  stroke="#16a34a" 
                  name="Ingresos"
                  strokeWidth={2}
                  activeDot={{ r: 6 }}
                />
                <Line 
                  type="monotone" 
                  dataKey="gastos" 
                  stroke="#dc2626" 
                  name="Gastos"
                  strokeWidth={2}
                  activeDot={{ r: 6 }}
                />
                <Line 
                  type="monotone" 
                  dataKey="ahorro" 
                  stroke="#2563eb" 
                  name="Ahorro"
                  strokeWidth={2}
                  activeDot={{ r: 6 }}
                />
              </LineChart>
            </ResponsiveContainer>
          </div>
          
          {metrics.transaccionesPorMes.length === 0 && (
            <div className="text-center py-8 text-gray-500">
              <p className="font-medium">No hay datos suficientes para mostrar tendencias</p>
              <p className="text-sm mt-1">Agrega más transacciones para ver las tendencias mensuales</p>
            </div>
          )}
          
          <div className="mt-4 grid grid-cols-3 gap-4 text-sm">
            <div className="text-center"><span className="inline-block w-3 h-3 bg-green-600 rounded-full mr-1"></span>Ingresos promedio: ${formatNumber(metrics.transaccionesPorMes.reduce((sum, item) => sum + item.ingresos, 0) / (metrics.transaccionesPorMes.length || 1))}</div>
            <div className="text-center"><span className="inline-block w-3 h-3 bg-red-600 rounded-full mr-1"></span>Gastos promedio: ${formatNumber(metrics.transaccionesPorMes.reduce((sum, item) => sum + item.gastos, 0) / (metrics.transaccionesPorMes.length || 1))}</div>
            <div className="text-center"><span className="inline-block w-3 h-3 bg-blue-600 rounded-full mr-1"></span>Ahorro promedio: ${formatNumber(metrics.transaccionesPorMes.reduce((sum, item) => sum + item.ahorro, 0) / (metrics.transaccionesPorMes.length || 1))}</div>
          </div>
        </div>
      </Card>

      <Card className="hover:shadow-lg transition-shadow duration-300">
        <div className="p-6">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-semibold">Evolución del Patrimonio Neto</h3>
            <div className="text-sm text-gray-400 bg-yellow-900/30 border border-yellow-800 text-yellow-300 px-2 py-1 rounded">
              Datos simulados - En desarrollo
            </div>
          </div>
          <div className="h-[300px]">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart data={mockPatrimonioTrends} animationDuration={1500}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="mes" />
                <YAxis tickFormatter={(value) => `$${formatNumber(value)}`} />
                <Tooltip formatter={(value) => `$${formatNumber(Number(value))}`} />
                <Line 
                  type="monotone" 
                  dataKey="valor" 
                  stroke="#2563eb" 
                  strokeWidth={2}
                  activeDot={{ r: 8 }}
                />
              </LineChart>
            </ResponsiveContainer>
          </div>
          
          <div className="mt-4 p-3 bg-blue-900/30 border border-blue-800 text-blue-300 rounded-lg text-sm">
            <p className="font-medium">📊 Próximamente:</p>
            <p>Esta sección mostrará la evolución real de tu patrimonio neto basada en el registro de activos y pasivos.</p>
            <p className="mt-1">Por ahora muestra datos de ejemplo para demostrar la funcionalidad.</p>
          </div>
        </div>
      </Card>
    </div>
  );
}