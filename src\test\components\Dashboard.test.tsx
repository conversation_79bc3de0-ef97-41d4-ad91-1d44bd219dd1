import { describe, it, expect, vi } from 'vitest';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { <PERSON><PERSON>er<PERSON>outer } from 'react-router-dom';
import { Dashboard } from '../../components/Dashboard';
import { ThemeProvider } from '../../lib/ThemeContext';
import { AuthProvider } from '../../lib/AuthContext';
import { TransactionProvider } from '../../lib/TransactionContext';
import { ToastProvider } from '../../components/Toast';
import { TooltipProvider } from '../../components/ui/Tooltip';

// Mock the transaction context
vi.mock('../../lib/TransactionContext', () => ({
  useTransactionContext: () => ({
    transactions: [
      {
        id: '1',
        description: 'Test transaction',
        date: '2024-01-15',
        type: 'income',
        amount: 1000,
        category: 'salary',
        icon: '💼'
      }
    ],
    loading: false,
    error: null,
    addTransaction: vi.fn(),
    deleteTransaction: vi.fn(),
    refetch: vi.fn()
  }),
  TransactionProvider: ({ children }: { children: React.ReactNode }) => <div>{children}</div>
}));

// Mock auth context
vi.mock('../../lib/AuthContext', () => ({
  useAuth: () => ({
    user: { id: 'test-user', email: '<EMAIL>' },
    session: null,
    loading: false,
    signUp: vi.fn(),
    signIn: vi.fn(),
    signOut: vi.fn()
  }),
  AuthProvider: ({ children }: { children: React.ReactNode }) => <div>{children}</div>
}));

const TestWrapper = ({ children }: { children: React.ReactNode }) => (
  <BrowserRouter>
    <ThemeProvider>
      <ToastProvider>
        <AuthProvider>
          <TransactionProvider>
            <TooltipProvider>
              {children}
            </TooltipProvider>
          </TransactionProvider>
        </AuthProvider>
      </ToastProvider>
    </ThemeProvider>
  </BrowserRouter>
);

describe('Dashboard Component', () => {
  it('renders dashboard title', async () => {
    render(
      <TestWrapper>
        <Dashboard />
      </TestWrapper>
    );
    
    expect(screen.getByText('Dashboard')).toBeInTheDocument();
  });

  it('displays financial metrics', async () => {
    render(
      <TestWrapper>
        <Dashboard />
      </TestWrapper>
    );
    
    await waitFor(() => {
      expect(screen.getByText('Visión General')).toBeInTheDocument();
    });
  });

  it('shows search input', () => {
    render(
      <TestWrapper>
        <Dashboard />
      </TestWrapper>
    );
    
    const searchInput = screen.getByPlaceholderText('Search');
    expect(searchInput).toBeInTheDocument();
  });

  it('displays transaction history section', () => {
    render(
      <TestWrapper>
        <Dashboard />
      </TestWrapper>
    );
    
    expect(screen.getByText('Transacciones Recientes')).toBeInTheDocument();
  });

  it('shows help tooltips when hovering info icons', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <Dashboard />
      </TestWrapper>
    );
    
    const infoIcons = screen.getAllByRole('button');
    const dashboardInfoIcon = infoIcons.find(icon => 
      icon.closest('[data-testid="dashboard-info"]')
    );
    
    if (dashboardInfoIcon) {
      await user.hover(dashboardInfoIcon);
      
      await waitFor(() => {
        expect(screen.getByText(/Panel Principal/)).toBeInTheDocument();
      });
    }
  });

  it('handles month selection', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <Dashboard />
      </TestWrapper>
    );
    
    // Find month dropdown button
    const monthButtons = screen.getAllByRole('button');
    const monthDropdown = monthButtons.find(button => 
      button.textContent?.includes('Enero')
    );
    
    if (monthDropdown) {
      await user.click(monthDropdown);
      
      await waitFor(() => {
        const februaryOption = screen.getByText('Febrero');
        expect(februaryOption).toBeInTheDocument();
      });
    }
  });
});