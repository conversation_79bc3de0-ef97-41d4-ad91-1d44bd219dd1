import React, { createContext, useState, useEffect, useContext } from 'react';

type Theme = 'dark';

interface ThemeContextType {
  theme: Theme;
  toggleTheme: () => void;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export function ThemeProvider({ children }: { children: React.ReactNode }) {
  // Always use dark theme
  const [theme, setTheme] = useState<Theme>('dark');

  // Apply theme class to html element
  useEffect(() => {
    const html = document.documentElement;
    html.classList.add('dark');
  }, []);

  // No-op toggle since we're always in dark mode
  const toggleTheme = () => {
    // Keep dark mode
  };

  return (
    <ThemeContext.Provider value={{ theme, toggleTheme }}>
      {children}
    </ThemeContext.Provider>
  );
}

export function useTheme() {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
}