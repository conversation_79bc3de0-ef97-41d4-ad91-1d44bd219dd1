import { useState, useEffect, useCallback, useRef } from 'react';

interface AccessibilityState {
  isHighContrast: boolean;
  fontSize: 'small' | 'medium' | 'large';
  reducedMotion: boolean;
  screenReaderMode: boolean;
  keyboardNavigation: boolean;
}

interface AccessibilityActions {
  toggleHighContrast: () => void;
  setFontSize: (size: 'small' | 'medium' | 'large') => void;
  toggleReducedMotion: () => void;
  toggleScreenReaderMode: () => void;
  announceToScreenReader: (message: string) => void;
  focusElement: (elementId: string) => void;
  skipToContent: () => void;
}

const defaultState: AccessibilityState = {
  isHighContrast: false,
  fontSize: 'medium',
  reducedMotion: false,
  screenReaderMode: false,
  keyboardNavigation: false
};

export function useAccessibility(): AccessibilityState & AccessibilityActions {
  const [state, setState] = useState<AccessibilityState>(() => {
    const saved = localStorage.getItem('accessibilitySettings');
    return saved ? { ...defaultState, ...JSON.parse(saved) } : defaultState;
  });

  const [announcement, setAnnouncement] = useState<string>('');
  const announcementTimeoutRef = useRef<NodeJS.Timeout>();

  // Save settings to localStorage
  const saveSettings = useCallback((newState: AccessibilityState) => {
    localStorage.setItem('accessibilitySettings', JSON.stringify(newState));
    setState(newState);
  }, []);

  // Apply accessibility settings to document
  useEffect(() => {
    const root = document.documentElement;
    
    // High contrast
    if (state.isHighContrast) {
      root.classList.add('high-contrast');
    } else {
      root.classList.remove('high-contrast');
    }

    // Font size
    root.classList.remove('font-small', 'font-medium', 'font-large');
    root.classList.add(`font-${state.fontSize}`);

    // Reduced motion
    if (state.reducedMotion) {
      root.classList.add('reduce-motion');
    } else {
      root.classList.remove('reduce-motion');
    }

    // Screen reader mode
    if (state.screenReaderMode) {
      root.classList.add('screen-reader-mode');
    } else {
      root.classList.remove('screen-reader-mode');
    }
  }, [state]);

  // Detect keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Tab') {
        setState(prev => ({ ...prev, keyboardNavigation: true }));
      }
    };

    const handleMouseDown = () => {
      setState(prev => ({ ...prev, keyboardNavigation: false }));
    };

    document.addEventListener('keydown', handleKeyDown);
    document.addEventListener('mousedown', handleMouseDown);

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.removeEventListener('mousedown', handleMouseDown);
    };
  }, []);

  // Detect system preferences
  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    
    const handleChange = (e: MediaQueryListEvent) => {
      setState(prev => ({ ...prev, reducedMotion: e.matches }));
    };

    mediaQuery.addEventListener('change', handleChange);
    
    // Set initial value
    if (mediaQuery.matches) {
      setState(prev => ({ ...prev, reducedMotion: true }));
    }

    return () => mediaQuery.removeEventListener('change', handleChange);
  }, []);

  // Actions
  const toggleHighContrast = useCallback(() => {
    const newState = { ...state, isHighContrast: !state.isHighContrast };
    saveSettings(newState);
  }, [state, saveSettings]);

  const setFontSize = useCallback((size: 'small' | 'medium' | 'large') => {
    const newState = { ...state, fontSize: size };
    saveSettings(newState);
  }, [state, saveSettings]);

  const toggleReducedMotion = useCallback(() => {
    const newState = { ...state, reducedMotion: !state.reducedMotion };
    saveSettings(newState);
  }, [state, saveSettings]);

  const toggleScreenReaderMode = useCallback(() => {
    const newState = { ...state, screenReaderMode: !state.screenReaderMode };
    saveSettings(newState);
  }, [state, saveSettings]);

  const announceToScreenReader = useCallback((message: string) => {
    setAnnouncement(message);
    
    // Clear previous timeout
    if (announcementTimeoutRef.current) {
      clearTimeout(announcementTimeoutRef.current);
    }
    
    // Clear announcement after 1 second
    announcementTimeoutRef.current = setTimeout(() => {
      setAnnouncement('');
    }, 1000);
  }, []);

  const focusElement = useCallback((elementId: string) => {
    const element = document.getElementById(elementId);
    if (element) {
      element.focus();
    }
  }, []);

  const skipToContent = useCallback(() => {
    const mainContent = document.getElementById('main-content') || document.querySelector('main');
    if (mainContent) {
      mainContent.focus();
      mainContent.scrollIntoView({ behavior: 'smooth' });
    }
  }, []);

  // Announcement region component
  const AnnouncementRegion = () => (
    <div
      aria-live="polite"
      aria-atomic="true"
      className="sr-only"
      role="status"
    >
      {announcement}
    </div>
  );

  return {
    ...state,
    toggleHighContrast,
    setFontSize,
    toggleReducedMotion,
    toggleScreenReaderMode,
    announceToScreenReader,
    focusElement,
    skipToContent,
    AnnouncementRegion
  };
}
