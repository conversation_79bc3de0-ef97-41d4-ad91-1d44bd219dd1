import React, { useState, useEffect } from 'react';
import { X, ChevronRight, ChevronLeft, Book, Lightbulb, Target } from 'lucide-react';
import { useTheme } from '../../lib/ThemeContext';
import { HelpTooltip, InfoTooltip } from './Tooltip';

interface HelpStep {
  id: string;
  title: string;
  content: string;
  target?: string;
  position?: 'top' | 'bottom' | 'left' | 'right';
}

interface HelpTourProps {
  steps: HelpStep[];
  isActive: boolean;
  onComplete: () => void;
  onSkip: () => void;
}

export function HelpTour({ steps, isActive, onComplete, onSkip }: HelpTourProps) {
  const { theme } = useTheme();
  const [currentStep, setCurrentStep] = useState(0);
  const [targetElement, setTargetElement] = useState<HTMLElement | null>(null);

  useEffect(() => {
    if (isActive && steps[currentStep]?.target) {
      const element = document.querySelector(steps[currentStep].target) as HTMLElement;
      setTargetElement(element);
      
      if (element) {
        element.scrollIntoView({ behavior: 'smooth', block: 'center' });
        element.style.position = 'relative';
        element.style.zIndex = '1000';
      }
    }
  }, [currentStep, isActive, steps]);

  const nextStep = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      onComplete();
    }
  };

  const prevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  if (!isActive || !steps.length) return null;

  const step = steps[currentStep];

  return (
    <>
      {/* Overlay */}
      <div className="fixed inset-0 bg-black bg-opacity-50 z-40" />
      
      {/* Spotlight effect */}
      {targetElement && (
        <div
          className="fixed z-50 pointer-events-none"
          style={{
            top: targetElement.offsetTop - 8,
            left: targetElement.offsetLeft - 8,
            width: targetElement.offsetWidth + 16,
            height: targetElement.offsetHeight + 16,
            boxShadow: '0 0 0 9999px rgba(0, 0, 0, 0.5)',
            borderRadius: '8px'
          }}
        />
      )}
      
      {/* Help Card */}
      <div
        className={`fixed z-50 max-w-sm w-full p-6 rounded-xl shadow-xl animate-scaleIn ${
          theme === 'dark'
            ? 'bg-[#21222d] border border-[#2a2a3d] text-white'
            : 'bg-white border border-slate-200 text-slate-900'
        }`}
        style={{
          top: targetElement ? targetElement.offsetTop + targetElement.offsetHeight + 16 : '50%',
          left: targetElement ? targetElement.offsetLeft : '50%',
          transform: targetElement ? 'none' : 'translate(-50%, -50%)'
        }}
      >
        {/* Header */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-2">
            <Lightbulb className="w-5 h-5 text-yellow-500" />
            <h3 className="font-semibold">{step.title}</h3>
          </div>
          <button
            onClick={onSkip}
            className={`p-1 rounded-lg transition-colors ${
              theme === 'dark'
                ? 'text-gray-400 hover:text-white hover:bg-gray-700'
                : 'text-slate-400 hover:text-slate-600 hover:bg-slate-100'
            }`}
          >
            <X className="w-4 h-4" />
          </button>
        </div>
        
        {/* Content */}
        <p className={`text-sm mb-6 ${
          theme === 'dark' ? 'text-gray-300' : 'text-slate-600'
        }`}>
          {step.content}
        </p>
        
        {/* Progress */}
        <div className="flex items-center justify-between mb-4">
          <div className={`text-xs ${
            theme === 'dark' ? 'text-gray-400' : 'text-slate-500'
          }`}>
            Paso {currentStep + 1} de {steps.length}
          </div>
          <div className="flex space-x-1">
            {steps.map((_, index) => (
              <div
                key={index}
                className={`w-2 h-2 rounded-full ${
                  index === currentStep
                    ? 'bg-blue-500'
                    : theme === 'dark'
                      ? 'bg-gray-600'
                      : 'bg-slate-300'
                }`}
              />
            ))}
          </div>
        </div>
        
        {/* Actions */}
        <div className="flex justify-between">
          <button
            onClick={prevStep}
            disabled={currentStep === 0}
            className={`flex items-center space-x-1 px-3 py-2 rounded-lg text-sm transition-colors ${
              currentStep === 0
                ? 'opacity-50 cursor-not-allowed'
                : theme === 'dark'
                  ? 'text-gray-300 hover:text-white hover:bg-gray-700'
                  : 'text-slate-600 hover:text-slate-900 hover:bg-slate-100'
            }`}
          >
            <ChevronLeft className="w-4 h-4" />
            <span>Anterior</span>
          </button>
          
          <button
            onClick={nextStep}
            className="flex items-center space-x-1 px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg text-sm transition-colors"
          >
            <span>{currentStep === steps.length - 1 ? 'Finalizar' : 'Siguiente'}</span>
            {currentStep < steps.length - 1 && <ChevronRight className="w-4 h-4" />}
          </button>
        </div>
      </div>
    </>
  );
}

// Help Center Component
interface HelpCenterProps {
  isOpen: boolean;
  onClose: () => void;
}

export function HelpCenter({ isOpen, onClose }: HelpCenterProps) {
  const { theme } = useTheme();
  const [activeSection, setActiveSection] = useState('getting-started');

  const helpSections = {
    'getting-started': {
      title: 'Primeros Pasos',
      icon: <Target className="w-5 h-5" />,
      content: [
        {
          title: 'Agregar tu primera transacción',
          content: 'Haz clic en el botón "+" para registrar tus ingresos y gastos. Puedes categorizar cada transacción para un mejor seguimiento.'
        },
        {
          title: 'Configurar categorías',
          content: 'Personaliza las categorías de ingresos y gastos según tus necesidades. Puedes agregar iconos y colores personalizados.'
        },
        {
          title: 'Crear presupuestos',
          content: 'Establece límites de gasto por categoría para mantener control sobre tus finanzas. Recibirás alertas cuando te acerques al límite.'
        }
      ]
    },
    'features': {
      title: 'Características',
      icon: <Book className="w-5 h-5" />,
      content: [
        {
          title: 'Dashboard inteligente',
          content: 'Visualiza un resumen completo de tus finanzas con gráficos y estadísticas en tiempo real.'
        },
        {
          title: 'Importar/Exportar datos',
          content: 'Importa transacciones desde archivos CSV o exporta tus datos para usar en otras aplicaciones.'
        },
        {
          title: 'Reportes avanzados',
          content: 'Genera reportes detallados por período, categoría o tipo de transacción.'
        }
      ]
    },
    'tips': {
      title: 'Consejos',
      icon: <Lightbulb className="w-5 h-5" />,
      content: [
        {
          title: 'Registra transacciones diariamente',
          content: 'Mantén el hábito de registrar tus gastos e ingresos todos los días para tener datos precisos.'
        },
        {
          title: 'Revisa tus presupuestos mensualmente',
          content: 'Ajusta tus presupuestos cada mes basándote en tus patrones de gasto reales.'
        },
        {
          title: 'Usa categorías específicas',
          content: 'Mientras más específicas sean tus categorías, mejor podrás analizar tus hábitos financieros.'
        }
      ]
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className={`rounded-xl max-w-4xl w-full max-h-[90vh] overflow-hidden transition-colors duration-200 ${
        theme === 'dark' 
          ? 'bg-[#21222d] border border-[#2a2a3d]' 
          : 'bg-white border border-slate-200 shadow-xl'
      }`}>
        {/* Header */}
        <div className="flex justify-between items-center p-6 border-b border-gray-600">
          <h2 className={`text-xl font-bold transition-colors duration-200 ${
            theme === 'dark' ? 'text-white' : 'text-slate-900'
          }`}>
            Centro de Ayuda
          </h2>
          <button
            onClick={onClose}
            className={`p-1 rounded-lg transition-colors duration-200 ${
              theme === 'dark'
                ? 'text-gray-400 hover:text-white hover:bg-gray-700'
                : 'text-slate-400 hover:text-slate-600 hover:bg-slate-100'
            }`}
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        <div className="flex h-[calc(90vh-120px)]">
          {/* Sidebar */}
          <div className={`w-64 border-r border-gray-600 p-4 ${
            theme === 'dark' ? 'bg-[#2a2b38]' : 'bg-slate-50'
          }`}>
            <nav className="space-y-2">
              {Object.entries(helpSections).map(([key, section]) => (
                <button
                  key={key}
                  onClick={() => setActiveSection(key)}
                  className={`w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-colors ${
                    activeSection === key
                      ? 'bg-blue-500 text-white'
                      : theme === 'dark'
                        ? 'text-gray-300 hover:text-white hover:bg-gray-700'
                        : 'text-slate-600 hover:text-slate-900 hover:bg-slate-200'
                  }`}
                >
                  {section.icon}
                  <span>{section.title}</span>
                </button>
              ))}
            </nav>
          </div>

          {/* Content */}
          <div className="flex-1 p-6 overflow-y-auto">
            <div className="space-y-6">
              <h3 className={`text-lg font-semibold transition-colors duration-200 ${
                theme === 'dark' ? 'text-white' : 'text-slate-900'
              }`}>
                {helpSections[activeSection as keyof typeof helpSections].title}
              </h3>
              
              {helpSections[activeSection as keyof typeof helpSections].content.map((item, index) => (
                <div key={index} className={`p-4 rounded-lg transition-colors duration-200 ${
                  theme === 'dark' 
                    ? 'bg-[#2a2b38] border border-gray-600' 
                    : 'bg-slate-50 border border-slate-200'
                }`}>
                  <h4 className={`font-medium mb-2 transition-colors duration-200 ${
                    theme === 'dark' ? 'text-white' : 'text-slate-900'
                  }`}>
                    {item.title}
                  </h4>
                  <p className={`text-sm transition-colors duration-200 ${
                    theme === 'dark' ? 'text-gray-300' : 'text-slate-600'
                  }`}>
                    {item.content}
                  </p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

// Quick Help Button
interface QuickHelpProps {
  onOpenHelp: () => void;
  onStartTour: () => void;
}

export function QuickHelp({ onOpenHelp, onStartTour }: QuickHelpProps) {
  const { theme } = useTheme();
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div className="relative">
      <HelpTooltip content="¿Necesitas ayuda? Haz clic para ver opciones de ayuda">
        <button
          onClick={() => setIsOpen(!isOpen)}
          className={`p-2 rounded-full transition-colors ${
            theme === 'dark'
              ? 'text-gray-400 hover:text-white hover:bg-gray-700'
              : 'text-slate-500 hover:text-slate-700 hover:bg-slate-100'
          }`}
        >
          <Book className="w-5 h-5" />
        </button>
      </HelpTooltip>

      {isOpen && (
        <div className={`absolute right-0 top-full mt-2 w-48 rounded-lg shadow-lg border z-50 ${
          theme === 'dark'
            ? 'bg-[#21222d] border-[#2a2a3d]'
            : 'bg-white border-slate-200'
        }`}>
          <div className="p-2">
            <button
              onClick={() => {
                onStartTour();
                setIsOpen(false);
              }}
              className={`w-full text-left px-3 py-2 rounded-lg text-sm transition-colors ${
                theme === 'dark'
                  ? 'text-gray-300 hover:text-white hover:bg-gray-700'
                  : 'text-slate-600 hover:text-slate-900 hover:bg-slate-100'
              }`}
            >
              🎯 Tour guiado
            </button>
            <button
              onClick={() => {
                onOpenHelp();
                setIsOpen(false);
              }}
              className={`w-full text-left px-3 py-2 rounded-lg text-sm transition-colors ${
                theme === 'dark'
                  ? 'text-gray-300 hover:text-white hover:bg-gray-700'
                  : 'text-slate-600 hover:text-slate-900 hover:bg-slate-100'
              }`}
            >
              📚 Centro de ayuda
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
