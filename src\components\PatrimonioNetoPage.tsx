import { PatrimonioNetoPanel } from './PatrimonioNetoPanel';
import { AddPatrimonioModal } from './AddPatrimonioModal';
import { useState } from 'react';
import { Mes, MESES } from '../lib/types';
import { getCurrentMonth } from '../lib/calculations';
import { ChevronDown, PieChart, Info, Plus } from 'lucide-react';
import { InfoTooltip } from './ui/Tooltip';

export function PatrimonioNetoPage() {
  const [selectedMonth, setSelectedMonth] = useState<Mes>(getCurrentMonth());
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [showAddModal, setShowAddModal] = useState<'asset' | 'liability' | null>(null);
  
  const handleMonthSelect = (month: Mes) => {
    setSelectedMonth(month);
    setIsDropdownOpen(false);
  };

  return (
    <>
      <div className="space-y-8 pb-20">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6">
        <div className="flex items-center gap-2">
          <h1 className="text-3xl font-bold text-white">Patrimonio Neto</h1>
          <InfoTooltip
            title="Tu Riqueza Total"
            description="El patrimonio neto es lo que realmente posees: tus activos (lo que tienes) menos tus pasivos (lo que debes). Es la medida real de tu riqueza."
          >
            <Info className="w-5 h-5 text-gray-400 hover:text-white cursor-help" />
          </InfoTooltip>
        </div>
        <div className="flex items-center gap-3">
          <button
            onClick={() => setShowAddModal('asset')}
            className="bg-green-600 hover:bg-green-500 text-white rounded-lg px-4 py-2 font-medium flex items-center space-x-2 transition-colors"
          >
            <Plus className="w-4 h-4" />
            <span>Agregar Activo/Pasivo</span>
          </button>
          
          <div className="relative">
          <button 
            onClick={() => setIsDropdownOpen(!isDropdownOpen)}
            className="bg-[#2a2b38] hover:bg-[#32334a] text-white font-medium rounded-lg px-4 py-2.5 text-center inline-flex items-center"
          >
            {selectedMonth}
            <ChevronDown className="w-4 h-4 ml-2" />
          </button>
          
          {isDropdownOpen && (
            <div className="absolute right-0 mt-2 w-48 bg-[#2a2b38] rounded-lg shadow-xl z-10 p-2 animate-fadeIn">
              <div className="max-h-60 overflow-y-auto">
                {MESES.map(mes => (
                  <button
                    key={mes}
                    onClick={() => handleMonthSelect(mes)}
                    className={`w-full text-left px-4 py-2 rounded-md font-medium ${
                      selectedMonth === mes 
                        ? 'bg-[#f9769d] text-white' 
                        : 'text-gray-200 hover:bg-[#32334a]'
                    }`}
                  >
                    {mes}
                  </button>
                ))}
              </div>
            </div>
          )}
          </div>
        </div>
      </div>

      <div>
        <div className="flex items-center mb-4">
          <PieChart className="w-6 h-6 text-[#b66dff] mr-2" />
          <h2 className="text-2xl font-bold">Resumen de Patrimonio</h2>
        </div>
        <PatrimonioNetoPanel mes={selectedMonth} />
      </div>
    </div>
      
      <AddPatrimonioModal
        type={showAddModal || 'asset'}
        isOpen={showAddModal !== null}
        onClose={() => setShowAddModal(null)}
      />
    </>
  );
}