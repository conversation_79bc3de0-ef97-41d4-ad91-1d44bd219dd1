import React, { useState, useMemo } from 'react';
import { format } from 'date-fns';
import { es } from 'date-fns/locale';
import { 
  Search, Filter, Calendar, ArrowUpDown, Plus, 
  TrendingUp, TrendingDown, DollarSign, Eye,
  ChevronDown, X, Edit, Trash2
} from 'lucide-react';
import { useTheme } from '../lib/ThemeContext';
import { useTransactionContext } from '../lib/TransactionContext';
import { LoadingSpinner } from './LoadingSpinner';
import { AddTransactionButton } from './AddTransactionButton';
import { Transaction } from '../lib/types';

// Function to get appropriate background color for transaction icon
const getIconBackground = (category: string, type: 'income' | 'expense'): string => {
  const colorMap = {
    salario: 'bg-pink-500',
    freelance: 'bg-purple-500',
    alimentacion: 'bg-green-500',
    entretenimiento: 'bg-amber-500',
    transporte: 'bg-red-500',
    vivienda: 'bg-blue-500',
    servicios: 'bg-indigo-500',
    income: 'bg-emerald-500',
    expense: 'bg-red-500'
  };

  return colorMap[category] || (type === 'income' ? colorMap.income : colorMap.expense);
};

export function TransactionsPage() {
  const { theme } = useTheme();
  const { transactions, loading, error, deleteTransaction } = useTransactionContext();
  
  // State for filters and search
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState<'all' | 'income' | 'expense'>('all');
  const [sortBy, setSortBy] = useState<'date' | 'amount' | 'description'>('date');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [showFilters, setShowFilters] = useState(false);

  // Filter and sort transactions
  const filteredTransactions = useMemo(() => {
    let filtered = transactions.filter(transaction => {
      const matchesSearch = transaction.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           transaction.category.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesType = filterType === 'all' || transaction.type === filterType;
      
      return matchesSearch && matchesType;
    });

    // Sort transactions
    filtered.sort((a, b) => {
      let comparison = 0;
      
      switch (sortBy) {
        case 'date':
          comparison = new Date(a.date).getTime() - new Date(b.date).getTime();
          break;
        case 'amount':
          comparison = a.amount - b.amount;
          break;
        case 'description':
          comparison = a.description.localeCompare(b.description);
          break;
      }
      
      return sortOrder === 'asc' ? comparison : -comparison;
    });

    return filtered;
  }, [transactions, searchTerm, filterType, sortBy, sortOrder]);

  // Calculate totals
  const totals = useMemo(() => {
    const income = filteredTransactions
      .filter(t => t.type === 'income')
      .reduce((sum, t) => sum + t.amount, 0);
    
    const expenses = filteredTransactions
      .filter(t => t.type === 'expense')
      .reduce((sum, t) => sum + t.amount, 0);
    
    return { income, expenses, net: income - expenses };
  }, [filteredTransactions]);

  const handleDelete = async (id: string) => {
    if (window.confirm('¿Estás seguro de que quieres eliminar esta transacción?')) {
      await deleteTransaction(id);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-[400px]">
        <LoadingSpinner />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className={`text-3xl font-bold transition-colors duration-200 ${
            theme === 'dark' ? 'text-white' : 'text-slate-900'
          }`}>
            Transacciones
          </h1>
          <p className={`text-sm mt-1 transition-colors duration-200 ${
            theme === 'dark' ? 'text-gray-400' : 'text-slate-500'
          }`}>
            Gestiona todas tus transacciones
          </p>
        </div>
        <AddTransactionButton />
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className={`rounded-xl p-4 transition-colors duration-200 ${
          theme === 'dark' 
            ? 'bg-[#21222d] border border-[#2a2a3d]' 
            : 'bg-slate-50 border border-slate-200'
        }`}>
          <div className="flex items-center">
            <div className="p-2 bg-green-500 rounded-lg">
              <TrendingUp className="w-5 h-5 text-white" />
            </div>
            <div className="ml-3">
              <p className={`text-sm transition-colors duration-200 ${
                theme === 'dark' ? 'text-gray-400' : 'text-slate-500'
              }`}>
                Ingresos
              </p>
              <p className={`text-lg font-bold text-green-500`}>
                ${totals.income.toLocaleString()}
              </p>
            </div>
          </div>
        </div>

        <div className={`rounded-xl p-4 transition-colors duration-200 ${
          theme === 'dark' 
            ? 'bg-[#21222d] border border-[#2a2a3d]' 
            : 'bg-slate-50 border border-slate-200'
        }`}>
          <div className="flex items-center">
            <div className="p-2 bg-red-500 rounded-lg">
              <TrendingDown className="w-5 h-5 text-white" />
            </div>
            <div className="ml-3">
              <p className={`text-sm transition-colors duration-200 ${
                theme === 'dark' ? 'text-gray-400' : 'text-slate-500'
              }`}>
                Gastos
              </p>
              <p className={`text-lg font-bold text-red-500`}>
                ${totals.expenses.toLocaleString()}
              </p>
            </div>
          </div>
        </div>

        <div className={`rounded-xl p-4 transition-colors duration-200 ${
          theme === 'dark' 
            ? 'bg-[#21222d] border border-[#2a2a3d]' 
            : 'bg-slate-50 border border-slate-200'
        }`}>
          <div className="flex items-center">
            <div className={`p-2 rounded-lg ${totals.net >= 0 ? 'bg-blue-500' : 'bg-orange-500'}`}>
              <DollarSign className="w-5 h-5 text-white" />
            </div>
            <div className="ml-3">
              <p className={`text-sm transition-colors duration-200 ${
                theme === 'dark' ? 'text-gray-400' : 'text-slate-500'
              }`}>
                Balance
              </p>
              <p className={`text-lg font-bold ${
                totals.net >= 0 ? 'text-blue-500' : 'text-orange-500'
              }`}>
                ${totals.net.toLocaleString()}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Search and Filters */}
      <div className={`rounded-xl p-4 transition-colors duration-200 ${
        theme === 'dark' 
          ? 'bg-[#21222d] border border-[#2a2a3d]' 
          : 'bg-slate-50 border border-slate-200'
      }`}>
        <div className="flex flex-col sm:flex-row gap-4">
          {/* Search */}
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
              <input
                type="text"
                placeholder="Buscar transacciones..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className={`w-full pl-10 pr-4 py-2 rounded-lg border transition-colors duration-200 ${
                  theme === 'dark'
                    ? 'bg-[#2a2b38] border-gray-600 text-white placeholder-gray-400'
                    : 'bg-white border-slate-300 text-slate-900 placeholder-slate-400'
                }`}
              />
            </div>
          </div>

          {/* Filter Toggle */}
          <button
            onClick={() => setShowFilters(!showFilters)}
            className={`px-4 py-2 rounded-lg border transition-colors duration-200 flex items-center gap-2 ${
              theme === 'dark'
                ? 'bg-[#2a2b38] border-gray-600 text-white hover:bg-[#32334a]'
                : 'bg-white border-slate-300 text-slate-900 hover:bg-slate-50'
            }`}
          >
            <Filter className="w-4 h-4" />
            Filtros
            <ChevronDown className={`w-4 h-4 transition-transform ${showFilters ? 'rotate-180' : ''}`} />
          </button>
        </div>

        {/* Expanded Filters */}
        {showFilters && (
          <div className="mt-4 pt-4 border-t border-gray-600 grid grid-cols-1 sm:grid-cols-3 gap-4">
            {/* Type Filter */}
            <div>
              <label className={`block text-sm font-medium mb-2 ${
                theme === 'dark' ? 'text-gray-300' : 'text-slate-700'
              }`}>
                Tipo
              </label>
              <select
                value={filterType}
                onChange={(e) => setFilterType(e.target.value as 'all' | 'income' | 'expense')}
                className={`w-full px-3 py-2 rounded-lg border transition-colors duration-200 ${
                  theme === 'dark'
                    ? 'bg-[#2a2b38] border-gray-600 text-white'
                    : 'bg-white border-slate-300 text-slate-900'
                }`}
              >
                <option value="all">Todos</option>
                <option value="income">Ingresos</option>
                <option value="expense">Gastos</option>
              </select>
            </div>

            {/* Sort By */}
            <div>
              <label className={`block text-sm font-medium mb-2 ${
                theme === 'dark' ? 'text-gray-300' : 'text-slate-700'
              }`}>
                Ordenar por
              </label>
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value as 'date' | 'amount' | 'description')}
                className={`w-full px-3 py-2 rounded-lg border transition-colors duration-200 ${
                  theme === 'dark'
                    ? 'bg-[#2a2b38] border-gray-600 text-white'
                    : 'bg-white border-slate-300 text-slate-900'
                }`}
              >
                <option value="date">Fecha</option>
                <option value="amount">Monto</option>
                <option value="description">Descripción</option>
              </select>
            </div>

            {/* Sort Order */}
            <div>
              <label className={`block text-sm font-medium mb-2 ${
                theme === 'dark' ? 'text-gray-300' : 'text-slate-700'
              }`}>
                Orden
              </label>
              <button
                onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
                className={`w-full px-3 py-2 rounded-lg border transition-colors duration-200 flex items-center justify-center gap-2 ${
                  theme === 'dark'
                    ? 'bg-[#2a2b38] border-gray-600 text-white hover:bg-[#32334a]'
                    : 'bg-white border-slate-300 text-slate-900 hover:bg-slate-50'
                }`}
              >
                <ArrowUpDown className="w-4 h-4" />
                {sortOrder === 'asc' ? 'Ascendente' : 'Descendente'}
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Transactions List */}
      <div className={`rounded-xl transition-colors duration-200 ${
        theme === 'dark'
          ? 'bg-[#21222d] border border-[#2a2a3d]'
          : 'bg-slate-50 border border-slate-200'
      }`}>
        <div className="p-4 border-b border-gray-600">
          <div className="flex justify-between items-center">
            <h2 className={`text-lg font-semibold transition-colors duration-200 ${
              theme === 'dark' ? 'text-white' : 'text-slate-900'
            }`}>
              Transacciones ({filteredTransactions.length})
            </h2>
          </div>
        </div>

        {error && (
          <div className="p-4">
            <div className="bg-red-900/30 border border-red-800 text-red-300 p-3 rounded-lg">
              <p className="text-sm">Error: {error}</p>
            </div>
          </div>
        )}

        {!error && filteredTransactions.length === 0 && (
          <div className={`text-center py-12 transition-colors duration-200 ${
            theme === 'dark' ? 'text-gray-500' : 'text-slate-500'
          }`}>
            <Eye className="w-12 h-12 mx-auto mb-4 opacity-50" />
            <p className="font-medium">No se encontraron transacciones</p>
            <p className="text-sm mt-1">
              {searchTerm || filterType !== 'all'
                ? 'Intenta ajustar los filtros de búsqueda'
                : 'Agrega tu primera transacción para comenzar'
              }
            </p>
          </div>
        )}

        {!error && filteredTransactions.length > 0 && (
          <div className="divide-y divide-gray-600">
            {filteredTransactions.map((transaction) => (
              <div
                key={transaction.id}
                className={`p-4 transition-colors duration-200 ${
                  theme === 'dark' ? 'hover:bg-[#2a2b38]' : 'hover:bg-slate-100'
                }`}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center flex-1 min-w-0">
                    <div className={`w-10 h-10 rounded-full ${getIconBackground(transaction.category, transaction.type)} flex items-center justify-center text-white font-medium mr-4 flex-shrink-0`}>
                      {transaction.icon || (transaction.type === 'income' ? '💰' : '💸')}
                    </div>

                    <div className="min-w-0 flex-1">
                      <div className="flex items-center justify-between">
                        <h3 className={`font-medium truncate transition-colors duration-200 ${
                          theme === 'dark' ? 'text-white' : 'text-slate-900'
                        }`}>
                          {transaction.description}
                        </h3>

                        <div className="flex items-center gap-3 ml-4">
                          <span className={`text-lg font-bold ${
                            transaction.type === 'income' ? 'text-green-500' : 'text-red-500'
                          }`}>
                            {transaction.type === 'income' ? '+' : '-'}${transaction.amount.toLocaleString()}
                          </span>

                          <div className="flex items-center gap-2">
                            <button
                              className={`p-1 rounded transition-colors duration-200 ${
                                theme === 'dark'
                                  ? 'text-gray-400 hover:text-blue-400 hover:bg-blue-900/20'
                                  : 'text-slate-400 hover:text-blue-600 hover:bg-blue-50'
                              }`}
                              title="Editar transacción"
                            >
                              <Edit className="w-4 h-4" />
                            </button>

                            <button
                              onClick={() => handleDelete(transaction.id)}
                              className={`p-1 rounded transition-colors duration-200 ${
                                theme === 'dark'
                                  ? 'text-gray-400 hover:text-red-400 hover:bg-red-900/20'
                                  : 'text-slate-400 hover:text-red-600 hover:bg-red-50'
                              }`}
                              title="Eliminar transacción"
                            >
                              <Trash2 className="w-4 h-4" />
                            </button>
                          </div>
                        </div>
                      </div>

                      <div className="flex items-center gap-4 mt-1">
                        <span className={`text-sm transition-colors duration-200 ${
                          theme === 'dark' ? 'text-gray-400' : 'text-slate-500'
                        }`}>
                          {format(new Date(transaction.date), 'dd MMM yyyy', { locale: es })}
                        </span>

                        <span className={`inline-flex rounded-full px-2 py-0.5 text-xs font-medium ${
                          transaction.type === 'income'
                            ? 'bg-green-900/30 text-green-400'
                            : 'bg-red-900/30 text-red-400'
                        }`}>
                          {transaction.category}
                        </span>

                        <span className={`inline-flex rounded-full px-2 py-0.5 text-xs font-medium ${
                          transaction.type === 'income'
                            ? 'bg-emerald-900/30 text-emerald-400'
                            : 'bg-orange-900/30 text-orange-400'
                        }`}>
                          {transaction.type === 'income' ? 'Ingreso' : 'Gasto'}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
