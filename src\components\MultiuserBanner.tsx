import { InfoIcon, Users } from 'lucide-react';
import { useState } from 'react';
import { useTheme } from '../lib/ThemeContext';

export function MultiuserBanner() {
  const { theme } = useTheme();
  const [isVisible, setIsVisible] = useState(true);
  
  if (!isVisible) return null;
  
  return (
    <div className={`relative rounded-xl p-4 mb-6 flex items-center gap-3 animate-fadeIn ${
      theme === 'dark' 
        ? 'bg-indigo-900/30 border border-indigo-800' 
        : 'bg-gradient-to-r from-indigo-50 to-blue-50 border border-indigo-100'
    }`}>
      <div className={`flex-shrink-0 rounded-full p-2 ${
        theme === 'dark' ? 'bg-indigo-800' : 'bg-indigo-100'
      }`}>
        <Users className={`w-5 h-5 ${
          theme === 'dark' ? 'text-indigo-200' : 'text-indigo-600'
        }`} />
      </div>
      
      <div className="flex-grow">
        <p className={`text-sm font-medium ${
          theme === 'dark' ? 'text-indigo-200' : 'text-indigo-800'
        }`}>
          <span className="font-bold">¡Próximamente modo multiusuario!</span> Comparte el control de tus finanzas con tu pareja o familia.
        </p>
        <p className={`text-xs mt-1 ${
          theme === 'dark' ? 'text-indigo-300' : 'text-indigo-600'
        }`}>
          <InfoIcon className="w-3 h-3 inline mr-1" />
          Serás de los primeros en recibir acceso cuando esta función esté disponible.
        </p>
      </div>
      
      <button 
        className={`absolute top-2 right-2 ${
          theme === 'dark' ? 'text-indigo-400 hover:text-indigo-200' : 'text-indigo-400 hover:text-indigo-600'
        }`}
        onClick={() => setIsVisible(false)}
        aria-label="Cerrar banner"
      >
        &times;
      </button>
    </div>
  );
}