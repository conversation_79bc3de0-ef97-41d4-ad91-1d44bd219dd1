import { createClient } from '@supabase/supabase-js';

// Obtener las variables de entorno
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
const supabaseKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

// Verificar que las variables de entorno estén configuradas
if (!supabaseUrl || !supabaseKey) {
  throw new Error(
    'Variables de entorno de Supabase no configuradas. ' +
    'Asegúrate de tener VITE_SUPABASE_URL y VITE_SUPABASE_ANON_KEY en tu archivo .env'
  );
}

// Crear y exportar el cliente de Supabase
export const supabase = createClient(supabaseUrl, supabaseKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: false
  }
});

// Función helper para manejar errores de Supabase
export const handleSupabaseError = (error: any) => {
  console.error('Error de Supabase:', error);
  return {
    success: false,
    error: error.message || 'Ocurrió un error inesperado'
  };
};

// Función helper para respuestas exitosas
export const handleSupabaseSuccess = (data: any) => {
  return {
    success: true,
    data
  };
};