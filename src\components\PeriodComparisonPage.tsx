import React, { useState, useMemo } from 'react';
import { Calendar, TrendingUp, TrendingDown, BarChart3, ArrowUpDown, Percent, DollarSign } from 'lucide-react';
import { useTheme } from '../lib/ThemeContext';
import { useTransactionContext } from '../lib/TransactionContext';
import { FadeIn, SlideIn } from './ui/LoadingStates';
import { InfoTooltip, HelpTooltip } from './ui/Tooltip';
import { format, startOfMonth, endOfMonth, startOfYear, endOfYear, parseISO, isWithinInterval, subMonths, subYears } from 'date-fns';
import { es } from 'date-fns/locale';

interface PeriodData {
  label: string;
  startDate: Date;
  endDate: Date;
  income: number;
  expenses: number;
  balance: number;
  transactionCount: number;
  categoryBreakdown: Record<string, number>;
  avgTransactionAmount: number;
}

interface ComparisonMetrics {
  incomeChange: number;
  incomeChangePercent: number;
  expensesChange: number;
  expensesChangePercent: number;
  balanceChange: number;
  balanceChangePercent: number;
  transactionCountChange: number;
  transactionCountChangePercent: number;
}

type ComparisonType = 'month-to-month' | 'quarter-to-quarter' | 'year-to-year' | 'custom';

export function PeriodComparisonPage() {
  const { theme } = useTheme();
  const { transactions } = useTransactionContext();
  
  const [comparisonType, setComparisonType] = useState<ComparisonType>('month-to-month');
  const [customPeriod1Start, setCustomPeriod1Start] = useState('');
  const [customPeriod1End, setCustomPeriod1End] = useState('');
  const [customPeriod2Start, setCustomPeriod2Start] = useState('');
  const [customPeriod2End, setCustomPeriod2End] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  // Get available categories
  const availableCategories = useMemo(() => {
    const categories = new Set(transactions.map(t => t.category));
    return ['all', ...Array.from(categories)];
  }, [transactions]);

  // Calculate period data
  const calculatePeriodData = (startDate: Date, endDate: Date, label: string): PeriodData => {
    const periodTransactions = transactions.filter(t => {
      const transactionDate = parseISO(t.date);
      return isWithinInterval(transactionDate, { start: startDate, end: endDate }) &&
             (selectedCategory === 'all' || t.category === selectedCategory);
    });

    const income = periodTransactions
      .filter(t => t.type === 'income')
      .reduce((sum, t) => sum + t.amount, 0);

    const expenses = periodTransactions
      .filter(t => t.type === 'expense')
      .reduce((sum, t) => sum + t.amount, 0);

    const categoryBreakdown: Record<string, number> = {};
    periodTransactions.forEach(t => {
      if (!categoryBreakdown[t.category]) {
        categoryBreakdown[t.category] = 0;
      }
      categoryBreakdown[t.category] += t.amount;
    });

    const avgTransactionAmount = periodTransactions.length > 0 
      ? periodTransactions.reduce((sum, t) => sum + t.amount, 0) / periodTransactions.length 
      : 0;

    return {
      label,
      startDate,
      endDate,
      income,
      expenses,
      balance: income - expenses,
      transactionCount: periodTransactions.length,
      categoryBreakdown,
      avgTransactionAmount
    };
  };

  // Get comparison periods based on type
  const comparisonData = useMemo((): { period1: PeriodData; period2: PeriodData } | null => {
    const now = new Date();

    switch (comparisonType) {
      case 'month-to-month': {
        const currentMonthStart = startOfMonth(now);
        const currentMonthEnd = endOfMonth(now);
        const lastMonthStart = startOfMonth(subMonths(now, 1));
        const lastMonthEnd = endOfMonth(subMonths(now, 1));

        return {
          period1: calculatePeriodData(
            lastMonthStart, 
            lastMonthEnd, 
            format(lastMonthStart, 'MMMM yyyy', { locale: es })
          ),
          period2: calculatePeriodData(
            currentMonthStart, 
            currentMonthEnd, 
            format(currentMonthStart, 'MMMM yyyy', { locale: es })
          )
        };
      }

      case 'quarter-to-quarter': {
        const currentQuarterStart = new Date(now.getFullYear(), Math.floor(now.getMonth() / 3) * 3, 1);
        const currentQuarterEnd = new Date(now.getFullYear(), Math.floor(now.getMonth() / 3) * 3 + 3, 0);
        const lastQuarterStart = new Date(currentQuarterStart.getFullYear(), currentQuarterStart.getMonth() - 3, 1);
        const lastQuarterEnd = new Date(currentQuarterStart.getFullYear(), currentQuarterStart.getMonth(), 0);

        return {
          period1: calculatePeriodData(
            lastQuarterStart, 
            lastQuarterEnd, 
            `Q${Math.floor(lastQuarterStart.getMonth() / 3) + 1} ${lastQuarterStart.getFullYear()}`
          ),
          period2: calculatePeriodData(
            currentQuarterStart, 
            currentQuarterEnd, 
            `Q${Math.floor(currentQuarterStart.getMonth() / 3) + 1} ${currentQuarterStart.getFullYear()}`
          )
        };
      }

      case 'year-to-year': {
        const currentYearStart = startOfYear(now);
        const currentYearEnd = endOfYear(now);
        const lastYearStart = startOfYear(subYears(now, 1));
        const lastYearEnd = endOfYear(subYears(now, 1));

        return {
          period1: calculatePeriodData(
            lastYearStart, 
            lastYearEnd, 
            lastYearStart.getFullYear().toString()
          ),
          period2: calculatePeriodData(
            currentYearStart, 
            currentYearEnd, 
            currentYearStart.getFullYear().toString()
          )
        };
      }

      case 'custom': {
        if (!customPeriod1Start || !customPeriod1End || !customPeriod2Start || !customPeriod2End) {
          return null;
        }

        const period1Start = parseISO(customPeriod1Start);
        const period1End = parseISO(customPeriod1End);
        const period2Start = parseISO(customPeriod2Start);
        const period2End = parseISO(customPeriod2End);

        return {
          period1: calculatePeriodData(
            period1Start, 
            period1End, 
            `${format(period1Start, 'dd/MM/yyyy')} - ${format(period1End, 'dd/MM/yyyy')}`
          ),
          period2: calculatePeriodData(
            period2Start, 
            period2End, 
            `${format(period2Start, 'dd/MM/yyyy')} - ${format(period2End, 'dd/MM/yyyy')}`
          )
        };
      }

      default:
        return null;
    }
  }, [comparisonType, customPeriod1Start, customPeriod1End, customPeriod2Start, customPeriod2End, selectedCategory, transactions]);

  // Calculate comparison metrics
  const metrics = useMemo((): ComparisonMetrics | null => {
    if (!comparisonData) return null;

    const { period1, period2 } = comparisonData;

    const calculateChange = (oldValue: number, newValue: number) => {
      const change = newValue - oldValue;
      const changePercent = oldValue !== 0 ? (change / Math.abs(oldValue)) * 100 : 0;
      return { change, changePercent };
    };

    const incomeChange = calculateChange(period1.income, period2.income);
    const expensesChange = calculateChange(period1.expenses, period2.expenses);
    const balanceChange = calculateChange(period1.balance, period2.balance);
    const transactionCountChange = calculateChange(period1.transactionCount, period2.transactionCount);

    return {
      incomeChange: incomeChange.change,
      incomeChangePercent: incomeChange.changePercent,
      expensesChange: expensesChange.change,
      expensesChangePercent: expensesChange.changePercent,
      balanceChange: balanceChange.change,
      balanceChangePercent: balanceChange.changePercent,
      transactionCountChange: transactionCountChange.change,
      transactionCountChangePercent: transactionCountChange.changePercent
    };
  }, [comparisonData]);

  // Get top categories for comparison
  const categoryComparison = useMemo(() => {
    if (!comparisonData) return [];

    const { period1, period2 } = comparisonData;
    const allCategories = new Set([
      ...Object.keys(period1.categoryBreakdown),
      ...Object.keys(period2.categoryBreakdown)
    ]);

    return Array.from(allCategories).map(category => {
      const period1Amount = period1.categoryBreakdown[category] || 0;
      const period2Amount = period2.categoryBreakdown[category] || 0;
      const change = period2Amount - period1Amount;
      const changePercent = period1Amount !== 0 ? (change / period1Amount) * 100 : 0;

      return {
        category,
        period1Amount,
        period2Amount,
        change,
        changePercent
      };
    }).sort((a, b) => Math.abs(b.change) - Math.abs(a.change));
  }, [comparisonData]);

  const formatChange = (value: number, isPercent: boolean = false) => {
    const prefix = value >= 0 ? '+' : '';
    const suffix = isPercent ? '%' : '';
    return `${prefix}${value.toLocaleString()}${suffix}`;
  };

  const getChangeColor = (value: number, isExpense: boolean = false) => {
    if (value === 0) return theme === 'dark' ? 'text-gray-400' : 'text-slate-500';
    
    // For expenses, negative change (reduction) is good
    const isPositive = isExpense ? value < 0 : value > 0;
    return isPositive ? 'text-green-500' : 'text-red-500';
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div className="flex items-center gap-3">
          <div>
            <h1 className={`text-3xl font-bold transition-colors duration-200 ${
              theme === 'dark' ? 'text-white' : 'text-slate-900'
            }`}>
              Comparación de Períodos
            </h1>
            <p className={`text-sm mt-1 transition-colors duration-200 ${
              theme === 'dark' ? 'text-gray-400' : 'text-slate-500'
            }`}>
              Analiza la evolución de tus finanzas entre diferentes períodos
            </p>
          </div>
          <HelpTooltip content="La comparación de períodos te permite analizar cómo han evolucionado tus ingresos, gastos y patrones financieros a lo largo del tiempo." />
        </div>
      </div>

      {/* Controls */}
      <div className={`rounded-xl p-4 transition-colors duration-200 ${
        theme === 'dark' 
          ? 'bg-[#21222d] border border-[#2a2a3d]' 
          : 'bg-slate-50 border border-slate-200'
      }`}>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {/* Comparison Type */}
          <div>
            <label className={`block text-sm font-medium mb-2 transition-colors duration-200 ${
              theme === 'dark' ? 'text-gray-300' : 'text-slate-700'
            }`}>
              Tipo de Comparación
            </label>
            <select
              value={comparisonType}
              onChange={(e) => setComparisonType(e.target.value as ComparisonType)}
              className={`w-full px-3 py-2 rounded-lg border transition-colors duration-200 ${
                theme === 'dark'
                  ? 'bg-[#2a2b38] border-gray-600 text-white'
                  : 'bg-white border-slate-300 text-slate-900'
              }`}
            >
              <option value="month-to-month">Mes a Mes</option>
              <option value="quarter-to-quarter">Trimestre a Trimestre</option>
              <option value="year-to-year">Año a Año</option>
              <option value="custom">Personalizado</option>
            </select>
          </div>

          {/* Category Filter */}
          <div>
            <label className={`block text-sm font-medium mb-2 transition-colors duration-200 ${
              theme === 'dark' ? 'text-gray-300' : 'text-slate-700'
            }`}>
              Categoría
            </label>
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className={`w-full px-3 py-2 rounded-lg border transition-colors duration-200 ${
                theme === 'dark'
                  ? 'bg-[#2a2b38] border-gray-600 text-white'
                  : 'bg-white border-slate-300 text-slate-900'
              }`}
            >
              <option value="all">Todas las categorías</option>
              {availableCategories.slice(1).map(category => (
                <option key={category} value={category}>
                  {category}
                </option>
              ))}
            </select>
          </div>
        </div>

        {/* Custom Date Ranges */}
        {comparisonType === 'custom' && (
          <div className="mt-4 grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className={`font-medium mb-3 transition-colors duration-200 ${
                theme === 'dark' ? 'text-white' : 'text-slate-900'
              }`}>
                Período 1
              </h4>
              <div className="grid grid-cols-2 gap-3">
                <div>
                  <label className={`block text-sm font-medium mb-1 transition-colors duration-200 ${
                    theme === 'dark' ? 'text-gray-300' : 'text-slate-700'
                  }`}>
                    Desde
                  </label>
                  <input
                    type="date"
                    value={customPeriod1Start}
                    onChange={(e) => setCustomPeriod1Start(e.target.value)}
                    className={`w-full px-3 py-2 rounded-lg border transition-colors duration-200 ${
                      theme === 'dark'
                        ? 'bg-[#2a2b38] border-gray-600 text-white'
                        : 'bg-white border-slate-300 text-slate-900'
                    }`}
                  />
                </div>
                <div>
                  <label className={`block text-sm font-medium mb-1 transition-colors duration-200 ${
                    theme === 'dark' ? 'text-gray-300' : 'text-slate-700'
                  }`}>
                    Hasta
                  </label>
                  <input
                    type="date"
                    value={customPeriod1End}
                    onChange={(e) => setCustomPeriod1End(e.target.value)}
                    className={`w-full px-3 py-2 rounded-lg border transition-colors duration-200 ${
                      theme === 'dark'
                        ? 'bg-[#2a2b38] border-gray-600 text-white'
                        : 'bg-white border-slate-300 text-slate-900'
                    }`}
                  />
                </div>
              </div>
            </div>

            <div>
              <h4 className={`font-medium mb-3 transition-colors duration-200 ${
                theme === 'dark' ? 'text-white' : 'text-slate-900'
              }`}>
                Período 2
              </h4>
              <div className="grid grid-cols-2 gap-3">
                <div>
                  <label className={`block text-sm font-medium mb-1 transition-colors duration-200 ${
                    theme === 'dark' ? 'text-gray-300' : 'text-slate-700'
                  }`}>
                    Desde
                  </label>
                  <input
                    type="date"
                    value={customPeriod2Start}
                    onChange={(e) => setCustomPeriod2Start(e.target.value)}
                    className={`w-full px-3 py-2 rounded-lg border transition-colors duration-200 ${
                      theme === 'dark'
                        ? 'bg-[#2a2b38] border-gray-600 text-white'
                        : 'bg-white border-slate-300 text-slate-900'
                    }`}
                  />
                </div>
                <div>
                  <label className={`block text-sm font-medium mb-1 transition-colors duration-200 ${
                    theme === 'dark' ? 'text-gray-300' : 'text-slate-700'
                  }`}>
                    Hasta
                  </label>
                  <input
                    type="date"
                    value={customPeriod2End}
                    onChange={(e) => setCustomPeriod2End(e.target.value)}
                    className={`w-full px-3 py-2 rounded-lg border transition-colors duration-200 ${
                      theme === 'dark'
                        ? 'bg-[#2a2b38] border-gray-600 text-white'
                        : 'bg-white border-slate-300 text-slate-900'
                    }`}
                  />
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Comparison Results */}
      {comparisonData && metrics && (
        <div className="space-y-6">
          {/* Period Overview */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Period 1 */}
            <div className={`rounded-xl p-6 transition-colors duration-200 ${
              theme === 'dark'
                ? 'bg-[#21222d] border border-[#2a2a3d]'
                : 'bg-slate-50 border border-slate-200'
            }`}>
              <h3 className={`text-lg font-semibold mb-4 transition-colors duration-200 ${
                theme === 'dark' ? 'text-white' : 'text-slate-900'
              }`}>
                {comparisonData.period1.label}
              </h3>

              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className={`transition-colors duration-200 ${
                    theme === 'dark' ? 'text-gray-400' : 'text-slate-600'
                  }`}>
                    Ingresos:
                  </span>
                  <span className="text-green-500 font-medium">
                    ${comparisonData.period1.income.toLocaleString()}
                  </span>
                </div>

                <div className="flex justify-between items-center">
                  <span className={`transition-colors duration-200 ${
                    theme === 'dark' ? 'text-gray-400' : 'text-slate-600'
                  }`}>
                    Gastos:
                  </span>
                  <span className="text-red-500 font-medium">
                    ${comparisonData.period1.expenses.toLocaleString()}
                  </span>
                </div>

                <div className="flex justify-between items-center">
                  <span className={`transition-colors duration-200 ${
                    theme === 'dark' ? 'text-gray-400' : 'text-slate-600'
                  }`}>
                    Balance:
                  </span>
                  <span className={`font-medium ${
                    comparisonData.period1.balance >= 0 ? 'text-blue-500' : 'text-orange-500'
                  }`}>
                    ${comparisonData.period1.balance.toLocaleString()}
                  </span>
                </div>

                <div className="flex justify-between items-center">
                  <span className={`transition-colors duration-200 ${
                    theme === 'dark' ? 'text-gray-400' : 'text-slate-600'
                  }`}>
                    Transacciones:
                  </span>
                  <span className={`font-medium transition-colors duration-200 ${
                    theme === 'dark' ? 'text-white' : 'text-slate-900'
                  }`}>
                    {comparisonData.period1.transactionCount}
                  </span>
                </div>
              </div>
            </div>

            {/* Period 2 */}
            <div className={`rounded-xl p-6 transition-colors duration-200 ${
              theme === 'dark'
                ? 'bg-[#21222d] border border-[#2a2a3d]'
                : 'bg-slate-50 border border-slate-200'
            }`}>
              <h3 className={`text-lg font-semibold mb-4 transition-colors duration-200 ${
                theme === 'dark' ? 'text-white' : 'text-slate-900'
              }`}>
                {comparisonData.period2.label}
              </h3>

              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className={`transition-colors duration-200 ${
                    theme === 'dark' ? 'text-gray-400' : 'text-slate-600'
                  }`}>
                    Ingresos:
                  </span>
                  <span className="text-green-500 font-medium">
                    ${comparisonData.period2.income.toLocaleString()}
                  </span>
                </div>

                <div className="flex justify-between items-center">
                  <span className={`transition-colors duration-200 ${
                    theme === 'dark' ? 'text-gray-400' : 'text-slate-600'
                  }`}>
                    Gastos:
                  </span>
                  <span className="text-red-500 font-medium">
                    ${comparisonData.period2.expenses.toLocaleString()}
                  </span>
                </div>

                <div className="flex justify-between items-center">
                  <span className={`transition-colors duration-200 ${
                    theme === 'dark' ? 'text-gray-400' : 'text-slate-600'
                  }`}>
                    Balance:
                  </span>
                  <span className={`font-medium ${
                    comparisonData.period2.balance >= 0 ? 'text-blue-500' : 'text-orange-500'
                  }`}>
                    ${comparisonData.period2.balance.toLocaleString()}
                  </span>
                </div>

                <div className="flex justify-between items-center">
                  <span className={`transition-colors duration-200 ${
                    theme === 'dark' ? 'text-gray-400' : 'text-slate-600'
                  }`}>
                    Transacciones:
                  </span>
                  <span className={`font-medium transition-colors duration-200 ${
                    theme === 'dark' ? 'text-white' : 'text-slate-900'
                  }`}>
                    {comparisonData.period2.transactionCount}
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Change Metrics */}
          <div className={`rounded-xl p-6 transition-colors duration-200 ${
            theme === 'dark'
              ? 'bg-[#21222d] border border-[#2a2a3d]'
              : 'bg-slate-50 border border-slate-200'
          }`}>
            <h3 className={`text-lg font-semibold mb-4 transition-colors duration-200 ${
              theme === 'dark' ? 'text-white' : 'text-slate-900'
            }`}>
              Cambios entre Períodos
            </h3>

            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <InfoTooltip title="Cambio en Ingresos" description="Variación absoluta y porcentual de los ingresos entre períodos">
                <div className={`p-4 rounded-lg transition-colors duration-200 ${
                  theme === 'dark'
                    ? 'bg-[#2a2b38] border border-gray-600'
                    : 'bg-white border border-slate-200'
                }`}>
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center gap-2">
                      <TrendingUp className="w-4 h-4 text-green-500" />
                      <span className={`text-sm transition-colors duration-200 ${
                        theme === 'dark' ? 'text-gray-400' : 'text-slate-500'
                      }`}>
                        Ingresos
                      </span>
                    </div>
                    <ArrowUpDown className="w-4 h-4 text-gray-500" />
                  </div>
                  <div className="space-y-1">
                    <p className={`text-lg font-bold ${getChangeColor(metrics.incomeChange)}`}>
                      {formatChange(metrics.incomeChange)}
                    </p>
                    <p className={`text-sm ${getChangeColor(metrics.incomeChangePercent)}`}>
                      {formatChange(metrics.incomeChangePercent.toFixed(1), true)}
                    </p>
                  </div>
                </div>
              </InfoTooltip>

              <div className={`p-4 rounded-lg transition-colors duration-200 ${
                theme === 'dark'
                  ? 'bg-[#2a2b38] border border-gray-600'
                  : 'bg-white border border-slate-200'
              }`}>
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    <TrendingDown className="w-4 h-4 text-red-500" />
                    <span className={`text-sm transition-colors duration-200 ${
                      theme === 'dark' ? 'text-gray-400' : 'text-slate-500'
                    }`}>
                      Gastos
                    </span>
                  </div>
                  <ArrowUpDown className="w-4 h-4 text-gray-500" />
                </div>
                <div className="space-y-1">
                  <p className={`text-lg font-bold ${getChangeColor(metrics.expensesChange, true)}`}>
                    {formatChange(metrics.expensesChange)}
                  </p>
                  <p className={`text-sm ${getChangeColor(metrics.expensesChangePercent, true)}`}>
                    {formatChange(metrics.expensesChangePercent.toFixed(1), true)}
                  </p>
                </div>
              </div>

              <div className={`p-4 rounded-lg transition-colors duration-200 ${
                theme === 'dark'
                  ? 'bg-[#2a2b38] border border-gray-600'
                  : 'bg-white border border-slate-200'
              }`}>
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    <BarChart3 className="w-4 h-4 text-blue-500" />
                    <span className={`text-sm transition-colors duration-200 ${
                      theme === 'dark' ? 'text-gray-400' : 'text-slate-500'
                    }`}>
                      Balance
                    </span>
                  </div>
                  <ArrowUpDown className="w-4 h-4 text-gray-500" />
                </div>
                <div className="space-y-1">
                  <p className={`text-lg font-bold ${getChangeColor(metrics.balanceChange)}`}>
                    {formatChange(metrics.balanceChange)}
                  </p>
                  <p className={`text-sm ${getChangeColor(metrics.balanceChangePercent)}`}>
                    {formatChange(metrics.balanceChangePercent.toFixed(1), true)}
                  </p>
                </div>
              </div>

              <div className={`p-4 rounded-lg transition-colors duration-200 ${
                theme === 'dark'
                  ? 'bg-[#2a2b38] border border-gray-600'
                  : 'bg-white border border-slate-200'
              }`}>
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    <Calendar className="w-4 h-4 text-purple-500" />
                    <span className={`text-sm transition-colors duration-200 ${
                      theme === 'dark' ? 'text-gray-400' : 'text-slate-500'
                    }`}>
                      Transacciones
                    </span>
                  </div>
                  <ArrowUpDown className="w-4 h-4 text-gray-500" />
                </div>
                <div className="space-y-1">
                  <p className={`text-lg font-bold ${getChangeColor(metrics.transactionCountChange)}`}>
                    {formatChange(metrics.transactionCountChange)}
                  </p>
                  <p className={`text-sm ${getChangeColor(metrics.transactionCountChangePercent)}`}>
                    {formatChange(metrics.transactionCountChangePercent.toFixed(1), true)}
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Category Comparison */}
          {categoryComparison.length > 0 && (
            <div className={`rounded-xl transition-colors duration-200 ${
              theme === 'dark'
                ? 'bg-[#21222d] border border-[#2a2a3d]'
                : 'bg-slate-50 border border-slate-200'
            }`}>
              <div className="p-4 border-b border-gray-600">
                <h3 className={`text-lg font-semibold transition-colors duration-200 ${
                  theme === 'dark' ? 'text-white' : 'text-slate-900'
                }`}>
                  Comparación por Categorías
                </h3>
              </div>

              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className={`border-b transition-colors duration-200 ${
                      theme === 'dark' ? 'border-gray-600' : 'border-slate-200'
                    }`}>
                      <th className={`text-left p-4 font-medium transition-colors duration-200 ${
                        theme === 'dark' ? 'text-gray-300' : 'text-slate-700'
                      }`}>
                        Categoría
                      </th>
                      <th className={`text-right p-4 font-medium transition-colors duration-200 ${
                        theme === 'dark' ? 'text-gray-300' : 'text-slate-700'
                      }`}>
                        {comparisonData.period1.label}
                      </th>
                      <th className={`text-right p-4 font-medium transition-colors duration-200 ${
                        theme === 'dark' ? 'text-gray-300' : 'text-slate-700'
                      }`}>
                        {comparisonData.period2.label}
                      </th>
                      <th className={`text-right p-4 font-medium transition-colors duration-200 ${
                        theme === 'dark' ? 'text-gray-300' : 'text-slate-700'
                      }`}>
                        Cambio
                      </th>
                      <th className={`text-right p-4 font-medium transition-colors duration-200 ${
                        theme === 'dark' ? 'text-gray-300' : 'text-slate-700'
                      }`}>
                        %
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {categoryComparison.slice(0, 10).map((item, index) => (
                      <SlideIn key={item.category} direction="up" delay={index * 50}>
                        <tr className={`border-b transition-colors duration-200 ${
                          theme === 'dark'
                            ? 'border-gray-600 hover:bg-[#2a2b38]'
                            : 'border-slate-200 hover:bg-slate-100'
                        }`}>
                          <td className={`p-4 font-medium transition-colors duration-200 ${
                            theme === 'dark' ? 'text-white' : 'text-slate-900'
                          }`}>
                            {item.category}
                          </td>
                          <td className={`p-4 text-right transition-colors duration-200 ${
                            theme === 'dark' ? 'text-gray-300' : 'text-slate-600'
                          }`}>
                            ${item.period1Amount.toLocaleString()}
                          </td>
                          <td className={`p-4 text-right transition-colors duration-200 ${
                            theme === 'dark' ? 'text-gray-300' : 'text-slate-600'
                          }`}>
                            ${item.period2Amount.toLocaleString()}
                          </td>
                          <td className={`p-4 text-right font-medium ${getChangeColor(item.change)}`}>
                            {formatChange(item.change)}
                          </td>
                          <td className={`p-4 text-right font-medium ${getChangeColor(item.changePercent)}`}>
                            {formatChange(item.changePercent.toFixed(1), true)}
                          </td>
                        </tr>
                      </SlideIn>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}
        </div>
      )}

      {/* No Data Message */}
      {!comparisonData && (
        <FadeIn>
          <div className={`text-center py-12 transition-colors duration-200 ${
            theme === 'dark' ? 'text-gray-500' : 'text-slate-500'
          }`}>
            <BarChart3 className="w-12 h-12 mx-auto mb-4 opacity-50" />
            <p className="font-medium">No hay datos para comparar</p>
            <p className="text-sm mt-1">
              {comparisonType === 'custom'
                ? 'Selecciona las fechas para ambos períodos'
                : 'No hay suficientes transacciones para realizar la comparación'
              }
            </p>
          </div>
        </FadeIn>
      )}
    </div>
  );
}
