import { useState, useCallback } from 'react';

export interface UploadedFile {
  id: string;
  name: string;
  size: number;
  type: string;
  url: string;
  uploadedAt: string;
  transactionId?: string;
}

interface UseFileUploadOptions {
  maxSize?: number; // in bytes
  allowedTypes?: string[];
  maxFiles?: number;
}

const defaultOptions: UseFileUploadOptions = {
  maxSize: 5 * 1024 * 1024, // 5MB
  allowedTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'application/pdf'],
  maxFiles: 5
};

export function useFileUpload(options: UseFileUploadOptions = {}) {
  const config = { ...defaultOptions, ...options };
  
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>(() => {
    const saved = localStorage.getItem('uploadedFiles');
    return saved ? JSON.parse(saved) : [];
  });
  
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);

  // Save files to localStorage
  const saveFiles = useCallback((files: UploadedFile[]) => {
    localStorage.setItem('uploadedFiles', JSON.stringify(files));
    setUploadedFiles(files);
  }, []);

  // Validate file
  const validateFile = useCallback((file: File): string | null => {
    if (file.size > config.maxSize!) {
      return `El archivo es demasiado grande. Máximo ${(config.maxSize! / 1024 / 1024).toFixed(1)}MB`;
    }

    if (config.allowedTypes && !config.allowedTypes.includes(file.type)) {
      return 'Tipo de archivo no permitido. Solo se permiten imágenes y PDFs';
    }

    return null;
  }, [config]);

  // Convert file to base64 for storage
  const fileToBase64 = useCallback((file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => resolve(reader.result as string);
      reader.onerror = reject;
      reader.readAsDataURL(file);
    });
  }, []);

  // Upload files
  const uploadFiles = useCallback(async (files: FileList | File[], transactionId?: string): Promise<UploadedFile[]> => {
    const fileArray = Array.from(files);
    
    if (fileArray.length === 0) {
      throw new Error('No se seleccionaron archivos');
    }

    if (fileArray.length > config.maxFiles!) {
      throw new Error(`Máximo ${config.maxFiles} archivos permitidos`);
    }

    // Validate all files first
    for (const file of fileArray) {
      const error = validateFile(file);
      if (error) {
        throw new Error(error);
      }
    }

    setIsUploading(true);
    setUploadProgress(0);

    try {
      const newFiles: UploadedFile[] = [];
      
      for (let i = 0; i < fileArray.length; i++) {
        const file = fileArray[i];
        
        // Simulate upload progress
        setUploadProgress(((i + 0.5) / fileArray.length) * 100);
        
        // Convert to base64
        const base64 = await fileToBase64(file);
        
        const uploadedFile: UploadedFile = {
          id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
          name: file.name,
          size: file.size,
          type: file.type,
          url: base64,
          uploadedAt: new Date().toISOString(),
          transactionId
        };

        newFiles.push(uploadedFile);
        
        // Update progress
        setUploadProgress(((i + 1) / fileArray.length) * 100);
        
        // Small delay to show progress
        await new Promise(resolve => setTimeout(resolve, 200));
      }

      const updatedFiles = [...uploadedFiles, ...newFiles];
      saveFiles(updatedFiles);
      
      return newFiles;
    } finally {
      setIsUploading(false);
      setUploadProgress(0);
    }
  }, [uploadedFiles, config, validateFile, fileToBase64, saveFiles]);

  // Delete file
  const deleteFile = useCallback((fileId: string) => {
    const updatedFiles = uploadedFiles.filter(f => f.id !== fileId);
    saveFiles(updatedFiles);
  }, [uploadedFiles, saveFiles]);

  // Get files for transaction
  const getFilesForTransaction = useCallback((transactionId: string): UploadedFile[] => {
    return uploadedFiles.filter(f => f.transactionId === transactionId);
  }, [uploadedFiles]);

  // Associate file with transaction
  const associateFileWithTransaction = useCallback((fileId: string, transactionId: string) => {
    const updatedFiles = uploadedFiles.map(f => 
      f.id === fileId ? { ...f, transactionId } : f
    );
    saveFiles(updatedFiles);
  }, [uploadedFiles, saveFiles]);

  // Remove file association
  const removeFileAssociation = useCallback((fileId: string) => {
    const updatedFiles = uploadedFiles.map(f => 
      f.id === fileId ? { ...f, transactionId: undefined } : f
    );
    saveFiles(updatedFiles);
  }, [uploadedFiles, saveFiles]);

  // Get file by ID
  const getFileById = useCallback((fileId: string): UploadedFile | undefined => {
    return uploadedFiles.find(f => f.id === fileId);
  }, [uploadedFiles]);

  // Get all unassociated files
  const getUnassociatedFiles = useCallback((): UploadedFile[] => {
    return uploadedFiles.filter(f => !f.transactionId);
  }, [uploadedFiles]);

  // Format file size
  const formatFileSize = useCallback((bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }, []);

  // Get file icon based on type
  const getFileIcon = useCallback((type: string): string => {
    if (type.startsWith('image/')) {
      return '🖼️';
    } else if (type === 'application/pdf') {
      return '📄';
    } else {
      return '📎';
    }
  }, []);

  // Check if file is image
  const isImage = useCallback((type: string): boolean => {
    return type.startsWith('image/');
  }, []);

  // Get storage usage
  const getStorageUsage = useCallback(() => {
    const totalSize = uploadedFiles.reduce((sum, file) => sum + file.size, 0);
    const fileCount = uploadedFiles.length;
    
    return {
      totalSize,
      fileCount,
      formattedSize: formatFileSize(totalSize)
    };
  }, [uploadedFiles, formatFileSize]);

  // Clear all files
  const clearAllFiles = useCallback(() => {
    localStorage.removeItem('uploadedFiles');
    setUploadedFiles([]);
  }, []);

  // Export files data
  const exportFilesData = useCallback(() => {
    const data = {
      files: uploadedFiles.map(f => ({
        ...f,
        url: undefined // Don't export base64 data
      })),
      exportedAt: new Date().toISOString()
    };
    
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `comprobantes-${new Date().toISOString().split('T')[0]}.json`;
    link.click();
    URL.revokeObjectURL(url);
  }, [uploadedFiles]);

  return {
    uploadedFiles,
    isUploading,
    uploadProgress,
    uploadFiles,
    deleteFile,
    getFilesForTransaction,
    associateFileWithTransaction,
    removeFileAssociation,
    getFileById,
    getUnassociatedFiles,
    formatFileSize,
    getFileIcon,
    isImage,
    getStorageUsage,
    clearAllFiles,
    exportFilesData,
    validateFile
  };
}
