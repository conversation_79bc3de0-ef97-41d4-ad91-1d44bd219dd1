import React, { useState, useRef } from 'react';
import { Upload, X, Eye, Download, Trash2, Paperclip, Image, FileText } from 'lucide-react';
import { useTheme } from '../lib/ThemeContext';
import { useFileUpload, UploadedFile } from '../hooks/useFileUpload';
import { useToast } from './Toast';
import { FadeIn, SlideIn } from './ui/LoadingStates';

interface ReceiptManagerProps {
  transactionId?: string;
  onFilesChange?: (files: UploadedFile[]) => void;
  compact?: boolean;
}

export function ReceiptManager({ transactionId, onFilesChange, compact = false }: ReceiptManagerProps) {
  const { theme } = useTheme();
  const { showSuccess, showError } = useToast();
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  const {
    uploadFiles,
    deleteFile,
    getFilesForTransaction,
    isUploading,
    uploadProgress,
    formatFileSize,
    getFileIcon,
    isImage
  } = useFileUpload();

  const [dragOver, setDragOver] = useState(false);
  const [previewFile, setPreviewFile] = useState<UploadedFile | null>(null);

  const files = transactionId ? getFilesForTransaction(transactionId) : [];

  const handleFileSelect = async (selectedFiles: FileList | null) => {
    if (!selectedFiles || selectedFiles.length === 0) return;

    try {
      const newFiles = await uploadFiles(selectedFiles, transactionId);
      showSuccess('Archivos subidos', `Se subieron ${newFiles.length} archivo(s) correctamente`);
      onFilesChange?.(files.concat(newFiles));
    } catch (error) {
      showError('Error al subir archivos', error instanceof Error ? error.message : 'Error desconocido');
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
    handleFileSelect(e.dataTransfer.files);
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
  };

  const handleDeleteFile = (fileId: string) => {
    if (window.confirm('¿Estás seguro de que quieres eliminar este archivo?')) {
      deleteFile(fileId);
      showSuccess('Archivo eliminado', 'El archivo se eliminó correctamente');
      onFilesChange?.(files.filter(f => f.id !== fileId));
    }
  };

  const handleDownloadFile = (file: UploadedFile) => {
    const link = document.createElement('a');
    link.href = file.url;
    link.download = file.name;
    link.click();
  };

  const handlePreviewFile = (file: UploadedFile) => {
    setPreviewFile(file);
  };

  if (compact) {
    return (
      <div className="space-y-2">
        {/* Compact Upload Area */}
        <div
          className={`border-2 border-dashed rounded-lg p-3 text-center cursor-pointer transition-all duration-200 ${
            dragOver
              ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
              : theme === 'dark'
                ? 'border-gray-600 hover:border-gray-500 bg-[#2a2b38]'
                : 'border-slate-300 hover:border-slate-400 bg-slate-50'
          }`}
          onDrop={handleDrop}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onClick={() => fileInputRef.current?.click()}
        >
          <input
            ref={fileInputRef}
            type="file"
            multiple
            accept="image/*,.pdf"
            onChange={(e) => handleFileSelect(e.target.files)}
            className="hidden"
          />
          
          {isUploading ? (
            <div className="space-y-2">
              <div className="w-6 h-6 mx-auto border-2 border-blue-500 border-t-transparent rounded-full animate-spin" />
              <p className="text-sm text-blue-500">{uploadProgress.toFixed(0)}%</p>
            </div>
          ) : (
            <div className="flex items-center justify-center gap-2">
              <Paperclip className="w-4 h-4" />
              <span className="text-sm">Adjuntar comprobante</span>
            </div>
          )}
        </div>

        {/* Compact File List */}
        {files.length > 0 && (
          <div className="space-y-1">
            {files.map((file, index) => (
              <SlideIn key={file.id} direction="right" delay={index * 100}>
                <div className={`flex items-center gap-2 p-2 rounded-lg transition-colors duration-200 ${
                  theme === 'dark' 
                    ? 'bg-[#2a2b38] border border-gray-600' 
                    : 'bg-white border border-slate-200'
                }`}>
                  <span className="text-lg">{getFileIcon(file.type)}</span>
                  <div className="flex-1 min-w-0">
                    <p className={`text-sm font-medium truncate transition-colors duration-200 ${
                      theme === 'dark' ? 'text-white' : 'text-slate-900'
                    }`}>
                      {file.name}
                    </p>
                    <p className={`text-xs transition-colors duration-200 ${
                      theme === 'dark' ? 'text-gray-400' : 'text-slate-500'
                    }`}>
                      {formatFileSize(file.size)}
                    </p>
                  </div>
                  
                  <div className="flex items-center gap-1">
                    {isImage(file.type) && (
                      <button
                        onClick={() => handlePreviewFile(file)}
                        className={`p-1 rounded transition-colors duration-200 ${
                          theme === 'dark'
                            ? 'text-gray-400 hover:text-blue-400 hover:bg-blue-900/20'
                            : 'text-slate-400 hover:text-blue-600 hover:bg-blue-50'
                        }`}
                        title="Vista previa"
                      >
                        <Eye className="w-3 h-3" />
                      </button>
                    )}
                    
                    <button
                      onClick={() => handleDownloadFile(file)}
                      className={`p-1 rounded transition-colors duration-200 ${
                        theme === 'dark'
                          ? 'text-gray-400 hover:text-green-400 hover:bg-green-900/20'
                          : 'text-slate-400 hover:text-green-600 hover:bg-green-50'
                      }`}
                      title="Descargar"
                    >
                      <Download className="w-3 h-3" />
                    </button>
                    
                    <button
                      onClick={() => handleDeleteFile(file.id)}
                      className={`p-1 rounded transition-colors duration-200 ${
                        theme === 'dark'
                          ? 'text-gray-400 hover:text-red-400 hover:bg-red-900/20'
                          : 'text-slate-400 hover:text-red-600 hover:bg-red-50'
                      }`}
                      title="Eliminar"
                    >
                      <Trash2 className="w-3 h-3" />
                    </button>
                  </div>
                </div>
              </SlideIn>
            ))}
          </div>
        )}

        {/* Preview Modal */}
        {previewFile && (
          <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4">
            <div className="relative max-w-4xl max-h-full">
              <button
                onClick={() => setPreviewFile(null)}
                className="absolute top-4 right-4 p-2 bg-black bg-opacity-50 text-white rounded-full hover:bg-opacity-75 transition-colors duration-200"
              >
                <X className="w-4 h-4" />
              </button>
              
              {isImage(previewFile.type) ? (
                <img
                  src={previewFile.url}
                  alt={previewFile.name}
                  className="max-w-full max-h-full object-contain rounded-lg"
                />
              ) : (
                <div className="bg-white p-8 rounded-lg">
                  <div className="text-center">
                    <FileText className="w-16 h-16 mx-auto mb-4 text-gray-400" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">{previewFile.name}</h3>
                    <p className="text-gray-600 mb-4">Vista previa no disponible para este tipo de archivo</p>
                    <button
                      onClick={() => handleDownloadFile(previewFile)}
                      className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors duration-200"
                    >
                      Descargar archivo
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    );
  }

  // Full version (non-compact)
  return (
    <div className="space-y-4">
      {/* Upload Area */}
      <div
        className={`border-2 border-dashed rounded-xl p-8 text-center cursor-pointer transition-all duration-200 ${
          dragOver
            ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
            : theme === 'dark'
              ? 'border-gray-600 hover:border-gray-500 bg-[#2a2b38]'
              : 'border-slate-300 hover:border-slate-400 bg-slate-50'
        }`}
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onClick={() => fileInputRef.current?.click()}
      >
        <input
          ref={fileInputRef}
          type="file"
          multiple
          accept="image/*,.pdf"
          onChange={(e) => handleFileSelect(e.target.files)}
          className="hidden"
        />
        
        {isUploading ? (
          <div className="space-y-4">
            <div className="w-12 h-12 mx-auto border-4 border-blue-500 border-t-transparent rounded-full animate-spin" />
            <div>
              <p className="text-lg font-medium text-blue-500">Subiendo archivos...</p>
              <p className="text-sm text-blue-400">{uploadProgress.toFixed(0)}% completado</p>
            </div>
          </div>
        ) : (
          <div className="space-y-4">
            <Upload className="w-12 h-12 mx-auto text-gray-400" />
            <div>
              <p className={`text-lg font-medium transition-colors duration-200 ${
                theme === 'dark' ? 'text-white' : 'text-slate-900'
              }`}>
                Arrastra archivos aquí o haz clic para seleccionar
              </p>
              <p className={`text-sm transition-colors duration-200 ${
                theme === 'dark' ? 'text-gray-400' : 'text-slate-500'
              }`}>
                Soporta imágenes (JPG, PNG, GIF, WebP) y PDFs hasta 5MB
              </p>
            </div>
          </div>
        )}
      </div>

      {/* File List */}
      {files.length > 0 && (
        <div className="space-y-3">
          <h4 className={`font-medium transition-colors duration-200 ${
            theme === 'dark' ? 'text-white' : 'text-slate-900'
          }`}>
            Archivos adjuntos ({files.length})
          </h4>
          
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
            {files.map((file, index) => (
              <SlideIn key={file.id} direction="up" delay={index * 100}>
                <div className={`p-4 rounded-lg border transition-colors duration-200 ${
                  theme === 'dark' 
                    ? 'bg-[#2a2b38] border-gray-600' 
                    : 'bg-white border-slate-200'
                }`}>
                  <div className="flex items-start gap-3">
                    <span className="text-2xl">{getFileIcon(file.type)}</span>
                    <div className="flex-1 min-w-0">
                      <p className={`font-medium truncate transition-colors duration-200 ${
                        theme === 'dark' ? 'text-white' : 'text-slate-900'
                      }`}>
                        {file.name}
                      </p>
                      <p className={`text-sm transition-colors duration-200 ${
                        theme === 'dark' ? 'text-gray-400' : 'text-slate-500'
                      }`}>
                        {formatFileSize(file.size)}
                      </p>
                      <p className={`text-xs transition-colors duration-200 ${
                        theme === 'dark' ? 'text-gray-500' : 'text-slate-400'
                      }`}>
                        {new Date(file.uploadedAt).toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-2 mt-3">
                    {isImage(file.type) && (
                      <button
                        onClick={() => handlePreviewFile(file)}
                        className={`flex-1 py-1 px-2 text-xs rounded transition-colors duration-200 ${
                          theme === 'dark'
                            ? 'text-blue-400 hover:bg-blue-900/20'
                            : 'text-blue-600 hover:bg-blue-50'
                        }`}
                      >
                        <Eye className="w-3 h-3 inline mr-1" />
                        Ver
                      </button>
                    )}
                    
                    <button
                      onClick={() => handleDownloadFile(file)}
                      className={`flex-1 py-1 px-2 text-xs rounded transition-colors duration-200 ${
                        theme === 'dark'
                          ? 'text-green-400 hover:bg-green-900/20'
                          : 'text-green-600 hover:bg-green-50'
                      }`}
                    >
                      <Download className="w-3 h-3 inline mr-1" />
                      Descargar
                    </button>
                    
                    <button
                      onClick={() => handleDeleteFile(file.id)}
                      className={`py-1 px-2 text-xs rounded transition-colors duration-200 ${
                        theme === 'dark'
                          ? 'text-red-400 hover:bg-red-900/20'
                          : 'text-red-600 hover:bg-red-50'
                      }`}
                    >
                      <Trash2 className="w-3 h-3" />
                    </button>
                  </div>
                </div>
              </SlideIn>
            ))}
          </div>
        </div>
      )}

      {/* Preview Modal */}
      {previewFile && (
        <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4">
          <div className="relative max-w-4xl max-h-full">
            <button
              onClick={() => setPreviewFile(null)}
              className="absolute top-4 right-4 p-2 bg-black bg-opacity-50 text-white rounded-full hover:bg-opacity-75 transition-colors duration-200 z-10"
            >
              <X className="w-4 h-4" />
            </button>
            
            {isImage(previewFile.type) ? (
              <img
                src={previewFile.url}
                alt={previewFile.name}
                className="max-w-full max-h-full object-contain rounded-lg"
              />
            ) : (
              <div className="bg-white p-8 rounded-lg">
                <div className="text-center">
                  <FileText className="w-16 h-16 mx-auto mb-4 text-gray-400" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">{previewFile.name}</h3>
                  <p className="text-gray-600 mb-4">Vista previa no disponible para este tipo de archivo</p>
                  <button
                    onClick={() => handleDownloadFile(previewFile)}
                    className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors duration-200"
                  >
                    Descargar archivo
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
