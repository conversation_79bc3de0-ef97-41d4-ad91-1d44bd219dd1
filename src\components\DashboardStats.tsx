import React, { useState } from 'react';
import { MESES, Mes } from '../lib/types';
import { ChevronDown } from 'lucide-react';
import { useTransactionContext } from '../lib/TransactionContext';
import { calculateFinancialMetrics, formatNumber, formatDecimal, getCurrentMonth } from '../lib/calculations';
import { TooltipRoot, TooltipTrigger, TooltipContent } from './ui/Tooltip';

interface DashboardStatsProps {
  selectedMonth?: Mes;
  onMonthChange?: (month: Mes) => void;
}

export function DashboardStats({ selectedMonth = getCurrentMonth(), onMonthChange }: DashboardStatsProps) {
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const { transactions } = useTransactionContext();
  const metrics = calculateFinancialMetrics(transactions, selectedMonth);
  
  const handleMonthSelect = (month: Mes) => {
    if (onMonthChange) {
      onM<PERSON>hChange(month);
    }
    setIsDropdownOpen(false);
  };

  return (
    <div className="bg-[#21222d] rounded-2xl p-6 mb-8">
      <div className="grid grid-cols-1 md:grid-cols-5 gap-8">
        {/* Income Stat */}
        <div className="flex items-center gap-4">
          <TooltipRoot delayDuration={0}>
            <TooltipTrigger asChild>
          <div className="relative w-16 h-16">
            <svg className="w-full h-full" viewBox="0 0 36 36">
              <circle cx="18" cy="18" r="16" fill="none" stroke="#2d2d3d" strokeWidth="4" />
              <circle 
                cx="18" cy="18" r="16" 
                fill="none" 
                stroke="#f9769d" 
                strokeWidth="4" 
                strokeDasharray={`${metrics.totalIngresos > 0 ? 75 : 0} 100`} 
                strokeDashoffset={metrics.totalIngresos > 0 ? "25" : "100"}
                style={{ transform: 'rotate(-90deg)', transformOrigin: 'center' }}
              />
            </svg>
            <span className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-xs text-gray-400">
              {metrics.totalIngresos > 0 ? '75%' : '0%'}
            </span>
          </div>
            </TooltipTrigger>
            <TooltipContent>
              <p>Total de dinero que has recibido este mes (salario, freelance, etc.)</p>
            </TooltipContent>
          </TooltipRoot>
          <div>
            <p className="text-gray-400 mb-1">Total Income</p>
            <h3 className="text-white text-2xl font-bold">${formatNumber(metrics.totalIngresos)}</h3>
          </div>
        </div>
        
        {/* Expense Stat */}
        <div className="flex items-center gap-4">
          <TooltipRoot delayDuration={0}>
            <TooltipTrigger asChild>
          <div className="relative w-16 h-16">
            <svg className="w-full h-full" viewBox="0 0 36 36">
              <circle cx="18" cy="18" r="16" fill="none" stroke="#2d2d3d" strokeWidth="4" />
              <circle 
                cx="18" cy="18" r="16" 
                fill="none" 
                stroke="#96a7ff" 
                strokeWidth="4" 
                strokeDasharray={`${metrics.totalGastos > 0 ? 60 : 0} 100`} 
                strokeDashoffset={metrics.totalGastos > 0 ? "40" : "100"}
                style={{ transform: 'rotate(-90deg)', transformOrigin: 'center' }}
              />
            </svg>
            <span className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-xs text-gray-400">
              {metrics.totalGastos > 0 ? '60%' : '0%'}
            </span>
          </div>
            </TooltipTrigger>
            <TooltipContent>
              <p>Total de dinero que has gastado este mes en todas las categorías</p>
            </TooltipContent>
          </TooltipRoot>
          <div>
            <p className="text-gray-400 mb-1">Total Expense</p>
            <h3 className="text-white text-2xl font-bold">${formatNumber(metrics.totalGastos)}</h3>
          </div>
        </div>
        
        {/* Bonus Stat */}
        <div className="flex items-center gap-4">
          <TooltipRoot delayDuration={0}>
            <TooltipTrigger asChild>
          <div className="relative w-16 h-16">
            <svg className="w-full h-full" viewBox="0 0 36 36">
              <circle cx="18" cy="18" r="16" fill="none" stroke="#2d2d3d" strokeWidth="4" />
              <circle 
                cx="18" cy="18" r="16" 
                fill="none" 
                stroke="#b66dff" 
                strokeWidth="4" 
                strokeDasharray={`${Math.abs(metrics.remanente) > 0 ? 68 : 0} 100`} 
                strokeDashoffset={Math.abs(metrics.remanente) > 0 ? "32" : "100"}
                style={{ transform: 'rotate(-90deg)', transformOrigin: 'center' }}
              />
            </svg>
            <span className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-xs text-gray-400">
              {Math.abs(metrics.remanente) > 0 ? '68%' : '0%'}
            </span>
          </div>
            </TooltipTrigger>
            <TooltipContent>
              <p>Lo que te queda después de gastos. Positivo = puedes ahorrar. Negativo = gastas más de lo que ingresas.</p>
            </TooltipContent>
          </TooltipRoot>
          <div>
            <p className="text-gray-400 mb-1">Remanente</p>
            <h3 className={`text-2xl font-bold ${metrics.remanente >= 0 ? 'text-white' : 'text-red-400'}`}>
              ${formatNumber(metrics.remanente)}
            </h3>
          </div>
        </div>
        
        {/* Tasa Ahorro Stat */}
        <div className="flex items-center gap-4">
          <TooltipRoot delayDuration={0}>
            <TooltipTrigger asChild>
          <div className="relative w-16 h-16">
            <svg className="w-full h-full" viewBox="0 0 36 36">
              <circle cx="18" cy="18" r="16" fill="none" stroke="#2d2d3d" strokeWidth="4" />
              <circle 
                cx="18" cy="18" r="16" 
                fill="none" 
                stroke="#0aefff" 
                strokeWidth="4" 
                strokeDasharray={`${Math.max(0, metrics.tasaAhorroInversion)} 100`} 
                strokeDashoffset={`${100 - Math.max(0, metrics.tasaAhorroInversion)}`}
                style={{ transform: 'rotate(-90deg)', transformOrigin: 'center' }}
              />
            </svg>
            <span className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-xs text-gray-400">
              {Math.max(0, metrics.tasaAhorroInversion).toFixed(0)}%
            </span>
          </div>
            </TooltipTrigger>
            <TooltipContent className="p-3 bg-gray-800 text-white rounded-lg shadow-lg">
              <div>
                <p className="text-sm">Porcentaje de tus ingresos que estás ahorrando. Meta recomendada: 20% o más.</p>
              </div>
            </TooltipContent>
          </TooltipRoot>
          <div>
            <p className="text-gray-400 mb-1">Tasa Ahorro</p>
            <h3 className={`text-2xl font-bold ${metrics.tasaAhorroInversion >= 0 ? 'text-white' : 'text-red-400'}`}>
              {formatDecimal(metrics.tasaAhorroInversion, 1)}%
            </h3>
          </div>
        </div>
        
        {/* Month Selector */}
        <div className="flex items-center justify-end">
          <div className="relative">
            <button 
              onClick={() => setIsDropdownOpen(!isDropdownOpen)}
              className="bg-[#2a2b38] hover:bg-[#32334a] text-white font-medium rounded-lg px-4 py-2.5 text-center inline-flex items-center"
            >
              {selectedMonth}
              <ChevronDown className="w-4 h-4 ml-2" />
            </button>
            
            {isDropdownOpen && (
              <div className="absolute right-0 mt-2 w-48 bg-[#2a2b38] rounded-lg shadow-xl z-10 p-2 animate-fadeIn">
                <div className="max-h-60 overflow-y-auto">
                  {MESES.map(mes => (
                    <button
                      key={mes}
                      onClick={() => handleMonthSelect(mes)}
                      className={`w-full text-left px-4 py-2 rounded-md font-medium ${
                        selectedMonth === mes 
                          ? 'bg-[#f9769d] text-white' 
                          : 'text-gray-200 hover:bg-[#32334a]'
                      }`}
                    >
                      {mes}
                    </button>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}