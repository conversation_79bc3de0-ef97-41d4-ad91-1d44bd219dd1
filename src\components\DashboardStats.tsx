import React, { useState } from 'react';
import { MESES, Mes } from '../lib/types';
import { ChevronDown } from 'lucide-react';
import { useTransactionContext } from '../lib/TransactionContext';
import { calculateFinancialMetrics, formatNumber, formatDecimal, getCurrentMonth } from '../lib/calculations';
import { InfoTooltip } from './ui/Tooltip';
import { useTheme } from '../lib/ThemeContext';

interface DashboardStatsProps {
  selectedMonth?: Mes;
  onMonthChange?: (month: Mes) => void;
}

export function DashboardStats({ selectedMonth = getCurrentMonth(), onMonthChange }: DashboardStatsProps) {
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const { transactions } = useTransactionContext();
  const metrics = calculateFinancialMetrics(transactions, selectedMonth);
  const { theme } = useTheme();
  
  const handleMonthSelect = (month: Mes) => {
    if (onMonthChange) {
      onMonthChange(month);
    }
    setIsDropdownOpen(false);
  };

  return (
    <div className={`rounded-2xl p-6 mb-8 transition-colors duration-200 ${
      theme === 'dark'
        ? 'bg-[#21222d]'
        : 'bg-slate-50 border border-slate-200 shadow-sm'
    }`}>
      <div className="grid grid-cols-1 md:grid-cols-5 gap-8">
        {/* Income Stat */}
        <div className="flex items-center gap-4">
          <InfoTooltip
            title="Total Income"
            description="Total de dinero que has recibido este mes (salario, freelance, etc.)"
          >
            <div className="relative w-16 h-16">
              <svg className="w-full h-full" viewBox="0 0 36 36">
                <circle cx="18" cy="18" r="16" fill="none" stroke={theme === 'dark' ? '#2d2d3d' : '#e2e8f0'} strokeWidth="4" />
                <circle
                  cx="18" cy="18" r="16"
                  fill="none"
                  stroke="#f9769d"
                  strokeWidth="4"
                  strokeDasharray={`${metrics.totalIngresos > 0 ? 75 : 0} 100`}
                  strokeDashoffset={metrics.totalIngresos > 0 ? "25" : "100"}
                  style={{ transform: 'rotate(-90deg)', transformOrigin: 'center' }}
                />
              </svg>
              <span className={`absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-xs transition-colors duration-200 ${
                theme === 'dark' ? 'text-gray-400' : 'text-slate-600'
              }`}>
                {metrics.totalIngresos > 0 ? '75%' : '0%'}
              </span>
            </div>
          </InfoTooltip>
          <div>
            <p className={`mb-1 transition-colors duration-200 ${
              theme === 'dark' ? 'text-gray-400' : 'text-slate-500'
            }`}>Total Income</p>
            <h3 className={`text-2xl font-bold transition-colors duration-200 ${
              theme === 'dark' ? 'text-white' : 'text-slate-900'
            }`}>${formatNumber(metrics.totalIngresos)}</h3>
          </div>
        </div>
        
        {/* Expense Stat */}
        <div className="flex items-center gap-4">
          <InfoTooltip
            title="Total Expense"
            description="Total de dinero que has gastado este mes en todas las categorías"
          >
            <div className="relative w-16 h-16">
              <svg className="w-full h-full" viewBox="0 0 36 36">
                <circle cx="18" cy="18" r="16" fill="none" stroke={theme === 'dark' ? '#2d2d3d' : '#e2e8f0'} strokeWidth="4" />
                <circle
                  cx="18" cy="18" r="16"
                  fill="none"
                  stroke="#96a7ff"
                  strokeWidth="4"
                  strokeDasharray={`${metrics.totalGastos > 0 ? 60 : 0} 100`}
                  strokeDashoffset={metrics.totalGastos > 0 ? "40" : "100"}
                  style={{ transform: 'rotate(-90deg)', transformOrigin: 'center' }}
                />
              </svg>
              <span className={`absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-xs transition-colors duration-200 ${
                theme === 'dark' ? 'text-gray-400' : 'text-slate-600'
              }`}>
                {metrics.totalGastos > 0 ? '60%' : '0%'}
              </span>
            </div>
          </InfoTooltip>
          <div>
            <p className={`mb-1 transition-colors duration-200 ${
              theme === 'dark' ? 'text-gray-400' : 'text-slate-500'
            }`}>Total Expense</p>
            <h3 className={`text-2xl font-bold transition-colors duration-200 ${
              theme === 'dark' ? 'text-white' : 'text-slate-900'
            }`}>${formatNumber(metrics.totalGastos)}</h3>
          </div>
        </div>
        
        {/* Bonus Stat */}
        <div className="flex items-center gap-4">
          <InfoTooltip
            title="Remanente"
            description="Lo que te queda después de gastos. Positivo = puedes ahorrar. Negativo = gastas más de lo que ingresas."
          >
            <div className="relative w-16 h-16">
              <svg className="w-full h-full" viewBox="0 0 36 36">
                <circle cx="18" cy="18" r="16" fill="none" stroke={theme === 'dark' ? '#2d2d3d' : '#e2e8f0'} strokeWidth="4" />
                <circle
                  cx="18" cy="18" r="16"
                  fill="none"
                  stroke="#b66dff"
                  strokeWidth="4"
                  strokeDasharray={`${Math.abs(metrics.remanente) > 0 ? 68 : 0} 100`}
                  strokeDashoffset={Math.abs(metrics.remanente) > 0 ? "32" : "100"}
                  style={{ transform: 'rotate(-90deg)', transformOrigin: 'center' }}
                />
              </svg>
              <span className={`absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-xs transition-colors duration-200 ${
                theme === 'dark' ? 'text-gray-400' : 'text-slate-600'
              }`}>
                {Math.abs(metrics.remanente) > 0 ? '68%' : '0%'}
              </span>
            </div>
          </InfoTooltip>
          <div>
            <p className={`mb-1 transition-colors duration-200 ${
              theme === 'dark' ? 'text-gray-400' : 'text-slate-500'
            }`}>Remanente</p>
            <h3 className={`text-2xl font-bold transition-colors duration-200 ${
              metrics.remanente >= 0
                ? (theme === 'dark' ? 'text-white' : 'text-slate-900')
                : 'text-red-500'
            }`}>
              ${formatNumber(metrics.remanente)}
            </h3>
          </div>
        </div>
        
        {/* Tasa Ahorro Stat */}
        <div className="flex items-center gap-4">
          <InfoTooltip
            title="Tasa de Ahorro"
            description="Porcentaje de tus ingresos que estás ahorrando. Meta recomendada: 20% o más."
          >
            <div className="relative w-16 h-16">
              <svg className="w-full h-full" viewBox="0 0 36 36">
                <circle cx="18" cy="18" r="16" fill="none" stroke={theme === 'dark' ? '#2d2d3d' : '#e2e8f0'} strokeWidth="4" />
                <circle
                  cx="18" cy="18" r="16"
                  fill="none"
                  stroke="#0aefff"
                  strokeWidth="4"
                  strokeDasharray={`${Math.max(0, metrics.tasaAhorroInversion)} 100`}
                  strokeDashoffset={`${100 - Math.max(0, metrics.tasaAhorroInversion)}`}
                  style={{ transform: 'rotate(-90deg)', transformOrigin: 'center' }}
                />
              </svg>
              <span className={`absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-xs transition-colors duration-200 ${
                theme === 'dark' ? 'text-gray-400' : 'text-slate-600'
              }`}>
                {Math.max(0, metrics.tasaAhorroInversion).toFixed(0)}%
              </span>
            </div>
          </InfoTooltip>
          <div>
            <p className={`mb-1 transition-colors duration-200 ${
              theme === 'dark' ? 'text-gray-400' : 'text-slate-500'
            }`}>Tasa Ahorro</p>
            <h3 className={`text-2xl font-bold transition-colors duration-200 ${
              metrics.tasaAhorroInversion >= 0
                ? (theme === 'dark' ? 'text-white' : 'text-slate-900')
                : 'text-red-500'
            }`}>
              {formatDecimal(metrics.tasaAhorroInversion, 1)}%
            </h3>
          </div>
        </div>
        
        {/* Month Selector */}
        <div className="flex items-center justify-end">
          <div className="relative">
            <button
              onClick={() => setIsDropdownOpen(!isDropdownOpen)}
              className={`font-medium rounded-lg px-4 py-2.5 text-center inline-flex items-center transition-colors duration-200 ${
                theme === 'dark'
                  ? 'bg-[#2a2b38] hover:bg-[#32334a] text-white'
                  : 'bg-slate-100 hover:bg-slate-200 text-slate-900 border border-slate-200'
              }`}
            >
              {selectedMonth}
              <ChevronDown className="w-4 h-4 ml-2" />
            </button>
            
            {isDropdownOpen && (
              <div className={`absolute right-0 mt-2 w-48 rounded-lg shadow-xl z-10 p-2 animate-fadeIn transition-colors duration-200 ${
                theme === 'dark'
                  ? 'bg-[#2a2b38]'
                  : 'bg-white border border-slate-200'
              }`}>
                <div className="max-h-60 overflow-y-auto">
                  {MESES.map(mes => (
                    <button
                      key={mes}
                      onClick={() => handleMonthSelect(mes)}
                      className={`w-full text-left px-4 py-2 rounded-md font-medium transition-colors duration-200 ${
                        selectedMonth === mes
                          ? 'bg-[#f9769d] text-white'
                          : theme === 'dark'
                            ? 'text-gray-200 hover:bg-[#32334a]'
                            : 'text-slate-700 hover:bg-slate-100'
                      }`}
                    >
                      {mes}
                    </button>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}