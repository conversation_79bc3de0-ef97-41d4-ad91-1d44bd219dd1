/*
  # Add user_id field to transactions table

  1. Changes
    - Add user_id column to transactions table
    - Add foreign key constraint to auth.users
    - Update RLS policies to filter by user_id
    - Make user_id required for new transactions

  2. Security
    - Users can only access their own transactions
    - RLS policies updated to use user_id
*/

-- Add user_id column to transactions table
ALTER TABLE transactions 
ADD COLUMN user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE;

-- Create index for better performance
CREATE INDEX transactions_user_id_idx ON transactions(user_id);

-- Drop existing policies
DROP POLICY IF EXISTS "Allow all operations for now" ON transactions;

-- Enable RLS (should already be enabled, but just in case)
ALTER TABLE transactions ENABLE ROW LEVEL SECURITY;

-- Create new RLS policies for authenticated users
CREATE POLICY "Users can view own transactions"
  ON transactions
  FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own transactions"
  ON transactions
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own transactions"
  ON transactions
  FOR UPDATE
  TO authenticated
  USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can delete own transactions"
  ON transactions
  FOR DELETE
  TO authenticated
  USING (auth.uid() = user_id);