import React, { useState } from 'react';
import { useOfflineSync } from '../hooks/useOfflineSync';
import { useTheme } from '../lib/ThemeContext';
import { 
  Wifi, 
  WifiOff, 
  RefreshCw, 
  Clock, 
  AlertCircle, 
  CheckCircle,
  X,
  ChevronDown,
  ChevronUp
} from 'lucide-react';

export function OfflineIndicator() {
  const { theme } = useTheme();
  const {
    isOnline,
    syncInProgress,
    pendingActions,
    lastSyncTime,
    manualSync,
    getOfflineStatus,
    clearPendingActions
  } = useOfflineSync();
  
  const [isExpanded, setIsExpanded] = useState(false);
  const [showDetails, setShowDetails] = useState(false);
  
  const status = getOfflineStatus();
  
  // Don't show if online and no pending actions
  if (isOnline && status.unsyncedCount === 0) {
    return null;
  }

  const formatLastSync = (date: Date | null) => {
    if (!date) return 'Nunca';
    
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);
    
    if (minutes < 1) return 'Hace un momento';
    if (minutes < 60) return `Hace ${minutes} min`;
    if (hours < 24) return `Hace ${hours}h`;
    return `Hace ${days}d`;
  };

  const getStatusColor = () => {
    if (!isOnline) return 'bg-red-500';
    if (status.unsyncedCount > 0) return 'bg-yellow-500';
    return 'bg-green-500';
  };

  const getStatusText = () => {
    if (!isOnline) return 'Sin conexión';
    if (syncInProgress) return 'Sincronizando...';
    if (status.unsyncedCount > 0) return `${status.unsyncedCount} cambios pendientes`;
    return 'Sincronizado';
  };

  const getStatusIcon = () => {
    if (!isOnline) return <WifiOff className="w-4 h-4" />;
    if (syncInProgress) return <RefreshCw className="w-4 h-4 animate-spin" />;
    if (status.unsyncedCount > 0) return <Clock className="w-4 h-4" />;
    return <CheckCircle className="w-4 h-4" />;
  };

  return (
    <div className={`fixed bottom-4 right-4 z-50 ${
      theme === 'dark' ? 'text-white' : 'text-gray-900'
    }`}>
      {/* Main indicator */}
      <div 
        className={`flex items-center gap-2 px-3 py-2 rounded-lg shadow-lg cursor-pointer transition-all duration-200 ${
          theme === 'dark' 
            ? 'bg-gray-800 border border-gray-700' 
            : 'bg-white border border-gray-200'
        } ${isExpanded ? 'rounded-b-none' : ''}`}
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <div className={`w-2 h-2 rounded-full ${getStatusColor()}`} />
        {getStatusIcon()}
        <span className="text-sm font-medium">{getStatusText()}</span>
        {isExpanded ? <ChevronUp className="w-4 h-4" /> : <ChevronDown className="w-4 h-4" />}
      </div>

      {/* Expanded details */}
      {isExpanded && (
        <div className={`mt-0 p-4 rounded-lg rounded-t-none shadow-lg border-t-0 ${
          theme === 'dark' 
            ? 'bg-gray-800 border border-gray-700' 
            : 'bg-white border border-gray-200'
        }`}>
          <div className="space-y-3">
            {/* Connection status */}
            <div className="flex items-center justify-between">
              <span className="text-sm">Estado de conexión:</span>
              <div className="flex items-center gap-2">
                {isOnline ? <Wifi className="w-4 h-4 text-green-500" /> : <WifiOff className="w-4 h-4 text-red-500" />}
                <span className={`text-sm ${isOnline ? 'text-green-500' : 'text-red-500'}`}>
                  {isOnline ? 'En línea' : 'Sin conexión'}
                </span>
              </div>
            </div>

            {/* Last sync */}
            <div className="flex items-center justify-between">
              <span className="text-sm">Última sincronización:</span>
              <span className="text-sm text-gray-500">
                {formatLastSync(status.lastSyncDate)}
              </span>
            </div>

            {/* Pending actions */}
            {status.unsyncedCount > 0 && (
              <div className="flex items-center justify-between">
                <span className="text-sm">Cambios pendientes:</span>
                <span className="text-sm font-medium text-yellow-500">
                  {status.unsyncedCount}
                </span>
              </div>
            )}

            {/* Actions */}
            <div className="flex gap-2 pt-2 border-t border-gray-200 dark:border-gray-700">
              {status.canSync && (
                <button
                  onClick={manualSync}
                  disabled={syncInProgress}
                  className="flex items-center gap-1 px-3 py-1 text-xs bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"
                >
                  <RefreshCw className={`w-3 h-3 ${syncInProgress ? 'animate-spin' : ''}`} />
                  Sincronizar
                </button>
              )}
              
              <button
                onClick={() => setShowDetails(!showDetails)}
                className="flex items-center gap-1 px-3 py-1 text-xs bg-gray-500 text-white rounded hover:bg-gray-600"
              >
                Ver detalles
              </button>
              
              {status.unsyncedCount > 0 && (
                <button
                  onClick={() => {
                    if (confirm('¿Estás seguro de que quieres eliminar todos los cambios pendientes?')) {
                      clearPendingActions();
                    }
                  }}
                  className="flex items-center gap-1 px-3 py-1 text-xs bg-red-500 text-white rounded hover:bg-red-600"
                >
                  <X className="w-3 h-3" />
                  Limpiar
                </button>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Detailed view modal */}
      {showDetails && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className={`w-full max-w-md mx-4 rounded-lg shadow-xl ${
            theme === 'dark' ? 'bg-gray-800' : 'bg-white'
          }`}>
            <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
              <h3 className="text-lg font-semibold">Estado de Sincronización</h3>
              <button
                onClick={() => setShowDetails(false)}
                className="p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded"
              >
                <X className="w-5 h-5" />
              </button>
            </div>
            
            <div className="p-4 space-y-4 max-h-96 overflow-y-auto">
              {/* Connection info */}
              <div className="space-y-2">
                <h4 className="font-medium">Conexión</h4>
                <div className="flex items-center gap-2 text-sm">
                  {isOnline ? <Wifi className="w-4 h-4 text-green-500" /> : <WifiOff className="w-4 h-4 text-red-500" />}
                  {isOnline ? 'Conectado a internet' : 'Sin conexión a internet'}
                </div>
              </div>

              {/* Sync info */}
              <div className="space-y-2">
                <h4 className="font-medium">Sincronización</h4>
                <div className="text-sm space-y-1">
                  <div>Última sincronización: {formatLastSync(status.lastSyncDate)}</div>
                  <div>Estado: {syncInProgress ? 'En progreso...' : 'Inactiva'}</div>
                  <div>Cambios pendientes: {status.unsyncedCount}</div>
                </div>
              </div>

              {/* Pending actions */}
              {pendingActions.length > 0 && (
                <div className="space-y-2">
                  <h4 className="font-medium">Acciones Pendientes</h4>
                  <div className="space-y-2 max-h-40 overflow-y-auto">
                    {pendingActions.map((action) => (
                      <div 
                        key={action.id}
                        className={`p-2 rounded text-xs ${
                          theme === 'dark' ? 'bg-gray-700' : 'bg-gray-100'
                        }`}
                      >
                        <div className="flex items-center justify-between">
                          <span className="font-medium">
                            {action.type} {action.entity}
                          </span>
                          {action.synced ? (
                            <CheckCircle className="w-3 h-3 text-green-500" />
                          ) : (
                            <Clock className="w-3 h-3 text-yellow-500" />
                          )}
                        </div>
                        <div className="text-gray-500 mt-1">
                          {new Date(action.timestamp).toLocaleString()}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Tips */}
              <div className="space-y-2">
                <h4 className="font-medium">Consejos</h4>
                <div className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                  <div>• Los cambios se guardan localmente cuando estás offline</div>
                  <div>• Se sincronizan automáticamente al reconectarte</div>
                  <div>• Puedes forzar la sincronización manualmente</div>
                  <div>• Los datos se mantienen seguros en tu dispositivo</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
