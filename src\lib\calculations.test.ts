import { describe, it, expect, beforeEach } from 'vitest';
import { 
  calculateFinancialMetrics, 
  formatCurrency, 
  formatNumber, 
  formatDecimal,
  getProgressColor,
  getTasaAhorroMessage
} from './calculations';
import { Transaction } from './types';

describe('Financial Calculations', () => {
  let mockTransactions: Transaction[];

  beforeEach(() => {
    // Mock transactions for current month
    const currentDate = new Date();
    const currentMonth = currentDate.getMonth();
    const currentYear = currentDate.getFullYear();

    mockTransactions = [
      {
        id: '1',
        description: 'Salario',
        date: `${currentYear}-${String(currentMonth + 1).padStart(2, '0')}-01`,
        type: 'income',
        amount: 5000,
        category: 'salario',
        icon: '💼'
      },
      {
        id: '2',
        description: 'Freelance',
        date: `${currentYear}-${String(currentMonth + 1).padStart(2, '0')}-05`,
        type: 'income',
        amount: 1000,
        category: 'freelance',
        icon: '💻'
      },
      {
        id: '3',
        description: 'Supermercado',
        date: `${currentYear}-${String(currentMonth + 1).padStart(2, '0')}-03`,
        type: 'expense',
        amount: 800,
        category: 'alimentacion',
        icon: '🍎'
      },
      {
        id: '4',
        description: 'Alquiler',
        date: `${currentYear}-${String(currentMonth + 1).padStart(2, '0')}-01`,
        type: 'expense',
        amount: 1200,
        category: 'vivienda',
        icon: '🏠'
      },
      {
        id: '5',
        description: 'Netflix',
        date: `${currentYear}-${String(currentMonth + 1).padStart(2, '0')}-10`,
        type: 'expense',
        amount: 15,
        category: 'suscripciones',
        icon: '📺'
      }
    ];
  });

  describe('calculateFinancialMetrics', () => {
    it('should calculate total income correctly', () => {
      const metrics = calculateFinancialMetrics(mockTransactions);
      expect(metrics.totalIngresos).toBe(6000);
    });

    it('should calculate total expenses correctly', () => {
      const metrics = calculateFinancialMetrics(mockTransactions);
      expect(metrics.totalGastos).toBe(2015);
    });

    it('should calculate remainder correctly', () => {
      const metrics = calculateFinancialMetrics(mockTransactions);
      expect(metrics.remanente).toBe(3985);
    });

    it('should calculate savings rate correctly', () => {
      const metrics = calculateFinancialMetrics(mockTransactions);
      expect(metrics.tasaAhorroInversion).toBeCloseTo(66.4, 1);
    });

    it('should handle empty transactions array', () => {
      const metrics = calculateFinancialMetrics([]);
      expect(metrics.totalIngresos).toBe(0);
      expect(metrics.totalGastos).toBe(0);
      expect(metrics.remanente).toBe(0);
      expect(metrics.tasaAhorroInversion).toBe(0);
    });

    it('should group expenses by category correctly', () => {
      const metrics = calculateFinancialMetrics(mockTransactions);
      expect(metrics.gastosPorCategoria).toHaveLength(3);
      
      const alimentacion = metrics.gastosPorCategoria.find(cat => 
        cat.categoria.toLowerCase() === 'alimentacion'
      );
      expect(alimentacion?.total).toBe(800);
    });

    it('should generate monthly trends data', () => {
      const metrics = calculateFinancialMetrics(mockTransactions);
      expect(metrics.transaccionesPorMes).toHaveLength(6);
      expect(metrics.transaccionesPorMes[5].mes).toBeDefined();
    });
  });

  describe('Formatting functions', () => {
    it('should format currency correctly', () => {
      expect(formatCurrency(1000)).toBe('$1.000');
      expect(formatCurrency(1234.56)).toBe('$1.235');
      expect(formatCurrency(0)).toBe('$0');
    });

    it('should format numbers correctly', () => {
      expect(formatNumber(1000)).toBe('1.000');
      expect(formatNumber(1234.99)).toBe('1.235');
      expect(formatNumber(0)).toBe('0');
    });

    it('should format decimals correctly', () => {
      expect(formatDecimal(66.666, 1)).toBe('66,7');
      expect(formatDecimal(66.666, 2)).toBe('66,67');
      expect(formatDecimal(0, 1)).toBe('0,0');
    });
  });

  describe('getProgressColor', () => {
    it('should return correct colors for different percentages', () => {
      expect(getProgressColor(20)).toBe('bg-red-500');
      expect(getProgressColor(50)).toBe('bg-yellow-500');
      expect(getProgressColor(80)).toBe('bg-green-500');
    });
  });

  describe('getTasaAhorroMessage', () => {
    it('should return appropriate messages for different savings rates', () => {
      const excellentMessage = getTasaAhorroMessage(35);
      expect(excellentMessage).toContain('¡Excelente trabajo Carlos!');

      const goodMessage = getTasaAhorroMessage(25);
      expect(goodMessage).toContain('¡Buen trabajo Carlos!');

      const okMessage = getTasaAhorroMessage(15);
      expect(okMessage).toContain('Vas por buen camino Carlos');

      const lowMessage = getTasaAhorroMessage(5);
      expect(lowMessage).toContain('Estás ahorrando algo');

      const negativeMessage = getTasaAhorroMessage(-5);
      expect(negativeMessage).toContain('gastos superaron tus ingresos');
    });
  });

  describe('Edge cases and error handling', () => {
    it('should handle invalid dates gracefully', () => {
      const invalidTransaction: Transaction = {
        id: '1',
        description: 'Test',
        date: 'invalid-date',
        type: 'income',
        amount: 1000,
        category: 'test',
        icon: '💰'
      };

      // Should not throw error
      expect(() => calculateFinancialMetrics([invalidTransaction])).not.toThrow();
    });

    it('should handle negative amounts correctly', () => {
      const negativeTransaction: Transaction = {
        id: '1',
        description: 'Refund',
        date: new Date().toISOString().split('T')[0],
        type: 'income',
        amount: -100,
        category: 'refund',
        icon: '↩️'
      };

      const metrics = calculateFinancialMetrics([negativeTransaction]);
      expect(metrics.totalIngresos).toBe(0); // Negative income should be handled
    });

    it('should handle very large numbers', () => {
      const largeTransaction: Transaction = {
        id: '1',
        description: 'Lottery Win',
        date: new Date().toISOString().split('T')[0],
        type: 'income',
        amount: 999999999,
        category: 'lottery',
        icon: '🎰'
      };

      const metrics = calculateFinancialMetrics([largeTransaction]);
      expect(metrics.totalIngresos).toBe(999999999);
      expect(formatCurrency(metrics.totalIngresos)).toBeDefined();
    });
  });
});