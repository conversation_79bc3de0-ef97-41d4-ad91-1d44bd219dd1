import { useState, useEffect } from 'react';
import { PlusIcon, X, Check, BadgeEuro, CircleDollarSign, Settings } from 'lucide-react';
import { useTheme } from '../lib/ThemeContext';
import { useTransactionContext } from '../lib/TransactionContext';
import { validateTransaction, validateAmount, validateDate } from '../lib/validation';
import { useToast } from './Toast';
import { LoadingSpinner } from './LoadingSpinner';
import { useCustomCategories } from '../hooks/useCustomCategories';
import { CategoryManager } from './CategoryManager';

const categoryIcons = {
  // Ingresos
  salario: '💼',
  freelance: '💻',
  intereses: '💹',
  dividendos: '📊',
  
  // Gastos
  vivienda: '🏠',
  alimentacion: '🍎',
  transporte: '🚗',
  entretenimiento: '🎬',
  servicios: '📱',
  salud: '🩺',
  educacion: '📚',
  compras: '🛍️',
  suscripciones: '📺',
  
  // Deudas
  tarjeta: '💳',
  prestamo: '🏦',
  
  // Ahorros
  emergencia: '🧰',
  metasCP: '🎯',
  
  // Inversiones
  acciones: '📈',
  cripto: '₿',
  fondos: '💹'
};

interface AddTransactionButtonProps {
  isNewMonth?: boolean;
  onClose?: () => void;
  isMobileModal?: boolean;
}

export function AddTransactionButton({ isNewMonth = false, onClose, isMobileModal = false }: AddTransactionButtonProps) {
  const { theme } = useTheme();
  const { addTransaction } = useTransactionContext();
  const { showError } = useToast();
  const { getCategoriesForType, getCategoryIcon, refreshCategories } = useCustomCategories();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [transactionType, setTransactionType] = useState<'ingreso' | 'gasto'>('ingreso');
  const [showFeedback, setShowFeedback] = useState(false);
  const [amount, setAmount] = useState('');
  const [description, setDescription] = useState('');
  const [category, setCategory] = useState('');
  const [date, setDate] = useState(new Date().toISOString().split('T')[0]); // Today's date
  const [time, setTime] = useState(new Date().toTimeString().split(' ')[0].substring(0, 5)); // Current time (HH:MM)
  const [isDesktop, setIsDesktop] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});
  const [showCategoryManager, setShowCategoryManager] = useState(false);

  // Check if screen is desktop size
  useEffect(() => {
    const checkScreenSize = () => {
      setIsDesktop(window.innerWidth >= 1024);
    };
    
    checkScreenSize();
    window.addEventListener('resize', checkScreenSize);
    
    return () => window.removeEventListener('resize', checkScreenSize);
  }, []);

  // Close feedback after 3 seconds
  useEffect(() => {
    if (showFeedback) {
      const timer = setTimeout(() => {
        setShowFeedback(false);
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [showFeedback]);

  // Validation function
  const validateForm = (): boolean => {
    const errors: Record<string, string> = {};
    
    if (!description.trim()) {
      errors.description = 'La descripción es requerida';
    }
    
    if (!date) {
      errors.date = 'La fecha es requerida';
    } else if (!validateDate(date)) {
      errors.date = 'La fecha debe ser válida y no estar en el futuro';
    }
    
    if (!category) {
      errors.category = 'La categoría es requerida';
    }
    
    const validAmount = validateAmount(amount);
    if (!amount || validAmount === null || validAmount <= 0) {
      errors.amount = 'El monto debe ser un número válido mayor a 0';
    }
    
    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // Clear validation errors when fields change
  const clearFieldError = (field: string) => {
    if (validationErrors[field]) {
      setValidationErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    
    if (isSubmitting || !validateForm()) return;

    const submitTransaction = async () => {
      setIsSubmitting(true);
      
      try {
        // Combine date and time into a single datetime string
        const datetime = `${date}T${time}:00.000Z`;
        
        const validAmount = validateAmount(amount);
        if (validAmount === null) {
          showError('Error de validación', 'El monto ingresado no es válido');
          return;
        }
        
        const transactionData = {
          description: description || `${transactionType === 'ingreso' ? 'Ingreso' : 'Gasto'} de ${category}`,
          date: date, // Keep date as YYYY-MM-DD for the database
          type: transactionType === 'ingreso' ? 'income' : 'expense',
          amount: validAmount,
          category,
          icon: getCategoryIcon(category) || (transactionType === 'ingreso' ? '💰' : '💸')
        };
        
        // Final validation before sending
        const validation = validateTransaction(transactionData);
        if (!validation.success) {
          showError('Error de validación', 'Los datos ingresados no son válidos');
          return;
        }
        
        const result = await addTransaction(transactionData);
        
        if (result.success) {
          // Reset form
          setTransactionType('ingreso');
          setAmount('');
          setDescription('');
          setCategory('');
          setDate(new Date().toISOString().split('T')[0]);
          setTime(new Date().toTimeString().split(' ')[0].substring(0, 5));
          setValidationErrors({});
          setIsModalOpen(false);
          setShowFeedback(true);

          // Call onClose if provided (for mobile modal)
          if (onClose) {
            onClose();
          }
        } else {
          // Handle error
          console.error('Error adding transaction:', result.error);
          alert('Error al agregar la transacción: ' + result.error);
        }
      } catch (error) {
        showError('Error inesperado', 'No se pudo procesar la transacción');
      } finally {
        setIsSubmitting(false);
      }
    };
    
    submitTransaction();
  };

  return (
    <>
      {!isMobileModal && (
        <button
          onClick={() => setIsModalOpen(true)}
          className="fixed bottom-6 right-6 w-auto min-w-14 h-14 bg-[#3B82F6] rounded-full flex items-center justify-center shadow-lg hover:bg-blue-600 transition-colors z-50 hover:shadow-xl transform hover:scale-105 transition-all duration-300 pr-4 pl-4 group"
          aria-label="Agregar movimiento"
        >
          <PlusIcon className="w-6 h-6 text-white mr-0 group-hover:mr-2 transition-all duration-300" />
          <span className="text-white font-medium max-w-0 overflow-hidden group-hover:max-w-xs transition-all duration-300 ease-in-out">
            {isNewMonth ? "¡Agregar tu primer gasto!" : "Agregar movimiento"}
          </span>

          {/* Tooltip de ayuda */}
          <div className="absolute -top-12 right-0 bg-black text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap z-50">
            Haz clic para registrar ingresos o gastos
          </div>
        </button>
      )}

      {/* Feedback toast */}
      {showFeedback && (
        <div className="fixed bottom-24 right-6 bg-green-50 text-green-800 px-4 py-3 rounded-lg shadow-md flex items-center space-x-2 z-50 animate-fadeIn dark:bg-green-900 dark:text-green-100 max-w-xs">
          <Check className="w-5 h-5 text-green-600 dark:text-green-400" />
          <span>Movimiento agregado correctamente</span>
        </div>
      )}

      {(isModalOpen || isMobileModal) && (
        <div className={`fixed inset-0 bg-black bg-opacity-70 flex ${isDesktop ? 'items-center justify-end' : 'items-center justify-center'} z-[100] p-4 animate-fadeIn backdrop-blur-sm`}>
          <div className={`bg-[#21222d] border border-[#2a2a3d] ${isDesktop ? 'h-full w-[420px] overflow-y-auto' : 'rounded-[20px] w-full max-w-md max-h-[90vh] overflow-y-auto'} shadow-2xl p-6 relative ${isDesktop ? 'animate-slideInRight' : 'animate-scaleIn'}`}>
            <button
              className="absolute top-4 right-4 text-gray-400 hover:text-white transition-colors z-10 w-8 h-8 flex items-center justify-center rounded-full hover:bg-gray-700"
              onClick={() => {
                setIsModalOpen(false);
                if (onClose) onClose();
              }}
            >
              <X className="w-5 h-5" />
            </button>
            
            <h2 className="text-xl font-bold mb-4 flex items-center">
              {transactionType === 'ingreso' 
                ? <CircleDollarSign className="w-6 h-6 text-green-400 mr-2" /> 
                : <BadgeEuro className="w-6 h-6 text-red-400 mr-2" />}
              <span className="text-white">Agregar Movimiento</span>
            </h2>
            
            <form className="space-y-4" onSubmit={handleSubmit}>
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-1">
                  Tipo
                </label>
                <div className="grid grid-cols-2 gap-3">
                  <button
                    type="button"
                    className={`flex justify-center items-center space-x-1 rounded-lg p-3 font-medium transition-all duration-300 ${
                      transactionType === 'ingreso' 
                        ? 'bg-gradient-to-r from-emerald-500 to-emerald-600 text-white shadow-md' 
                        : 'bg-green-500 bg-opacity-20 border border-green-500 text-green-400 hover:bg-opacity-30'
                    }`}
                    onClick={() => setTransactionType('ingreso')}
                  >
                    <span>💰</span>
                    <span>Ingreso</span>
                  </button>
                  <button
                    type="button"
                    className={`flex justify-center items-center space-x-1 rounded-lg p-3 font-medium transition-all duration-300 ${
                      transactionType === 'gasto' 
                        ? 'bg-gradient-to-r from-red-500 to-red-600 text-white shadow-md' 
                        : 'bg-red-500 bg-opacity-20 border border-red-500 text-red-400 hover:bg-opacity-30'
                    }`}
                    onClick={() => setTransactionType('gasto')}
                  >
                    <span>💸</span>
                    <span>Gasto</span>
                  </button>
                </div>
              </div>
              
              <div>
                <label htmlFor="categoria" className="block text-sm font-medium text-gray-300 mb-1">
                  Categoría
                </label>
                <div className="relative">
                  <div className="flex gap-2">
                    <select
                      id="categoria"
                      className={`flex-1 border rounded-lg p-2 pl-10 focus:ring-blue-500 focus:border-blue-500 hover:border-gray-500 transition-colors duration-300 shadow-sm focus:shadow-md bg-[#2a2b38] text-gray-200 border-gray-600 ${validationErrors.category ? 'border-red-500' : ''}`}
                      value={category}
                      onChange={(e) => setCategory(e.target.value)}
                      required
                      onFocus={() => clearFieldError('category')}
                    >
                      <option value="">Seleccionar categoría</option>
                      {getCategoriesForType(transactionType === 'ingreso' ? 'income' : 'expense').map(cat => (
                        <option key={cat.key} value={cat.key}>
                          {cat.icon} {cat.name}
                        </option>
                      ))}
                    </select>
                    <button
                      type="button"
                      onClick={() => setShowCategoryManager(true)}
                      className="px-3 py-2 bg-gray-700 hover:bg-gray-600 text-gray-300 rounded-lg transition-colors duration-200"
                      title="Gestionar categorías"
                    >
                      <Settings className="w-4 h-4" />
                    </button>
                  </div>
                  <div className="absolute left-3 top-1/2 transform -translate-y-1/2 pointer-events-none">
                    {category ? getCategoryIcon(category) : '📋'}
                  </div>
                  {validationErrors.category && (
                    <p className="text-red-400 text-xs mt-1">{validationErrors.category}</p>
                  )}
                </div>
              </div>
              
              <div>
                <label htmlFor="fecha" className="block text-sm font-medium text-gray-300 mb-1">
                  Fecha
                </label>
                <input
                  type="date"
                  id="fecha"
                  className={`w-full border rounded-lg p-2 focus:ring-blue-500 focus:border-blue-500 hover:border-gray-500 transition-colors duration-300 shadow-sm focus:shadow-md bg-[#2a2b38] text-gray-200 border-gray-600 ${validationErrors.date ? 'border-red-500' : ''}`}
                  value={date}
                  onChange={(e) => setDate(e.target.value)}
                  onFocus={() => clearFieldError('date')}
                  required
                />
                {validationErrors.date && (
                  <p className="text-red-400 text-xs mt-1">{validationErrors.date}</p>
                )}
              </div>
              
              <div>
                <label htmlFor="hora" className="block text-sm font-medium text-gray-300 mb-1">
                  Hora (opcional)
                </label>
                <input
                  type="time"
                  id="hora"
                  className="w-full border border-gray-600 rounded-lg p-2 focus:ring-blue-500 focus:border-blue-500 hover:border-gray-500 transition-colors duration-300 shadow-sm focus:shadow-md bg-[#2a2b38] text-gray-200"
                  value={time}
                  onChange={(e) => setTime(e.target.value)}
                />
              </div>
              
              <div>
                <label htmlFor="monto" className="block text-sm font-medium text-gray-300 mb-1">
                  Monto
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <span className="text-gray-400">$</span>
                  </div>
                  <input
                    type="number"
                    id="monto"
                    min="0"
                    step="0.01"
                    className={`w-full border rounded-lg p-2 pl-8 focus:ring-blue-500 focus:border-blue-500 hover:border-gray-500 transition-colors duration-300 shadow-sm focus:shadow-md bg-[#2a2b38] text-gray-200 border-gray-600 ${validationErrors.amount ? 'border-red-500' : ''}`}
                    placeholder="Ej. 2500.00"
                    value={amount}
                    onChange={(e) => {
                      setAmount(e.target.value);
                      clearFieldError('amount');
                    }}
                    required
                  />
                  {validationErrors.amount && (
                    <p className="text-red-400 text-xs mt-1">{validationErrors.amount}</p>
                  )}
                </div>
              </div>
              
              <div>
                <label htmlFor="descripcion" className="block text-sm font-medium text-gray-300 mb-1">
                  Descripción (opcional)
                </label>
                <textarea
                  id="descripcion"
                  rows={3}
                  className={`w-full border rounded-lg p-2 focus:ring-blue-500 focus:border-blue-500 hover:border-gray-500 transition-colors duration-300 shadow-sm focus:shadow-md bg-[#2a2b38] text-gray-200 border-gray-600 ${validationErrors.description ? 'border-red-500' : ''}`}
                  placeholder="Ej. Sueldo de marzo"
                  value={description}
                  onChange={(e) => {
                    setDescription(e.target.value);
                    clearFieldError('description');
                  }}
                />
                {validationErrors.description && (
                  <p className="text-red-400 text-xs mt-1">{validationErrors.description}</p>
                )}
              </div>
              
              <div className="pt-4">
                <button
                  type="submit"
                  disabled={isSubmitting}
                  className={`w-full text-white rounded-lg p-3 font-medium transition-all duration-300 hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-offset-2 ${
                    isSubmitting
                      ? 'bg-gray-500 cursor-not-allowed'
                      : transactionType === 'ingreso' 
                      ? 'bg-gradient-to-r from-green-600 to-green-500 hover:from-green-700 hover:to-green-600 focus:ring-green-500' 
                      : 'bg-gradient-to-r from-red-600 to-red-500 hover:from-red-700 hover:to-red-600 focus:ring-red-500'
                  }`}
                >
                  {isSubmitting ? (
                    <div className="flex items-center justify-center">
                      <LoadingSpinner size="sm" />
                      <span className="ml-2">
                        Guardando...
                      </span>
                    </div>
                  ) : (
                    'Guardar'
                  )}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Category Manager Modal */}
      <CategoryManager
        isOpen={showCategoryManager}
        onClose={() => setShowCategoryManager(false)}
        onCategoryChange={refreshCategories}
      />
    </>
  );
}