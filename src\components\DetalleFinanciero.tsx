import { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from './ui/Tabs';
import type { Mes } from '../lib/types';
import { useTheme } from '../lib/ThemeContext';
import { useTransactionContext } from '../lib/TransactionContext';
import { calculateFinancialMetrics } from '../lib/calculations';

interface DetalleFinancieroProps {
  mes: Mes;
}

// Category icons
const categoryIcons = {
  ingresos: '💰',
  esenciales: '🏠',
  discrecionales: '🎯',
  deudas: '🧾',
  ahorros: '🧠',
  inversiones: '📈'
};

// Subcategory icons
const subcategoryIcons = {
  // Ingresos
  'Salario': '💼',
  'Freelance': '💻',
  'Otros': '🪙',
  
  // Esenciales
  'Vivienda': '🏠',
  'Alimentación': '🍎',
  'Servicios': '🔌',
  'Transporte': '🚗',
  
  // Discrecionales
  'Restaurantes': '🍽️',
  'Entretenimiento': '🎬',
  'Compras': '🛍️',
  'Suscripciones': '📺',
  
  // Deudas
  'Tarjeta de crédito': '💳',
  'Préstamo': '🏦',
  
  // Ahorros
  'Fondo de emergencia': '🧰',
  'Metas corto plazo': '🎯',
  
  // Inversiones
  'Cuenta inversión': '📊',
  'Cripto': '₿'
};


export function DetalleFinanciero({ mes }: DetalleFinancieroProps) {
  const { theme } = useTheme();
  const { transactions } = useTransactionContext();
  const [activeTab, setActiveTab] = useState('ingresos');
  
  // Calculate metrics from real transaction data
  const metrics = calculateFinancialMetrics(transactions, mes);
  
  // Create tabData from real transactions
  const createTabData = () => {
    // Map categories to Spanish labels
    const categoryMapping = {
      // Ingresos
      salario: 'Salario',
      freelance: 'Freelance',
      intereses: 'Intereses',
      dividendos: 'Dividendos',
      
      // Esenciales (gastos básicos)
      vivienda: 'Vivienda',
      alimentacion: 'Alimentación',
      servicios: 'Servicios',
      transporte: 'Transporte',
      salud: 'Salud',
      
      // Discrecionales
      entretenimiento: 'Entretenimiento',
      compras: 'Compras',
      suscripciones: 'Suscripciones',
      
      // Deudas
      tarjeta: 'Tarjeta de crédito',
      prestamo: 'Préstamo',
      
      // Ahorros
      emergencia: 'Fondo de emergencia',
      metas: 'Metas corto plazo',
      
      // Inversiones
      acciones: 'Cuenta inversión',
      cripto: 'Cripto'
    };
    
    // Group income categories
    const ingresosCategorias = ['salario', 'freelance', 'intereses', 'dividendos'];
    const esencialesCategorias = ['vivienda', 'alimentacion', 'servicios', 'transporte', 'salud'];
    const discrecionalesCategorias = ['entretenimiento', 'compras', 'suscripciones'];
    const deudasCategorias = ['tarjeta', 'prestamo'];
    const ahorrosCategorias = ['emergencia', 'metas'];
    const inversionesCategorias = ['acciones', 'cripto'];
    
    // Helper function to create data for each tab
    const createTabSection = (categorias: string[], isIncome: boolean = false) => {
      const relevantCategories = isIncome 
        ? metrics.ingresosPorCategoria 
        : metrics.gastosPorCategoria;
        
      return categorias.map(categoria => {
        const categoryData = relevantCategories.find(cat => 
          cat.categoria.toLowerCase() === categoria.toLowerCase()
        );
        
        const real = categoryData ? categoryData.total : 0;
        // For demo purposes, set estimated as 90% of real value, or add some variation
        const estimado = real > 0 ? Math.round(real * 0.9) : 0;
        
        return {
          subcategoria: categoryMapping[categoria] || categoria,
          estimado,
          real: Math.round(real)
        };
      }).filter(item => item.real > 0 || item.estimado > 0); // Only show categories with data
    };
    
    return {
      ingresos: createTabSection(ingresosCategorias, true),
      esenciales: createTabSection(esencialesCategorias),
      discrecionales: createTabSection(discrecionalesCategorias),
      deudas: createTabSection(deudasCategorias),
      ahorros: createTabSection(ahorrosCategorias),
      inversiones: createTabSection(inversionesCategorias)
    };
  };
  
  const tabData = createTabData();

  // Calculate totals
  const getTotal = (items) => {
    return items.reduce(
      (acc, item) => {
        return {
          estimado: acc.estimado + item.estimado,
          real: acc.real + item.real
        };
      },
      { estimado: 0, real: 0 }
    );
  };

  const total = getTotal(tabData[activeTab]);
  const diferencia = total.real - total.estimado;

  return (
    <div className="p-6 pt-2">
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <div className="overflow-x-auto">
          <TabsList className="grid grid-cols-3 lg:grid-cols-6 gap-2 min-w-max bg-[#2a2b38] text-gray-200">
            {Object.keys(tabData).map(key => (
              <TabsTrigger key={key} value={key} className="flex items-center gap-1 font-medium data-[state=active]:bg-[#383a4f] data-[state=active]:text-white">
                <span className="text-base">{categoryIcons[key]}</span>
                <span>{key.charAt(0).toUpperCase() + key.slice(1)}</span>
              </TabsTrigger>
            ))}
          </TabsList>
        </div>

        <div className="mt-4 overflow-x-auto">
          <table className="w-full min-w-[600px]">
            <thead>
              <tr className="border-b border-gray-700">
                <th className="text-left py-2 font-bold">Subcategoría</th>
                <th className="text-right py-2 font-bold">Estimado</th>
                <th className="text-right py-2 font-bold">Real</th>
                <th className="text-right py-2 font-bold">Diferencia</th>
              </tr>
            </thead>
            <tbody>
              {tabData[activeTab] && tabData[activeTab].length > 0 ? tabData[activeTab].map((item, index) => {
                const itemDiff = item.real - item.estimado;
                const diffClass = 
                  activeTab === 'ingresos' 
                    ? (itemDiff >= 0 ? 'text-[#10B981]' : 'text-red-400')
                    : (itemDiff <= 0 ? 'text-[#10B981]' : 'text-red-400');
                
                return (
                  <tr key={index} className="border-b border-gray-700 hover:bg-[#2a2b38] transition-colors duration-150">
                    <td className="py-3 flex items-center space-x-2">
                      <span className="text-lg">{subcategoryIcons[item.subcategoria] || '📋'}</span>
                      <span className="font-medium">{item.subcategoria}</span>
                    </td>
                    <td className="text-right">
                      <span className="inline-flex items-center justify-center bg-[#2e3259] text-blue-300 py-1 px-2 rounded shadow-sm font-medium">
                        ${item.estimado}
                      </span>
                    </td>
                    <td className="text-right">
                      <span className="inline-flex items-center justify-center bg-[#35294f] text-purple-300 py-1 px-2 rounded shadow-sm font-medium">
                        ${item.real}
                      </span>
                    </td>
                    <td className={`text-right font-medium`}>
                      <span className={`inline-flex items-center justify-center ${
                        itemDiff >= 0 
                          ? 'bg-[#1d3a32] text-green-400' 
                          : 'bg-[#3d2b33] text-red-400'
                      } py-1 px-2 rounded shadow-sm font-medium`}>
                        {itemDiff > 0 ? '+' : ''}{itemDiff}
                      </span>
                    </td>
                  </tr>
                );
              }) : (
                <tr>
                  <td colSpan={4} className="py-8 text-center text-gray-500">
                    <div className="flex flex-col items-center">
                      <span className="text-4xl mb-2">📊</span>
                      <p className="font-medium">No hay transacciones en esta categoría</p>
                      <p className="text-sm mt-1">Agrega transacciones para ver el detalle</p>
                    </div>
                  </td>
                </tr>
              )}
            </tbody>
            <tfoot>
              <tr className="font-bold bg-[#2a2b38]">
                <td className="py-3">Total</td>
                <td className="text-right">${total.estimado}</td>
                <td className="text-right">${total.real}</td>
                <td className={`text-right ${
                  activeTab === 'ingresos' 
                    ? (diferencia >= 0 ? 'text-[#10B981]' : 'text-red-600')
                    : (diferencia <= 0 ? 'text-[#10B981]' : 'text-red-600')
                } font-bold`}>
                  {diferencia > 0 ? '+' : ''}{diferencia}
                </td>
              </tr>
            </tfoot>
          </table>
        </div>
      </Tabs>
    </div>
  );
}