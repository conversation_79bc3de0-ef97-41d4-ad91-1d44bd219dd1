import React, { useState, useMemo } from 'react';
import { Plus, Edit, Trash2, Target, Calendar, DollarSign, TrendingUp, CheckCircle, AlertCircle } from 'lucide-react';
import { useTheme } from '../lib/ThemeContext';
import { useToast } from './Toast';
import { ProgressBar, FadeIn, SlideIn } from './ui/LoadingStates';
import { InfoTooltip, HelpTooltip } from './ui/Tooltip';
import { format, differenceInDays, parseISO } from 'date-fns';
import { es } from 'date-fns/locale';

interface SavingsGoal {
  id: string;
  name: string;
  description?: string;
  targetAmount: number;
  currentAmount: number;
  targetDate: string;
  category: 'emergency' | 'vacation' | 'purchase' | 'investment' | 'other';
  priority: 'low' | 'medium' | 'high';
  createdAt: string;
  isCompleted: boolean;
}

const goalCategories = {
  emergency: { name: 'Fondo de Emergencia', icon: '🛡️', color: 'bg-red-500' },
  vacation: { name: 'Vacaciones', icon: '🏖️', color: 'bg-blue-500' },
  purchase: { name: '<PERSON><PERSON><PERSON>', icon: '🛍️', color: 'bg-green-500' },
  investment: { name: 'Inversión', icon: '📈', color: 'bg-purple-500' },
  other: { name: 'Otro', icon: '🎯', color: 'bg-gray-500' }
};

export function SavingsGoalsPage() {
  const { theme } = useTheme();
  const { showSuccess, showError } = useToast();
  
  const [goals, setGoals] = useState<SavingsGoal[]>(() => {
    const saved = localStorage.getItem('savingsGoals');
    return saved ? JSON.parse(saved) : [];
  });
  
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingGoal, setEditingGoal] = useState<SavingsGoal | null>(null);
  const [showAddProgress, setShowAddProgress] = useState<string | null>(null);
  
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    targetAmount: '',
    targetDate: '',
    category: 'other' as keyof typeof goalCategories,
    priority: 'medium' as 'low' | 'medium' | 'high'
  });

  const [progressAmount, setProgressAmount] = useState('');

  // Save goals to localStorage
  const saveGoals = (newGoals: SavingsGoal[]) => {
    localStorage.setItem('savingsGoals', JSON.stringify(newGoals));
    setGoals(newGoals);
  };

  // Calculate goal statistics
  const goalStats = useMemo(() => {
    const totalGoals = goals.length;
    const completedGoals = goals.filter(g => g.isCompleted).length;
    const totalTarget = goals.reduce((sum, g) => sum + g.targetAmount, 0);
    const totalSaved = goals.reduce((sum, g) => sum + g.currentAmount, 0);
    const overallProgress = totalTarget > 0 ? (totalSaved / totalTarget) * 100 : 0;
    
    return {
      totalGoals,
      completedGoals,
      activeGoals: totalGoals - completedGoals,
      totalTarget,
      totalSaved,
      overallProgress
    };
  }, [goals]);

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.name || !formData.targetAmount || !formData.targetDate) {
      showError('Error', 'Todos los campos requeridos deben completarse');
      return;
    }

    const targetAmount = parseFloat(formData.targetAmount);
    if (targetAmount <= 0) {
      showError('Error', 'El monto objetivo debe ser mayor a 0');
      return;
    }

    if (editingGoal) {
      // Update existing goal
      const updatedGoals = goals.map(g =>
        g.id === editingGoal.id
          ? {
              ...g,
              name: formData.name,
              description: formData.description,
              targetAmount,
              targetDate: formData.targetDate,
              category: formData.category,
              priority: formData.priority
            }
          : g
      );
      saveGoals(updatedGoals);
      showSuccess('Meta actualizada', 'La meta de ahorro se actualizó correctamente');
      setEditingGoal(null);
    } else {
      // Add new goal
      const newGoal: SavingsGoal = {
        id: Date.now().toString(),
        name: formData.name,
        description: formData.description,
        targetAmount,
        currentAmount: 0,
        targetDate: formData.targetDate,
        category: formData.category,
        priority: formData.priority,
        createdAt: new Date().toISOString(),
        isCompleted: false
      };
      
      saveGoals([...goals, newGoal]);
      showSuccess('Meta creada', 'La nueva meta de ahorro se creó correctamente');
    }

    // Reset form
    setFormData({
      name: '',
      description: '',
      targetAmount: '',
      targetDate: '',
      category: 'other',
      priority: 'medium'
    });
    setShowAddForm(false);
  };

  const handleEdit = (goal: SavingsGoal) => {
    setEditingGoal(goal);
    setFormData({
      name: goal.name,
      description: goal.description || '',
      targetAmount: goal.targetAmount.toString(),
      targetDate: goal.targetDate,
      category: goal.category,
      priority: goal.priority
    });
    setShowAddForm(true);
  };

  const handleDelete = (goalId: string) => {
    if (window.confirm('¿Estás seguro de que quieres eliminar esta meta?')) {
      const updatedGoals = goals.filter(g => g.id !== goalId);
      saveGoals(updatedGoals);
      showSuccess('Meta eliminada', 'La meta de ahorro se eliminó correctamente');
    }
  };

  const handleAddProgress = (goalId: string) => {
    const amount = parseFloat(progressAmount);
    if (amount <= 0) {
      showError('Error', 'El monto debe ser mayor a 0');
      return;
    }

    const updatedGoals = goals.map(g => {
      if (g.id === goalId) {
        const newAmount = g.currentAmount + amount;
        const isCompleted = newAmount >= g.targetAmount;
        
        if (isCompleted && !g.isCompleted) {
          showSuccess('¡Meta completada!', `¡Felicitaciones! Has alcanzado tu meta "${g.name}"`);
        }
        
        return {
          ...g,
          currentAmount: newAmount,
          isCompleted
        };
      }
      return g;
    });

    saveGoals(updatedGoals);
    setProgressAmount('');
    setShowAddProgress(null);
    showSuccess('Progreso agregado', 'Se agregó el progreso a tu meta de ahorro');
  };

  const handleCancel = () => {
    setShowAddForm(false);
    setEditingGoal(null);
    setFormData({
      name: '',
      description: '',
      targetAmount: '',
      targetDate: '',
      category: 'other',
      priority: 'medium'
    });
  };

  const getDaysRemaining = (targetDate: string) => {
    const days = differenceInDays(parseISO(targetDate), new Date());
    return days;
  };

  const getGoalStatus = (goal: SavingsGoal) => {
    if (goal.isCompleted) return 'completed';
    
    const daysRemaining = getDaysRemaining(goal.targetDate);
    const progress = (goal.currentAmount / goal.targetAmount) * 100;
    
    if (daysRemaining < 0) return 'overdue';
    if (daysRemaining <= 30 && progress < 80) return 'urgent';
    if (progress >= 80) return 'on-track';
    return 'normal';
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'text-green-500';
      case 'overdue': return 'text-red-500';
      case 'urgent': return 'text-orange-500';
      case 'on-track': return 'text-blue-500';
      default: return theme === 'dark' ? 'text-gray-400' : 'text-gray-600';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed': return <CheckCircle className="w-4 h-4" />;
      case 'overdue': return <AlertCircle className="w-4 h-4" />;
      case 'urgent': return <AlertCircle className="w-4 h-4" />;
      default: return <Target className="w-4 h-4" />;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div className="flex items-center gap-3">
          <div>
            <h1 className={`text-3xl font-bold transition-colors duration-200 ${
              theme === 'dark' ? 'text-white' : 'text-slate-900'
            }`}>
              Metas de Ahorro
            </h1>
            <p className={`text-sm mt-1 transition-colors duration-200 ${
              theme === 'dark' ? 'text-gray-400' : 'text-slate-500'
            }`}>
              Establece y trackea tus objetivos financieros
            </p>
          </div>
          <HelpTooltip content="Las metas de ahorro te ayudan a planificar y alcanzar tus objetivos financieros. Puedes establecer diferentes tipos de metas y hacer seguimiento de tu progreso." />
        </div>
        
        <button
          onClick={() => setShowAddForm(true)}
          className="px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg transition-colors duration-200 flex items-center gap-2"
        >
          <Plus className="w-4 h-4" />
          Nueva Meta
        </button>
      </div>

      {/* Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <InfoTooltip title="Metas Totales" description="Número total de metas de ahorro creadas">
          <div className={`rounded-xl p-4 transition-colors duration-200 ${
            theme === 'dark' 
              ? 'bg-[#21222d] border border-[#2a2a3d]' 
              : 'bg-slate-50 border border-slate-200'
          }`}>
            <div className="flex items-center">
              <div className="p-2 bg-blue-500 rounded-lg">
                <Target className="w-5 h-5 text-white" />
              </div>
              <div className="ml-3">
                <p className={`text-sm transition-colors duration-200 ${
                  theme === 'dark' ? 'text-gray-400' : 'text-slate-500'
                }`}>
                  Metas Totales
                </p>
                <p className="text-lg font-bold text-blue-500">
                  {goalStats.totalGoals}
                </p>
              </div>
            </div>
          </div>
        </InfoTooltip>

        <div className={`rounded-xl p-4 transition-colors duration-200 ${
          theme === 'dark' 
            ? 'bg-[#21222d] border border-[#2a2a3d]' 
            : 'bg-slate-50 border border-slate-200'
        }`}>
          <div className="flex items-center">
            <div className="p-2 bg-green-500 rounded-lg">
              <CheckCircle className="w-5 h-5 text-white" />
            </div>
            <div className="ml-3">
              <p className={`text-sm transition-colors duration-200 ${
                theme === 'dark' ? 'text-gray-400' : 'text-slate-500'
              }`}>
                Completadas
              </p>
              <p className="text-lg font-bold text-green-500">
                {goalStats.completedGoals}
              </p>
            </div>
          </div>
        </div>

        <div className={`rounded-xl p-4 transition-colors duration-200 ${
          theme === 'dark' 
            ? 'bg-[#21222d] border border-[#2a2a3d]' 
            : 'bg-slate-50 border border-slate-200'
        }`}>
          <div className="flex items-center">
            <div className="p-2 bg-purple-500 rounded-lg">
              <DollarSign className="w-5 h-5 text-white" />
            </div>
            <div className="ml-3">
              <p className={`text-sm transition-colors duration-200 ${
                theme === 'dark' ? 'text-gray-400' : 'text-slate-500'
              }`}>
                Total Ahorrado
              </p>
              <p className="text-lg font-bold text-purple-500">
                ${goalStats.totalSaved.toLocaleString()}
              </p>
            </div>
          </div>
        </div>

        <div className={`rounded-xl p-4 transition-colors duration-200 ${
          theme === 'dark' 
            ? 'bg-[#21222d] border border-[#2a2a3d]' 
            : 'bg-slate-50 border border-slate-200'
        }`}>
          <div className="flex items-center">
            <div className="p-2 bg-orange-500 rounded-lg">
              <TrendingUp className="w-5 h-5 text-white" />
            </div>
            <div className="ml-3">
              <p className={`text-sm transition-colors duration-200 ${
                theme === 'dark' ? 'text-gray-400' : 'text-slate-500'
              }`}>
                Progreso General
              </p>
              <p className="text-lg font-bold text-orange-500">
                {goalStats.overallProgress.toFixed(1)}%
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Add/Edit Goal Form */}
      {showAddForm && (
        <SlideIn direction="down">
          <div className={`rounded-xl p-6 transition-colors duration-200 ${
            theme === 'dark'
              ? 'bg-[#21222d] border border-[#2a2a3d]'
              : 'bg-slate-50 border border-slate-200'
          }`}>
            <h3 className={`text-lg font-semibold mb-4 transition-colors duration-200 ${
              theme === 'dark' ? 'text-white' : 'text-slate-900'
            }`}>
              {editingGoal ? 'Editar Meta' : 'Nueva Meta de Ahorro'}
            </h3>

            <form onSubmit={handleSubmit} className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Name */}
              <div className="md:col-span-2">
                <label className={`block text-sm font-medium mb-2 transition-colors duration-200 ${
                  theme === 'dark' ? 'text-gray-300' : 'text-slate-700'
                }`}>
                  Nombre de la Meta *
                </label>
                <input
                  type="text"
                  value={formData.name}
                  onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                  className={`w-full px-3 py-2 rounded-lg border transition-colors duration-200 ${
                    theme === 'dark'
                      ? 'bg-[#2a2b38] border-gray-600 text-white placeholder-gray-400'
                      : 'bg-white border-slate-300 text-slate-900 placeholder-slate-400'
                  }`}
                  placeholder="Ej: Vacaciones en Europa"
                  required
                />
              </div>

              {/* Description */}
              <div className="md:col-span-2">
                <label className={`block text-sm font-medium mb-2 transition-colors duration-200 ${
                  theme === 'dark' ? 'text-gray-300' : 'text-slate-700'
                }`}>
                  Descripción
                </label>
                <textarea
                  value={formData.description}
                  onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                  className={`w-full px-3 py-2 rounded-lg border transition-colors duration-200 ${
                    theme === 'dark'
                      ? 'bg-[#2a2b38] border-gray-600 text-white placeholder-gray-400'
                      : 'bg-white border-slate-300 text-slate-900 placeholder-slate-400'
                  }`}
                  placeholder="Describe tu meta de ahorro..."
                  rows={3}
                />
              </div>

              {/* Target Amount */}
              <div>
                <label className={`block text-sm font-medium mb-2 transition-colors duration-200 ${
                  theme === 'dark' ? 'text-gray-300' : 'text-slate-700'
                }`}>
                  Monto Objetivo ($) *
                </label>
                <input
                  type="number"
                  step="0.01"
                  value={formData.targetAmount}
                  onChange={(e) => setFormData(prev => ({ ...prev, targetAmount: e.target.value }))}
                  className={`w-full px-3 py-2 rounded-lg border transition-colors duration-200 ${
                    theme === 'dark'
                      ? 'bg-[#2a2b38] border-gray-600 text-white placeholder-gray-400'
                      : 'bg-white border-slate-300 text-slate-900 placeholder-slate-400'
                  }`}
                  placeholder="0.00"
                  required
                />
              </div>

              {/* Target Date */}
              <div>
                <label className={`block text-sm font-medium mb-2 transition-colors duration-200 ${
                  theme === 'dark' ? 'text-gray-300' : 'text-slate-700'
                }`}>
                  Fecha Objetivo *
                </label>
                <input
                  type="date"
                  value={formData.targetDate}
                  onChange={(e) => setFormData(prev => ({ ...prev, targetDate: e.target.value }))}
                  className={`w-full px-3 py-2 rounded-lg border transition-colors duration-200 ${
                    theme === 'dark'
                      ? 'bg-[#2a2b38] border-gray-600 text-white'
                      : 'bg-white border-slate-300 text-slate-900'
                  }`}
                  required
                />
              </div>

              {/* Category */}
              <div>
                <label className={`block text-sm font-medium mb-2 transition-colors duration-200 ${
                  theme === 'dark' ? 'text-gray-300' : 'text-slate-700'
                }`}>
                  Categoría
                </label>
                <select
                  value={formData.category}
                  onChange={(e) => setFormData(prev => ({ ...prev, category: e.target.value as keyof typeof goalCategories }))}
                  className={`w-full px-3 py-2 rounded-lg border transition-colors duration-200 ${
                    theme === 'dark'
                      ? 'bg-[#2a2b38] border-gray-600 text-white'
                      : 'bg-white border-slate-300 text-slate-900'
                  }`}
                >
                  {Object.entries(goalCategories).map(([key, category]) => (
                    <option key={key} value={key}>
                      {category.icon} {category.name}
                    </option>
                  ))}
                </select>
              </div>

              {/* Priority */}
              <div>
                <label className={`block text-sm font-medium mb-2 transition-colors duration-200 ${
                  theme === 'dark' ? 'text-gray-300' : 'text-slate-700'
                }`}>
                  Prioridad
                </label>
                <select
                  value={formData.priority}
                  onChange={(e) => setFormData(prev => ({ ...prev, priority: e.target.value as 'low' | 'medium' | 'high' }))}
                  className={`w-full px-3 py-2 rounded-lg border transition-colors duration-200 ${
                    theme === 'dark'
                      ? 'bg-[#2a2b38] border-gray-600 text-white'
                      : 'bg-white border-slate-300 text-slate-900'
                  }`}
                >
                  <option value="low">🟢 Baja</option>
                  <option value="medium">🟡 Media</option>
                  <option value="high">🔴 Alta</option>
                </select>
              </div>

              {/* Form Actions */}
              <div className="md:col-span-2 flex gap-3 pt-4">
                <button
                  type="button"
                  onClick={handleCancel}
                  className={`flex-1 py-2 px-4 rounded-lg border transition-colors duration-200 ${
                    theme === 'dark'
                      ? 'border-gray-600 text-gray-300 hover:bg-gray-700'
                      : 'border-slate-300 text-slate-700 hover:bg-slate-50'
                  }`}
                >
                  Cancelar
                </button>
                <button
                  type="submit"
                  className="flex-1 py-2 px-4 rounded-lg bg-blue-500 hover:bg-blue-600 text-white transition-colors duration-200"
                >
                  {editingGoal ? 'Actualizar' : 'Crear'} Meta
                </button>
              </div>
            </form>
          </div>
        </SlideIn>
      )}

      {/* Goals List */}
      <div className={`rounded-xl transition-colors duration-200 ${
        theme === 'dark'
          ? 'bg-[#21222d] border border-[#2a2a3d]'
          : 'bg-slate-50 border border-slate-200'
      }`}>
        <div className="p-4 border-b border-gray-600">
          <h2 className={`text-lg font-semibold transition-colors duration-200 ${
            theme === 'dark' ? 'text-white' : 'text-slate-900'
          }`}>
            Mis Metas ({goals.length})
          </h2>
        </div>

        {goals.length === 0 ? (
          <FadeIn>
            <div className={`text-center py-12 transition-colors duration-200 ${
              theme === 'dark' ? 'text-gray-500' : 'text-slate-500'
            }`}>
              <Target className="w-12 h-12 mx-auto mb-4 opacity-50" />
              <p className="font-medium">No tienes metas de ahorro</p>
              <p className="text-sm mt-1">Crea tu primera meta para comenzar a ahorrar con propósito</p>
            </div>
          </FadeIn>
        ) : (
          <div className="divide-y divide-gray-600">
            {goals.map((goal, index) => {
              const progress = (goal.currentAmount / goal.targetAmount) * 100;
              const daysRemaining = getDaysRemaining(goal.targetDate);
              const status = getGoalStatus(goal);
              const category = goalCategories[goal.category];

              return (
                <SlideIn key={goal.id} direction="up" delay={index * 100}>
                  <div className={`p-6 transition-colors duration-200 ${
                    theme === 'dark' ? 'hover:bg-[#2a2b38]' : 'hover:bg-slate-100'
                  }`}>
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex items-start gap-4">
                        <div className={`w-12 h-12 rounded-lg flex items-center justify-center text-white font-medium ${category.color}`}>
                          {category.icon}
                        </div>
                        <div>
                          <h3 className={`font-semibold transition-colors duration-200 ${
                            theme === 'dark' ? 'text-white' : 'text-slate-900'
                          }`}>
                            {goal.name}
                          </h3>
                          {goal.description && (
                            <p className={`text-sm mt-1 transition-colors duration-200 ${
                              theme === 'dark' ? 'text-gray-400' : 'text-slate-600'
                            }`}>
                              {goal.description}
                            </p>
                          )}
                          <div className="flex items-center gap-4 mt-2 text-sm">
                            <span className={`flex items-center gap-1 ${getStatusColor(status)}`}>
                              {getStatusIcon(status)}
                              {status === 'completed' ? 'Completada' :
                               status === 'overdue' ? 'Vencida' :
                               status === 'urgent' ? 'Urgente' :
                               status === 'on-track' ? 'En progreso' : 'Normal'}
                            </span>
                            <span className={`transition-colors duration-200 ${
                              theme === 'dark' ? 'text-gray-400' : 'text-slate-600'
                            }`}>
                              {daysRemaining >= 0 ? `${daysRemaining} días restantes` : `${Math.abs(daysRemaining)} días vencida`}
                            </span>
                          </div>
                        </div>
                      </div>

                      <div className="flex items-center gap-2">
                        {!goal.isCompleted && (
                          <button
                            onClick={() => setShowAddProgress(goal.id)}
                            className={`p-2 rounded-lg transition-colors duration-200 ${
                              theme === 'dark'
                                ? 'text-gray-400 hover:text-green-400 hover:bg-green-900/20'
                                : 'text-slate-400 hover:text-green-600 hover:bg-green-50'
                            }`}
                            title="Agregar progreso"
                          >
                            <Plus className="w-4 h-4" />
                          </button>
                        )}

                        <button
                          onClick={() => handleEdit(goal)}
                          className={`p-2 rounded-lg transition-colors duration-200 ${
                            theme === 'dark'
                              ? 'text-gray-400 hover:text-blue-400 hover:bg-blue-900/20'
                              : 'text-slate-400 hover:text-blue-600 hover:bg-blue-50'
                          }`}
                          title="Editar meta"
                        >
                          <Edit className="w-4 h-4" />
                        </button>

                        <button
                          onClick={() => handleDelete(goal.id)}
                          className={`p-2 rounded-lg transition-colors duration-200 ${
                            theme === 'dark'
                              ? 'text-gray-400 hover:text-red-400 hover:bg-red-900/20'
                              : 'text-slate-400 hover:text-red-600 hover:bg-red-50'
                          }`}
                          title="Eliminar meta"
                        >
                          <Trash2 className="w-4 h-4" />
                        </button>
                      </div>
                    </div>

                    {/* Progress */}
                    <div className="space-y-2">
                      <div className="flex justify-between items-center text-sm">
                        <span className={`transition-colors duration-200 ${
                          theme === 'dark' ? 'text-gray-400' : 'text-slate-600'
                        }`}>
                          ${goal.currentAmount.toLocaleString()} de ${goal.targetAmount.toLocaleString()}
                        </span>
                        <span className={`font-medium ${
                          progress >= 100 ? 'text-green-500' : 'text-blue-500'
                        }`}>
                          {progress.toFixed(1)}%
                        </span>
                      </div>

                      <ProgressBar
                        progress={progress}
                        color={progress >= 100 ? 'green' : 'blue'}
                        size="md"
                      />
                    </div>

                    {/* Add Progress Modal */}
                    {showAddProgress === goal.id && (
                      <div className="mt-4 p-4 rounded-lg border border-blue-500 bg-blue-50 dark:bg-blue-900/20">
                        <h4 className="font-medium mb-2 text-blue-700 dark:text-blue-300">
                          Agregar Progreso
                        </h4>
                        <div className="flex gap-2">
                          <input
                            type="number"
                            step="0.01"
                            value={progressAmount}
                            onChange={(e) => setProgressAmount(e.target.value)}
                            className={`flex-1 px-3 py-2 rounded-lg border transition-colors duration-200 ${
                              theme === 'dark'
                                ? 'bg-[#2a2b38] border-gray-600 text-white placeholder-gray-400'
                                : 'bg-white border-slate-300 text-slate-900 placeholder-slate-400'
                            }`}
                            placeholder="Monto a agregar"
                          />
                          <button
                            onClick={() => handleAddProgress(goal.id)}
                            className="px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg transition-colors duration-200"
                          >
                            Agregar
                          </button>
                          <button
                            onClick={() => setShowAddProgress(null)}
                            className={`px-4 py-2 rounded-lg border transition-colors duration-200 ${
                              theme === 'dark'
                                ? 'border-gray-600 text-gray-300 hover:bg-gray-700'
                                : 'border-slate-300 text-slate-700 hover:bg-slate-50'
                            }`}
                          >
                            Cancelar
                          </button>
                        </div>
                      </div>
                    )}
                  </div>
                </SlideIn>
              );
            })}
          </div>
        )}
      </div>
    </div>
  );
}
