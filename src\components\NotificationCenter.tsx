import React, { useState } from 'react';
import { <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Trash2, <PERSON><PERSON><PERSON>, <PERSON>ert<PERSON>riangle, Target, Trophy, Calendar } from 'lucide-react';
import { useTheme } from '../lib/ThemeContext';
import { useNotificationSystem, Notification } from '../hooks/useNotificationSystem';
import { FadeIn, SlideIn } from './ui/LoadingStates';
import { formatDistanceToNow, parseISO } from 'date-fns';
import { es } from 'date-fns/locale';
import { Link } from 'react-router-dom';

interface NotificationCenterProps {
  isOpen: boolean;
  onClose: () => void;
}

export function NotificationCenter({ isOpen, onClose }: NotificationCenterProps) {
  const { theme } = useTheme();
  const {
    notifications,
    unreadCount,
    markAsRead,
    markAllAsRead,
    deleteNotification,
    clearAllNotifications
  } = useNotificationSystem();

  const [filter, setFilter] = useState<'all' | 'unread' | 'budget' | 'goals' | 'achievements'>('all');

  const getNotificationIcon = (type: Notification['type']) => {
    switch (type) {
      case 'budget_warning':
      case 'budget_exceeded':
        return <AlertTriangle className="w-5 h-5 text-orange-500" />;
      case 'goal_progress':
      case 'goal_completed':
        return <Target className="w-5 h-5 text-blue-500" />;
      case 'achievement':
        return <Trophy className="w-5 h-5 text-yellow-500" />;
      case 'reminder':
        return <Calendar className="w-5 h-5 text-purple-500" />;
      default:
        return <Bell className="w-5 h-5 text-gray-500" />;
    }
  };

  const getPriorityColor = (priority: Notification['priority']) => {
    switch (priority) {
      case 'high':
        return 'border-l-red-500';
      case 'medium':
        return 'border-l-orange-500';
      case 'low':
        return 'border-l-blue-500';
      default:
        return 'border-l-gray-500';
    }
  };

  const filteredNotifications = notifications.filter(notification => {
    switch (filter) {
      case 'unread':
        return !notification.isRead;
      case 'budget':
        return notification.type === 'budget_warning' || notification.type === 'budget_exceeded';
      case 'goals':
        return notification.type === 'goal_progress' || notification.type === 'goal_completed';
      case 'achievements':
        return notification.type === 'achievement';
      default:
        return true;
    }
  });

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className={`rounded-xl max-w-2xl w-full max-h-[90vh] overflow-hidden transition-colors duration-200 ${
        theme === 'dark' 
          ? 'bg-[#21222d] border border-[#2a2a3d]' 
          : 'bg-white border border-slate-200 shadow-xl'
      }`}>
        {/* Header */}
        <div className="flex justify-between items-center p-6 border-b border-gray-600">
          <div className="flex items-center gap-3">
            <Bell className="w-6 h-6 text-blue-500" />
            <div>
              <h2 className={`text-xl font-bold transition-colors duration-200 ${
                theme === 'dark' ? 'text-white' : 'text-slate-900'
              }`}>
                Centro de Notificaciones
              </h2>
              {unreadCount > 0 && (
                <p className={`text-sm transition-colors duration-200 ${
                  theme === 'dark' ? 'text-gray-400' : 'text-slate-500'
                }`}>
                  {unreadCount} sin leer
                </p>
              )}
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            {unreadCount > 0 && (
              <button
                onClick={markAllAsRead}
                className={`p-2 rounded-lg transition-colors duration-200 ${
                  theme === 'dark'
                    ? 'text-gray-400 hover:text-blue-400 hover:bg-blue-900/20'
                    : 'text-slate-400 hover:text-blue-600 hover:bg-blue-50'
                }`}
                title="Marcar todas como leídas"
              >
                <CheckCheck className="w-4 h-4" />
              </button>
            )}
            
            {notifications.length > 0 && (
              <button
                onClick={clearAllNotifications}
                className={`p-2 rounded-lg transition-colors duration-200 ${
                  theme === 'dark'
                    ? 'text-gray-400 hover:text-red-400 hover:bg-red-900/20'
                    : 'text-slate-400 hover:text-red-600 hover:bg-red-50'
                }`}
                title="Eliminar todas"
              >
                <Trash2 className="w-4 h-4" />
              </button>
            )}
            
            <button
              onClick={onClose}
              className={`p-2 rounded-lg transition-colors duration-200 ${
                theme === 'dark'
                  ? 'text-gray-400 hover:text-white hover:bg-gray-700'
                  : 'text-slate-400 hover:text-slate-600 hover:bg-slate-100'
              }`}
            >
              <X className="w-4 h-4" />
            </button>
          </div>
        </div>

        {/* Filters */}
        <div className="p-4 border-b border-gray-600">
          <div className="flex flex-wrap gap-2">
            {[
              { key: 'all', label: 'Todas', count: notifications.length },
              { key: 'unread', label: 'Sin leer', count: unreadCount },
              { key: 'budget', label: 'Presupuestos', count: notifications.filter(n => n.type.includes('budget')).length },
              { key: 'goals', label: 'Metas', count: notifications.filter(n => n.type.includes('goal')).length },
              { key: 'achievements', label: 'Logros', count: notifications.filter(n => n.type === 'achievement').length }
            ].map(({ key, label, count }) => (
              <button
                key={key}
                onClick={() => setFilter(key as any)}
                className={`px-3 py-1 rounded-full text-sm font-medium transition-colors duration-200 ${
                  filter === key
                    ? 'bg-blue-500 text-white'
                    : theme === 'dark'
                      ? 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                      : 'bg-slate-100 text-slate-600 hover:bg-slate-200'
                }`}
              >
                {label} {count > 0 && `(${count})`}
              </button>
            ))}
          </div>
        </div>

        {/* Notifications List */}
        <div className="overflow-y-auto max-h-96">
          {filteredNotifications.length === 0 ? (
            <FadeIn>
              <div className={`text-center py-12 transition-colors duration-200 ${
                theme === 'dark' ? 'text-gray-500' : 'text-slate-500'
              }`}>
                <Bell className="w-12 h-12 mx-auto mb-4 opacity-50" />
                <p className="font-medium">No hay notificaciones</p>
                <p className="text-sm mt-1">
                  {filter === 'all' 
                    ? 'Todas las notificaciones aparecerán aquí'
                    : `No hay notificaciones en la categoría "${filter}"`
                  }
                </p>
              </div>
            </FadeIn>
          ) : (
            <div className="divide-y divide-gray-600">
              {filteredNotifications.map((notification, index) => (
                <SlideIn key={notification.id} direction="right" delay={index * 50}>
                  <div className={`p-4 transition-colors duration-200 border-l-4 ${
                    getPriorityColor(notification.priority)
                  } ${
                    !notification.isRead 
                      ? theme === 'dark' 
                        ? 'bg-blue-900/10' 
                        : 'bg-blue-50'
                      : theme === 'dark' 
                        ? 'hover:bg-[#2a2b38]' 
                        : 'hover:bg-slate-50'
                  }`}>
                    <div className="flex items-start gap-3">
                      <div className="flex-shrink-0 mt-1">
                        {getNotificationIcon(notification.type)}
                      </div>
                      
                      <div className="flex-1 min-w-0">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <h4 className={`font-medium transition-colors duration-200 ${
                              theme === 'dark' ? 'text-white' : 'text-slate-900'
                            }`}>
                              {notification.title}
                            </h4>
                            <p className={`text-sm mt-1 transition-colors duration-200 ${
                              theme === 'dark' ? 'text-gray-300' : 'text-slate-600'
                            }`}>
                              {notification.message}
                            </p>
                            
                            {/* Additional info */}
                            {(notification.amount || notification.percentage) && (
                              <div className="flex items-center gap-4 mt-2 text-xs">
                                {notification.amount && (
                                  <span className={`transition-colors duration-200 ${
                                    theme === 'dark' ? 'text-gray-400' : 'text-slate-500'
                                  }`}>
                                    Monto: ${notification.amount.toLocaleString()}
                                  </span>
                                )}
                                {notification.percentage && (
                                  <span className={`transition-colors duration-200 ${
                                    theme === 'dark' ? 'text-gray-400' : 'text-slate-500'
                                  }`}>
                                    {notification.percentage.toFixed(1)}%
                                  </span>
                                )}
                              </div>
                            )}
                            
                            <div className="flex items-center justify-between mt-3">
                              <span className={`text-xs transition-colors duration-200 ${
                                theme === 'dark' ? 'text-gray-500' : 'text-slate-400'
                              }`}>
                                {formatDistanceToNow(parseISO(notification.createdAt), { 
                                  addSuffix: true, 
                                  locale: es 
                                })}
                              </span>
                              
                              {notification.actionUrl && (
                                <Link
                                  to={notification.actionUrl}
                                  onClick={onClose}
                                  className="text-xs text-blue-500 hover:text-blue-600 font-medium"
                                >
                                  {notification.actionLabel || 'Ver más'}
                                </Link>
                              )}
                            </div>
                          </div>
                          
                          <div className="flex items-center gap-1 ml-3">
                            {!notification.isRead && (
                              <button
                                onClick={() => markAsRead(notification.id)}
                                className={`p-1 rounded transition-colors duration-200 ${
                                  theme === 'dark'
                                    ? 'text-gray-400 hover:text-blue-400 hover:bg-blue-900/20'
                                    : 'text-slate-400 hover:text-blue-600 hover:bg-blue-50'
                                }`}
                                title="Marcar como leída"
                              >
                                <Check className="w-3 h-3" />
                              </button>
                            )}
                            
                            <button
                              onClick={() => deleteNotification(notification.id)}
                              className={`p-1 rounded transition-colors duration-200 ${
                                theme === 'dark'
                                  ? 'text-gray-400 hover:text-red-400 hover:bg-red-900/20'
                                  : 'text-slate-400 hover:text-red-600 hover:bg-red-50'
                              }`}
                              title="Eliminar"
                            >
                              <X className="w-3 h-3" />
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </SlideIn>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

// Notification Bell Component for Navbar
interface NotificationBellProps {
  onClick: () => void;
}

export function NotificationBell({ onClick }: NotificationBellProps) {
  const { theme } = useTheme();
  const { unreadCount } = useNotificationSystem();

  return (
    <button
      onClick={onClick}
      className={`relative p-2 rounded-lg transition-colors duration-200 ${
        theme === 'dark'
          ? 'text-gray-400 hover:text-white hover:bg-gray-700'
          : 'text-slate-500 hover:text-slate-700 hover:bg-slate-100'
      }`}
      title="Notificaciones"
    >
      <Bell className="w-5 h-5" />
      {unreadCount > 0 && (
        <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center font-medium">
          {unreadCount > 9 ? '9+' : unreadCount}
        </span>
      )}
    </button>
  );
}
