import React from 'react';
import { useTheme } from '../../lib/ThemeContext';

// Skeleton Loader Component
export function SkeletonLoader({ className = '', width = 'w-full', height = 'h-4' }: {
  className?: string;
  width?: string;
  height?: string;
}) {
  const { theme } = useTheme();
  
  return (
    <div 
      className={`${width} ${height} rounded animate-pulse ${
        theme === 'dark' ? 'bg-gray-700' : 'bg-gray-200'
      } ${className}`}
    />
  );
}

// Card Skeleton
export function CardSkeleton() {
  const { theme } = useTheme();
  
  return (
    <div className={`rounded-xl p-6 transition-colors duration-200 ${
      theme === 'dark' 
        ? 'bg-[#21222d] border border-[#2a2a3d]' 
        : 'bg-slate-50 border border-slate-200'
    }`}>
      <div className="flex items-center space-x-4">
        <SkeletonLoader width="w-12" height="h-12" className="rounded-lg" />
        <div className="flex-1 space-y-2">
          <SkeletonLoader width="w-3/4" height="h-4" />
          <SkeletonLoader width="w-1/2" height="h-3" />
        </div>
      </div>
    </div>
  );
}

// Transaction List Skeleton
export function TransactionListSkeleton({ count = 5 }: { count?: number }) {
  return (
    <div className="space-y-3">
      {Array.from({ length: count }).map((_, index) => (
        <TransactionSkeleton key={index} />
      ))}
    </div>
  );
}

// Individual Transaction Skeleton
export function TransactionSkeleton() {
  const { theme } = useTheme();
  
  return (
    <div className={`p-4 rounded-lg transition-colors duration-200 ${
      theme === 'dark' 
        ? 'bg-[#21222d] border border-[#2a2a3d]' 
        : 'bg-white border border-slate-200'
    }`}>
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <SkeletonLoader width="w-10" height="h-10" className="rounded-lg" />
          <div className="space-y-2">
            <SkeletonLoader width="w-32" height="h-4" />
            <SkeletonLoader width="w-24" height="h-3" />
          </div>
        </div>
        <div className="text-right space-y-2">
          <SkeletonLoader width="w-20" height="h-4" />
          <SkeletonLoader width="w-16" height="h-3" />
        </div>
      </div>
    </div>
  );
}

// Spinner Component
export function Spinner({ size = 'md', color = 'blue' }: {
  size?: 'sm' | 'md' | 'lg';
  color?: 'blue' | 'white' | 'gray';
}) {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-6 h-6',
    lg: 'w-8 h-8'
  };
  
  const colorClasses = {
    blue: 'text-blue-500',
    white: 'text-white',
    gray: 'text-gray-500'
  };
  
  return (
    <div className={`${sizeClasses[size]} ${colorClasses[color]} animate-spin`}>
      <svg className="w-full h-full" fill="none" viewBox="0 0 24 24">
        <circle 
          className="opacity-25" 
          cx="12" 
          cy="12" 
          r="10" 
          stroke="currentColor" 
          strokeWidth="4"
        />
        <path 
          className="opacity-75" 
          fill="currentColor" 
          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
        />
      </svg>
    </div>
  );
}

// Loading Button
export function LoadingButton({ 
  children, 
  isLoading = false, 
  disabled = false,
  className = '',
  ...props 
}: {
  children: React.ReactNode;
  isLoading?: boolean;
  disabled?: boolean;
  className?: string;
  [key: string]: any;
}) {
  return (
    <button
      disabled={disabled || isLoading}
      className={`relative ${className} ${
        (disabled || isLoading) ? 'opacity-50 cursor-not-allowed' : ''
      }`}
      {...props}
    >
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center">
          <Spinner size="sm" color="white" />
        </div>
      )}
      <span className={isLoading ? 'opacity-0' : 'opacity-100'}>
        {children}
      </span>
    </button>
  );
}

// Pulse Animation
export function PulseAnimation({ children, className = '' }: {
  children: React.ReactNode;
  className?: string;
}) {
  return (
    <div className={`animate-pulse ${className}`}>
      {children}
    </div>
  );
}

// Fade In Animation
export function FadeIn({ 
  children, 
  delay = 0, 
  duration = 300,
  className = '' 
}: {
  children: React.ReactNode;
  delay?: number;
  duration?: number;
  className?: string;
}) {
  return (
    <div 
      className={`animate-fadeIn ${className}`}
      style={{
        animationDelay: `${delay}ms`,
        animationDuration: `${duration}ms`
      }}
    >
      {children}
    </div>
  );
}

// Slide In Animation
export function SlideIn({ 
  children, 
  direction = 'up',
  delay = 0,
  className = '' 
}: {
  children: React.ReactNode;
  direction?: 'up' | 'down' | 'left' | 'right';
  delay?: number;
  className?: string;
}) {
  const directionClasses = {
    up: 'animate-slideInUp',
    down: 'animate-slideInDown',
    left: 'animate-slideInLeft',
    right: 'animate-slideInRight'
  };
  
  return (
    <div 
      className={`${directionClasses[direction]} ${className}`}
      style={{ animationDelay: `${delay}ms` }}
    >
      {children}
    </div>
  );
}

// Scale Animation
export function ScaleIn({ 
  children, 
  delay = 0,
  className = '' 
}: {
  children: React.ReactNode;
  delay?: number;
  className?: string;
}) {
  return (
    <div 
      className={`animate-scaleIn ${className}`}
      style={{ animationDelay: `${delay}ms` }}
    >
      {children}
    </div>
  );
}

// Progress Bar
export function ProgressBar({ 
  progress, 
  color = 'blue',
  size = 'md',
  showLabel = false,
  className = '' 
}: {
  progress: number;
  color?: 'blue' | 'green' | 'red' | 'yellow' | 'purple';
  size?: 'sm' | 'md' | 'lg';
  showLabel?: boolean;
  className?: string;
}) {
  const { theme } = useTheme();
  
  const sizeClasses = {
    sm: 'h-1',
    md: 'h-2',
    lg: 'h-3'
  };
  
  const colorClasses = {
    blue: 'bg-blue-500',
    green: 'bg-green-500',
    red: 'bg-red-500',
    yellow: 'bg-yellow-500',
    purple: 'bg-purple-500'
  };
  
  return (
    <div className={className}>
      <div className={`w-full rounded-full transition-colors duration-200 ${
        theme === 'dark' ? 'bg-gray-700' : 'bg-gray-200'
      } ${sizeClasses[size]}`}>
        <div
          className={`${sizeClasses[size]} rounded-full transition-all duration-500 ease-out ${colorClasses[color]}`}
          style={{ width: `${Math.min(Math.max(progress, 0), 100)}%` }}
        />
      </div>
      {showLabel && (
        <div className={`text-sm mt-1 transition-colors duration-200 ${
          theme === 'dark' ? 'text-gray-400' : 'text-gray-600'
        }`}>
          {Math.round(progress)}%
        </div>
      )}
    </div>
  );
}

// Empty State
export function EmptyState({ 
  icon, 
  title, 
  description, 
  action,
  className = '' 
}: {
  icon: React.ReactNode;
  title: string;
  description?: string;
  action?: React.ReactNode;
  className?: string;
}) {
  const { theme } = useTheme();
  
  return (
    <div className={`text-center py-12 ${className}`}>
      <div className={`mx-auto mb-4 opacity-50 transition-colors duration-200 ${
        theme === 'dark' ? 'text-gray-500' : 'text-gray-400'
      }`}>
        {icon}
      </div>
      <h3 className={`text-lg font-medium mb-2 transition-colors duration-200 ${
        theme === 'dark' ? 'text-white' : 'text-gray-900'
      }`}>
        {title}
      </h3>
      {description && (
        <p className={`text-sm mb-4 transition-colors duration-200 ${
          theme === 'dark' ? 'text-gray-400' : 'text-gray-600'
        }`}>
          {description}
        </p>
      )}
      {action && action}
    </div>
  );
}
