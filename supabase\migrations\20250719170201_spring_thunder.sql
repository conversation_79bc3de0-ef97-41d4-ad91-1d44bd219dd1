/*
  # Update RLS policies for transactions table

  1. Security Changes
    - Remove the existing "Allow all operations for now" policy
    - Add user-specific RLS policies for authenticated users
    - Ensure users can only access their own transactions

  2. New Policies
    - Users can read only their own transactions
    - Users can insert their own transactions  
    - Users can update their own transactions
    - Users can delete their own transactions
*/

-- Drop the existing permissive policy
DROP POLICY IF EXISTS "Allow all operations for now" ON transactions;

-- Add user_id column to link transactions to users
ALTER TABLE transactions ADD COLUMN IF NOT EXISTS user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE;

-- Create index for performance
CREATE INDEX IF NOT EXISTS transactions_user_id_idx ON transactions(user_id);

-- Update existing transactions to have a user_id (if any exist)
-- This is only needed if there are existing transactions without user_id
UPDATE transactions SET user_id = auth.uid() WHERE user_id IS NULL;

-- Make user_id NOT NULL after updating existing records
ALTER TABLE transactions ALTER COLUMN user_id SET NOT NULL;

-- Create RLS policies for authenticated users

-- Policy for SELECT: Users can only read their own transactions
CREATE POLICY "Users can read own transactions"
  ON transactions
  FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id);

-- Policy for INSERT: Users can only insert transactions for themselves
CREATE POLICY "Users can insert own transactions"
  ON transactions
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = user_id);

-- Policy for UPDATE: Users can only update their own transactions
CREATE POLICY "Users can update own transactions"
  ON transactions
  FOR UPDATE
  TO authenticated
  USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);

-- Policy for DELETE: Users can only delete their own transactions
CREATE POLICY "Users can delete own transactions"
  ON transactions
  FOR DELETE
  TO authenticated
  USING (auth.uid() = user_id);