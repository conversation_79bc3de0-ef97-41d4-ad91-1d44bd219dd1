import { MESES, Mes } from '../lib/types';

interface MonthSelectorProps {
  selectedMonth: Mes;
  onMonthChange: (month: Mes) => void;
}

export function MonthSelector({ selectedMonth, onMonthChange }: MonthSelectorProps) {
  return (
    <div className="w-full overflow-x-auto py-3 sticky top-16 z-10 bg-[#171821]">
      <div className="flex space-x-2 min-w-max">
        {MESES.map((mes) => (
          <button
            key={mes}
            onClick={() => onMonthChange(mes)}
            className={`modern-pill ${selectedMonth === mes ? 'selected' : ''}`}
          >
            {mes}
          </button>
        ))}
      </div>
    </div>
  );
}