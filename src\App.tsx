import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { Navbar } from './components/Navbar';
import { BottomNavigation } from './components/BottomNavigation';
import { SkipLinks } from './components/ui/AccessibleComponents';
import { Dashboard } from './components/Dashboard';
import { Reports } from './components/Reports';
import { TransactionsPage } from './components/TransactionsPage';
import { BudgetPage } from './components/BudgetPage';
import { PatrimonioNetoPage } from './components/PatrimonioNetoPage';
import { FlujoDeGastoPage } from './components/FlujoDeGastoPage';
import { FondoEmergenciaPage } from './components/FondoEmergenciaPage';
import { VidaRicaPage } from './components/VidaRicaPage';
import { DoceSemanasPoderosasPage } from './components/DoceSemanasPoderosasPage';
import { CalculadoraFIREPage } from './components/CalculadoraFIREPage';
import { IngresosPasivosPage } from './components/IngresosPasivosPage';
import { AuthPage } from './components/Auth/AuthPage';
import { ProtectedRoute } from './components/ProtectedRoute';
import { ThemeProvider } from './lib/ThemeContext';
import { AuthProvider } from './lib/AuthContext';
import { TransactionProvider } from './lib/TransactionContext';
import { ToastProvider } from './components/Toast';
import { ErrorBoundary } from './components/ErrorBoundary';
import { TooltipProvider } from './components/ui/Tooltip';
import { useAuth } from './lib/AuthContext';
import { useTheme } from './lib/ThemeContext';

function AppContent() {
  const { user } = useAuth();
  const { theme } = useTheme();

  return (
    <Router>
      <ErrorBoundary>
        <div className={`min-h-screen transition-colors duration-300 ${
          theme === 'dark' ? 'bg-[#171821]' : 'bg-white'
        }`}>
          <Routes>
            <Route path="/login" element={<AuthPage />} />
            <Route path="/register" element={<AuthPage />} />
            <Route path="/*" element={
              <ProtectedRoute>
                <ErrorBoundary>
                  <SkipLinks />
                  <Navbar />
                  <main
                    id="main-content"
                    className="container mx-auto px-4 py-8 pb-20 md:pb-8"
                    role="main"
                    tabIndex={-1}
                  >
                    <Routes>
                      <Route path="/" element={<Dashboard />} />
                      <Route path="/transactions" element={<TransactionsPage />} />
                      <Route path="/budgets" element={<BudgetPage />} />
                      <Route path="/patrimonio" element={<PatrimonioNetoPage />} />
                      <Route path="/flujo-gasto" element={<FlujoDeGastoPage />} />
                      <Route path="/fondo-emergencia" element={<FondoEmergenciaPage />} />
                      <Route path="/vida-rica" element={<VidaRicaPage />} />
                      <Route path="/12-semanas" element={<DoceSemanasPoderosasPage />} />
                      <Route path="/calculadora-fire" element={<CalculadoraFIREPage />} />
                      <Route path="/ingresos-pasivos" element={<IngresosPasivosPage />} />
                      <Route path="/reports" element={<Reports />} />
                    </Routes>
                  </main>

                  {/* Bottom Navigation for Mobile */}
                  <BottomNavigation />
                </ErrorBoundary>
              </ProtectedRoute>
            } />
          </Routes>
        </div>
      </ErrorBoundary>
    </Router>
  );
}

function App() {
  return (
    <ThemeProvider>
      <ToastProvider>
        <AuthProvider>
          <TransactionProvider>
            <TooltipProvider>
              <AppContent />
            </TooltipProvider>
          </TransactionProvider>
        </AuthProvider>
      </ToastProvider>
    </ThemeProvider>
  );
}

export default App;