import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { Suspense, lazy } from 'react';
import { Navbar } from './components/Navbar';
import { BottomNavigation } from './components/BottomNavigation';
import { SkipLinks } from './components/ui/AccessibleComponents';
import { Dashboard } from './components/Dashboard';
import { Reports } from './components/Reports';
import { AdvancedReportsPage } from './components/AdvancedReportsPage';
import { TransactionsPage } from './components/TransactionsPage';
import { BudgetPage } from './components/BudgetPage';
import { PageLoader } from './components/PageLoader';

// Lazy load less critical pages
const SavingsGoalsPage = lazy(() => import('./components/SavingsGoalsPage').then(m => ({ default: m.SavingsGoalsPage })));
const RecurringTransactionsPage = lazy(() => import('./components/RecurringTransactionsPage').then(m => ({ default: m.RecurringTransactionsPage })));
const FinancialProjectionsPage = lazy(() => import('./components/FinancialProjectionsPage').then(m => ({ default: m.FinancialProjectionsPage })));
const PeriodComparisonPage = lazy(() => import('./components/PeriodComparisonPage').then(m => ({ default: m.PeriodComparisonPage })));
const AdvancedSettingsPage = lazy(() => import('./components/AdvancedSettingsPage').then(m => ({ default: m.AdvancedSettingsPage })));
// Lazy load specialized pages
const PatrimonioNetoPage = lazy(() => import('./components/PatrimonioNetoPage').then(m => ({ default: m.PatrimonioNetoPage })));
const FlujoDeGastoPage = lazy(() => import('./components/FlujoDeGastoPage').then(m => ({ default: m.FlujoDeGastoPage })));
const FondoEmergenciaPage = lazy(() => import('./components/FondoEmergenciaPage').then(m => ({ default: m.FondoEmergenciaPage })));
const VidaRicaPage = lazy(() => import('./components/VidaRicaPage').then(m => ({ default: m.VidaRicaPage })));
const DoceSemanasPoderosasPage = lazy(() => import('./components/DoceSemanasPoderosasPage').then(m => ({ default: m.DoceSemanasPoderosasPage })));
const CalculadoraFIREPage = lazy(() => import('./components/CalculadoraFIREPage').then(m => ({ default: m.CalculadoraFIREPage })));
const IngresosPasivosPage = lazy(() => import('./components/IngresosPasivosPage').then(m => ({ default: m.IngresosPasivosPage })));
import { AuthPage } from './components/Auth/AuthPage';
import { ProtectedRoute } from './components/ProtectedRoute';
import { ThemeProvider } from './lib/ThemeContext';
import { AuthProvider } from './lib/AuthContext';
import { TransactionProvider } from './lib/TransactionContext';
import { ToastProvider } from './components/Toast';
import { ErrorBoundary } from './components/ErrorBoundary';
import { TooltipProvider } from './components/ui/Tooltip';
import { PWAPrompt } from './components/PWAPrompt';
import { OfflineIndicator } from './components/OfflineIndicator';
import { HelmetProvider } from 'react-helmet-async';
import { SEOHead } from './components/SEOHead';
import { useAuth } from './lib/AuthContext';
import { useTheme } from './lib/ThemeContext';

function AppContent() {
  const { user } = useAuth();
  const { theme } = useTheme();

  return (
    <Router>
      <ErrorBoundary>
        <div className={`min-h-screen transition-colors duration-300 ${
          theme === 'dark' ? 'bg-[#171821]' : 'bg-white'
        }`}>
          <Routes>
            <Route path="/login" element={<AuthPage />} />
            <Route path="/register" element={<AuthPage />} />
            <Route path="/*" element={
              <ProtectedRoute>
                <ErrorBoundary>
                  <SkipLinks />
                  <Navbar />
                  <main
                    id="main-content"
                    className="container mx-auto px-4 py-8 pb-20 md:pb-8"
                    role="main"
                    tabIndex={-1}
                  >
                    <Suspense fallback={<PageLoader />}>
                      <Routes>
                        <Route path="/" element={<Dashboard />} />
                        <Route path="/transactions" element={<TransactionsPage />} />
                        <Route path="/budgets" element={<BudgetPage />} />
                        <Route path="/savings-goals" element={<SavingsGoalsPage />} />
                        <Route path="/recurring-transactions" element={<RecurringTransactionsPage />} />
                        <Route path="/financial-projections" element={<FinancialProjectionsPage />} />
                        <Route path="/period-comparison" element={<PeriodComparisonPage />} />
                        <Route path="/advanced-settings" element={<AdvancedSettingsPage />} />
                        <Route path="/patrimonio" element={<PatrimonioNetoPage />} />
                        <Route path="/flujo-gasto" element={<FlujoDeGastoPage />} />
                        <Route path="/fondo-emergencia" element={<FondoEmergenciaPage />} />
                        <Route path="/vida-rica" element={<VidaRicaPage />} />
                        <Route path="/12-semanas" element={<DoceSemanasPoderosasPage />} />
                        <Route path="/calculadora-fire" element={<CalculadoraFIREPage />} />
                        <Route path="/ingresos-pasivos" element={<IngresosPasivosPage />} />
                        <Route path="/reports" element={<Reports />} />
                        <Route path="/advanced-reports" element={<AdvancedReportsPage />} />
                      </Routes>
                    </Suspense>
                  </main>

                  {/* Bottom Navigation for Mobile */}
                  <BottomNavigation />
                </ErrorBoundary>
              </ProtectedRoute>
            } />
          </Routes>
        </div>
      </ErrorBoundary>
    </Router>
  );
}

function App() {
  return (
    <HelmetProvider>
      <ThemeProvider>
        <ToastProvider>
        <AuthProvider>
          <TransactionProvider>
            <TooltipProvider>
              <AppContent />
              <PWAPrompt />
              <OfflineIndicator />
            </TooltipProvider>
          </TransactionProvider>
        </AuthProvider>
      </ToastProvider>
    </ThemeProvider>
  </HelmetProvider>
  );
}

export default App;