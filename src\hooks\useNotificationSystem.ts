import { useState, useEffect, useCallback } from 'react';
import { useTransactionContext } from '../lib/TransactionContext';
import { differenceInDays, parseISO, format } from 'date-fns';
import { es } from 'date-fns/locale';

export interface Notification {
  id: string;
  type: 'budget_warning' | 'budget_exceeded' | 'goal_progress' | 'goal_completed' | 'reminder' | 'achievement';
  title: string;
  message: string;
  priority: 'low' | 'medium' | 'high';
  category?: string;
  amount?: number;
  percentage?: number;
  createdAt: string;
  isRead: boolean;
  actionUrl?: string;
  actionLabel?: string;
}

interface NotificationSettings {
  budgetWarnings: boolean;
  budgetWarningThreshold: number; // Percentage
  goalProgress: boolean;
  goalProgressInterval: number; // Days
  achievements: boolean;
  reminders: boolean;
  emailNotifications: boolean;
  pushNotifications: boolean;
}

const defaultSettings: NotificationSettings = {
  budgetWarnings: true,
  budgetWarningThreshold: 80,
  goalProgress: true,
  goalProgressInterval: 7,
  achievements: true,
  reminders: true,
  emailNotifications: false,
  pushNotifications: true
};

export function useNotificationSystem() {
  const { transactions } = useTransactionContext();
  
  const [notifications, setNotifications] = useState<Notification[]>(() => {
    const saved = localStorage.getItem('notifications');
    return saved ? JSON.parse(saved) : [];
  });

  const [settings, setSettings] = useState<NotificationSettings>(() => {
    const saved = localStorage.getItem('notificationSettings');
    return saved ? { ...defaultSettings, ...JSON.parse(saved) } : defaultSettings;
  });

  // Save notifications to localStorage
  const saveNotifications = useCallback((newNotifications: Notification[]) => {
    localStorage.setItem('notifications', JSON.stringify(newNotifications));
    setNotifications(newNotifications);
  }, []);

  // Save settings to localStorage
  const saveSettings = useCallback((newSettings: NotificationSettings) => {
    localStorage.setItem('notificationSettings', JSON.stringify(newSettings));
    setSettings(newSettings);
  }, []);

  // Add new notification
  const addNotification = useCallback((notification: Omit<Notification, 'id' | 'createdAt' | 'isRead'>) => {
    const newNotification: Notification = {
      ...notification,
      id: Date.now().toString(),
      createdAt: new Date().toISOString(),
      isRead: false
    };

    setNotifications(prev => {
      const updated = [newNotification, ...prev];
      localStorage.setItem('notifications', JSON.stringify(updated));
      return updated;
    });

    // Show browser notification if enabled
    if (settings.pushNotifications && 'Notification' in window && Notification.permission === 'granted') {
      new Notification(notification.title, {
        body: notification.message,
        icon: '/favicon.ico',
        badge: '/favicon.ico'
      });
    }

    return newNotification.id;
  }, [settings.pushNotifications]);

  // Mark notification as read
  const markAsRead = useCallback((notificationId: string) => {
    setNotifications(prev => {
      const updated = prev.map(n => 
        n.id === notificationId ? { ...n, isRead: true } : n
      );
      localStorage.setItem('notifications', JSON.stringify(updated));
      return updated;
    });
  }, []);

  // Mark all notifications as read
  const markAllAsRead = useCallback(() => {
    setNotifications(prev => {
      const updated = prev.map(n => ({ ...n, isRead: true }));
      localStorage.setItem('notifications', JSON.stringify(updated));
      return updated;
    });
  }, []);

  // Delete notification
  const deleteNotification = useCallback((notificationId: string) => {
    setNotifications(prev => {
      const updated = prev.filter(n => n.id !== notificationId);
      localStorage.setItem('notifications', JSON.stringify(updated));
      return updated;
    });
  }, []);

  // Clear all notifications
  const clearAllNotifications = useCallback(() => {
    setNotifications([]);
    localStorage.removeItem('notifications');
  }, []);

  // Request notification permission
  const requestNotificationPermission = useCallback(async () => {
    if ('Notification' in window) {
      const permission = await Notification.requestPermission();
      return permission === 'granted';
    }
    return false;
  }, []);

  // Check budgets and create notifications
  const checkBudgets = useCallback(() => {
    if (!settings.budgetWarnings) return;

    const budgets = JSON.parse(localStorage.getItem('budgets') || '[]');
    const currentMonth = format(new Date(), 'MMMM', { locale: es });

    budgets.forEach((budget: any) => {
      if (budget.month !== currentMonth) return;

      // Calculate spent amount for this budget
      const categoryTransactions = transactions.filter(t => 
        t.category === budget.category && 
        t.type === budget.type &&
        t.date.startsWith(new Date().getFullYear().toString())
      );
      
      const spent = categoryTransactions.reduce((sum, t) => sum + t.amount, 0);
      const percentage = budget.limit > 0 ? (spent / budget.limit) * 100 : 0;

      // Check if we should send a warning
      if (percentage >= settings.budgetWarningThreshold && percentage < 100) {
        // Check if we already sent this warning recently
        const recentWarning = notifications.find(n => 
          n.type === 'budget_warning' &&
          n.category === budget.category &&
          differenceInDays(new Date(), parseISO(n.createdAt)) < 1
        );

        if (!recentWarning) {
          addNotification({
            type: 'budget_warning',
            title: 'Alerta de Presupuesto',
            message: `Has gastado ${percentage.toFixed(1)}% de tu presupuesto en ${budget.category}`,
            priority: 'medium',
            category: budget.category,
            amount: spent,
            percentage,
            actionUrl: '/budgets',
            actionLabel: 'Ver Presupuestos'
          });
        }
      } else if (percentage >= 100) {
        // Check if we already sent exceeded notification
        const recentExceeded = notifications.find(n => 
          n.type === 'budget_exceeded' &&
          n.category === budget.category &&
          differenceInDays(new Date(), parseISO(n.createdAt)) < 1
        );

        if (!recentExceeded) {
          addNotification({
            type: 'budget_exceeded',
            title: 'Presupuesto Excedido',
            message: `Has excedido tu presupuesto en ${budget.category} por $${(spent - budget.limit).toLocaleString()}`,
            priority: 'high',
            category: budget.category,
            amount: spent,
            percentage,
            actionUrl: '/budgets',
            actionLabel: 'Revisar Presupuesto'
          });
        }
      }
    });
  }, [transactions, settings, notifications, addNotification]);

  // Check savings goals and create notifications
  const checkSavingsGoals = useCallback(() => {
    if (!settings.goalProgress) return;

    const goals = JSON.parse(localStorage.getItem('savingsGoals') || '[]');

    goals.forEach((goal: any) => {
      if (goal.isCompleted) return;

      const progress = (goal.currentAmount / goal.targetAmount) * 100;
      const daysRemaining = differenceInDays(parseISO(goal.targetDate), new Date());

      // Check for goal completion
      if (progress >= 100) {
        const recentCompletion = notifications.find(n => 
          n.type === 'goal_completed' &&
          n.message.includes(goal.name) &&
          differenceInDays(new Date(), parseISO(n.createdAt)) < 1
        );

        if (!recentCompletion) {
          addNotification({
            type: 'goal_completed',
            title: '¡Meta Completada!',
            message: `¡Felicitaciones! Has alcanzado tu meta "${goal.name}"`,
            priority: 'high',
            amount: goal.targetAmount,
            percentage: 100,
            actionUrl: '/savings-goals',
            actionLabel: 'Ver Metas'
          });
        }
      }
      // Check for progress milestones
      else if (progress >= 25 && progress < 50) {
        const recentMilestone = notifications.find(n => 
          n.type === 'goal_progress' &&
          n.message.includes(goal.name) &&
          n.message.includes('25%') &&
          differenceInDays(new Date(), parseISO(n.createdAt)) < 7
        );

        if (!recentMilestone) {
          addNotification({
            type: 'goal_progress',
            title: 'Progreso en Meta',
            message: `Has alcanzado el 25% de tu meta "${goal.name}"`,
            priority: 'low',
            percentage: progress,
            actionUrl: '/savings-goals',
            actionLabel: 'Ver Progreso'
          });
        }
      }
      // Check for urgent goals (less than 30 days and low progress)
      else if (daysRemaining <= 30 && daysRemaining > 0 && progress < 50) {
        const recentUrgent = notifications.find(n => 
          n.type === 'reminder' &&
          n.message.includes(goal.name) &&
          differenceInDays(new Date(), parseISO(n.createdAt)) < 7
        );

        if (!recentUrgent) {
          addNotification({
            type: 'reminder',
            title: 'Meta Próxima a Vencer',
            message: `Tu meta "${goal.name}" vence en ${daysRemaining} días y tienes ${progress.toFixed(1)}% completado`,
            priority: 'medium',
            percentage: progress,
            actionUrl: '/savings-goals',
            actionLabel: 'Agregar Progreso'
          });
        }
      }
    });
  }, [settings, notifications, addNotification]);

  // Check for achievements
  const checkAchievements = useCallback(() => {
    if (!settings.achievements) return;

    const totalTransactions = transactions.length;
    const thisMonthTransactions = transactions.filter(t => 
      t.date.startsWith(format(new Date(), 'yyyy-MM'))
    ).length;

    // First transaction achievement
    if (totalTransactions === 1) {
      addNotification({
        type: 'achievement',
        title: '¡Primera Transacción!',
        message: 'Has registrado tu primera transacción. ¡Excelente comienzo!',
        priority: 'low',
        actionUrl: '/transactions',
        actionLabel: 'Ver Transacciones'
      });
    }
    // 100 transactions milestone
    else if (totalTransactions === 100) {
      addNotification({
        type: 'achievement',
        title: '¡100 Transacciones!',
        message: 'Has registrado 100 transacciones. ¡Eres muy constante!',
        priority: 'low',
        actionUrl: '/advanced-reports',
        actionLabel: 'Ver Análisis'
      });
    }
    // Active month (20+ transactions)
    else if (thisMonthTransactions === 20) {
      addNotification({
        type: 'achievement',
        title: '¡Mes Activo!',
        message: 'Has registrado 20 transacciones este mes. ¡Excelente seguimiento!',
        priority: 'low',
        actionUrl: '/advanced-reports',
        actionLabel: 'Ver Reportes'
      });
    }
  }, [transactions, settings, addNotification]);

  // Run checks periodically
  useEffect(() => {
    const runChecks = () => {
      checkBudgets();
      checkSavingsGoals();
      checkAchievements();
    };

    // Run checks immediately
    runChecks();

    // Set up interval to run checks every hour
    const interval = setInterval(runChecks, 60 * 60 * 1000);

    return () => clearInterval(interval);
  }, [checkBudgets, checkSavingsGoals, checkAchievements]);

  // Get unread count
  const unreadCount = notifications.filter(n => !n.isRead).length;

  // Get notifications by priority
  const getNotificationsByPriority = useCallback((priority: 'low' | 'medium' | 'high') => {
    return notifications.filter(n => n.priority === priority);
  }, [notifications]);

  return {
    notifications,
    settings,
    unreadCount,
    addNotification,
    markAsRead,
    markAllAsRead,
    deleteNotification,
    clearAllNotifications,
    saveSettings,
    requestNotificationPermission,
    getNotificationsByPriority
  };
}
