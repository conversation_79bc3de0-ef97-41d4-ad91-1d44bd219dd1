// Funciones para calcular métricas financieras basadas en transacciones reales
import { Transaction } from './types';
import { Mes, MESES } from './types';

export interface FinancialMetrics {
  totalIngresos: number;
  totalGastos: number;
  remanente: number;
  tasaAhorroInversion: number;
  gastosPorCategoria: { categoria: string; total: number; color: string }[];
  ingresosPorCategoria: { categoria: string; total: number; color: string }[];
  transaccionesPorMes: { mes: string; ingresos: number; gastos: number; ahorro: number }[];
}

// Colores para categorías
const coloresCategorias = {
  // Ingresos
  salario: '#10B981',
  freelance: '#8B5CF6',
  intereses: '#3B82F6',
  dividendos: '#06B6D4',
  
  // Gastos
  vivienda: '#f9769d',
  alimentacion: '#f472b6',
  transporte: '#d946ef',
  entretenimiento: '#c084fc',
  servicios: '#a78bfa',
  salud: '#fb7185',
  educacion: '#fbbf24',
  compras: '#34d399',
  suscripciones: '#60a5fa',
  tarjeta: '#ef4444',
  prestamo: '#f97316'
};

// Helper function to get current month as Mes type
export function getCurrentMonth(): Mes {
  const currentMonthIndex = new Date().getMonth();
  return MESES[currentMonthIndex];
}

// Helper function to get month index from Mes string
function getMonthIndex(mes: Mes): number {
  return MESES.indexOf(mes);
}

export function calculateFinancialMetrics(transactions: Transaction[], selectedMonth?: Mes): FinancialMetrics {
  // Use current month if no month is specified
  const targetMonth = selectedMonth || getCurrentMonth();
  const targetMonthIndex = getMonthIndex(targetMonth);
  const currentYear = new Date().getFullYear();
  
  // Filter transactions for the selected month
  const monthTransactions = transactions.filter(t => {
    const transactionDate = new Date(t.date);
    return transactionDate.getMonth() === targetMonthIndex && 
           transactionDate.getFullYear() === currentYear;
  });

  // Calcular totales
  const totalIngresos = monthTransactions
    .filter(t => t.type === 'income')
    .filter(t => t.amount > 0)
    .reduce((sum, t) => sum + t.amount, 0);

  const totalGastos = monthTransactions
    .filter(t => t.type === 'expense')
    .filter(t => t.amount > 0)
    .reduce((sum, t) => sum + t.amount, 0);

  const remanente = totalIngresos - totalGastos;
  const tasaAhorroInversion = totalIngresos > 0 ? (remanente / totalIngresos) * 100 : 0;

  // Agrupar gastos por categoría
  const gastosAgrupados = monthTransactions
    .filter(t => t.type === 'expense')
    .reduce((acc, t) => {
      acc[t.category] = (acc[t.category] || 0) + t.amount;
      return acc;
    }, {} as Record<string, number>);

  const gastosPorCategoria = Object.entries(gastosAgrupados).map(([categoria, total]) => ({
    categoria: categoria.charAt(0).toUpperCase() + categoria.slice(1),
    total,
    color: coloresCategorias[categoria] || '#6B7280'
  }));

  // Agrupar ingresos por categoría
  const ingresosAgrupados = monthTransactions
    .filter(t => t.type === 'income')
    .reduce((acc, t) => {
      acc[t.category] = (acc[t.category] || 0) + t.amount;
      return acc;
    }, {} as Record<string, number>);

  const ingresosPorCategoria = Object.entries(ingresosAgrupados).map(([categoria, total]) => ({
    categoria: categoria.charAt(0).toUpperCase() + categoria.slice(1),
    total,
    color: coloresCategorias[categoria] || '#10B981'
  }));

  // Calcular tendencia por mes (últimos 6 meses)
  const transaccionesPorMes = [];
  const meses = ['Ene', 'Feb', 'Mar', 'Abr', 'May', 'Jun', 'Jul', 'Ago', 'Sep', 'Oct', 'Nov', 'Dic'];
  
  for (let i = 5; i >= 0; i--) {
    const fecha = new Date();
    fecha.setMonth(fecha.getMonth() - i);
    const mes = fecha.getMonth();
    const año = fecha.getFullYear();
    
    const transaccionesDelMes = transactions.filter(t => {
      const transactionDate = new Date(t.date);
      return transactionDate.getMonth() === mes && transactionDate.getFullYear() === año;
    });
    
    const ingresos = transaccionesDelMes
      .filter(t => t.type === 'income')
      .reduce((sum, t) => sum + t.amount, 0);
    
    const gastos = transaccionesDelMes
      .filter(t => t.type === 'expense')
      .reduce((sum, t) => sum + t.amount, 0);
    
    transaccionesPorMes.push({
      mes: meses[mes],
      ingresos,
      gastos,
      ahorro: ingresos - gastos
    });
  }

  return {
    totalIngresos,
    totalGastos,
    remanente,
    tasaAhorroInversion,
    gastosPorCategoria,
    ingresosPorCategoria,
    transaccionesPorMes
  };
}

export function formatCurrency(amount: number): string {
  return new Intl.NumberFormat('es-ES', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(Math.round(amount));
}

export function formatNumber(amount: number): string {
  return new Intl.NumberFormat('es-ES', {
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(Math.round(amount));
}

export function formatDecimal(amount: number, decimals: number = 1): string {
  return new Intl.NumberFormat('es-ES', {
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals
  }).format(Number(amount.toFixed(decimals)));
}

export function getProgressColor(percentage: number): string {
  if (percentage < 30) return 'bg-red-500';
  if (percentage < 70) return 'bg-yellow-500';
  return 'bg-green-500';
}

export function getTasaAhorroMessage(tasa: number): string {
  if (tasa >= 30) {
    return "🎯 ¡Excelente trabajo Carlos! Estás ahorrando un porcentaje ideal de tus ingresos.";
  } else if (tasa >= 20) {
    return "👍 ¡Buen trabajo Carlos! Tu tasa de ahorro está por encima del promedio.";
  } else if (tasa >= 10) {
    return "👌 Vas por buen camino Carlos. Intenta aumentar tu tasa de ahorro gradualmente.";
  } else if (tasa > 0) {
    return "💪 Estás ahorrando algo, que es un buen comienzo. Busca aumentar este porcentaje.";
  } else {
    return "⚠️ Este mes tus gastos superaron tus ingresos. Revisa tu presupuesto.";
  }
}