export interface Subcategoria {
  id: string;
  nombre: string;
  estimado: number;
  real: number;
}

export interface CategoriaFinanciera {
  id: string;
  nombre: string;
  subcategorias: Subcategoria[];
  totalEstimado: number;
  totalReal: number;
}

export interface RegistroMensual {
  id: string;
  mes: string;
  año: number;
  ingresos: CategoriaFinanciera;
  gastosEsenciales: CategoriaFinanciera;
  gastosDiscrecionales: CategoriaFinanciera;
  deudas: CategoriaFinanciera;
  ahorros: CategoriaFinanciera;
  inversiones: CategoriaFinanciera;
  tasaAhorroInversion: number;
}

export interface ActivoPasivo {
  id: string;
  nombre: string;
  valor: number;
  categoria: string;
}

export interface PatrimonioMensual {
  id: string;
  mes: string;
  año: number;
  activos: ActivoPasivo[];
  pasivos: ActivoPasivo[];
  totalActivos: number;
  totalPasivos: number;
  patrimonioNeto: number;
  cambioMensual: number;
}

export type Mes = 
  | 'Enero' 
  | 'Febrero' 
  | 'Marzo' 
  | 'Abril' 
  | 'Mayo' 
  | 'Junio' 
  | 'Julio' 
  | 'Agosto' 
  | 'Septiembre' 
  | 'Octubre' 
  | 'Noviembre' 
  | 'Diciembre';

export const MESES: Mes[] = [
  'Enero', 'Febrero', 'Marzo', 'Abril', 'Mayo', 'Junio',
  'Julio', 'Agosto', 'Septiembre', 'Octubre', 'Noviembre', 'Diciembre'
];

export interface EstadoFinanciero {
  superavit: boolean;
  patrimonioPositivo: boolean;
  tasaAhorroInversion: number;
}

export interface Transaction {
  id: string;
  description: string;
  date: string;
  type: 'income' | 'expense';
  amount: number;
  category: string;
  icon?: string;
  is_essential?: boolean;
  is_flexible?: boolean;
  status?: 'pendiente' | 'pagado' | 'parcial' | 'pospuesto' | 'cancelado';
  is_planned?: boolean;
  priority_order?: number;
}

// Interfaces for Vida Rica component
export interface GastoReal {
  id: string;
  categoria: string;
  subcategoria?: string;
  monto: number;
  fecha: string;
}

export interface GastoFeliz {
  id: string;
  categoria: string;
  presupuesto: number;
  icono: JSX.Element;
  gastado?: number;
}

export interface NoPrioridad {
  id: string;
  descripcion: string;
  checked: boolean;
}

export interface Alerta {
  id: string;
  tipo: 'subutilizado' | 'noPrioridad' | 'porcentajeBajo' | 'recomendacion' | 'success';
  mensaje: string;
  categoria?: string;
  icono: JSX.Element;
  accion?: string;
}

export interface DistribucionGastos {
  felices: number;
  neutrales: number;
  noDeseados: number;
}