import { useState, useEffect } from 'react';
import { Card } from './ui/Card';
import { 
  Calendar, ChevronDown, ChevronUp, Target, ListChecks, BookText, 
  ArrowRight, Pencil, Check, X, BarChart, Medal, History,
  GanttChart, MessageSquare, FilePlus, PieChart, PlusCircle,
  AlertTriangle, Trophy, FileDown, RefreshCw, Upload, ExternalLink,
  Sparkles, Heart
} from 'lucide-react';
import { InfoTooltip } from './ui/Tooltip';
import { useTheme } from '../lib/ThemeContext';
import { Link } from 'react-router-dom';
import { Label } from '@radix-ui/react-label';

// Interfaces para los tipos de datos
interface AccionSemanal {
  id: string;
  descripcion: string;
  estado: 'pendiente' | 'completada' | 'fallida';
  comentario?: string;
}

interface SemanaPoderosa {
  id: string;
  numero: number;
  accionClave: string;
  acciones: AccionSemanal[];
  estado: 'pendiente' | 'en-progreso' | 'completada';
  puntuacion: number;
  reflexion: {
    logros: string;
    ajustes: string;
    perdidaFoco: string;
  };
}

interface CicloPoderoso {
  id: string;
  objetivo: string;
  razon: string;
  fechaInicio: Date;
  fechaFin: Date;
  vidaRicaInicial: number;
  vidaRicaFinal?: number;
  semanas: SemanaPoderosa[];
  estado: 'no-iniciado' | 'en-progreso' | 'completado';
}

export function DoceSemanasPoderosasPage() {
  const { theme } = useTheme();
  
  // Estados para manejar los ciclos y semanas
  const [cicloActual, setCicloActual] = useState<CicloPoderoso>({
    id: '1',
    objetivo: 'Automatizar mi ingreso con un producto digital y subir mi Vida Rica al 60%',
    razon: 'Quiero tener mayor libertad financiera y tiempo para mi familia',
    fechaInicio: new Date(2023, 4, 1),
    fechaFin: new Date(2023, 6, 23),
    vidaRicaInicial: 45,
    semanas: generarSemanasIniciales(),
    estado: 'en-progreso'
  });
  
  const [ciclosAnteriores, setCiclosAnteriores] = useState<CicloPoderoso[]>([]);
  const [semanaActiva, setSemanaActiva] = useState<number>(0);
  const [diasRestantes, setDiasRestantes] = useState<number>(0);
  const [seccionExpandida, setSeccionExpandida] = useState<string>('vision');
  const [editandoObjetivo, setEditandoObjetivo] = useState<boolean>(false);
  const [nuevoObjetivo, setNuevoObjetivo] = useState<string>(cicloActual.objetivo);
  const [nuevaRazon, setNuevaRazon] = useState<string>(cicloActual.razon);
  
  const [mostrarModalNuevaAccion, setMostrarModalNuevaAccion] = useState<boolean>(false);
  const [nuevaAccion, setNuevaAccion] = useState<string>('');
  const [semanaSeleccionada, setSemanaSeleccionada] = useState<number | null>(null);
  
  const [mostrarModalReflexion, setMostrarModalReflexion] = useState<boolean>(false);
  const [reflexionTemporal, setReflexionTemporal] = useState({
    logros: '',
    ajustes: '',
    perdidaFoco: ''
  });
  
  // Función para generar semanas iniciales con datos de ejemplo
  function generarSemanasIniciales(): SemanaPoderosa[] {
    const acciones = [
      { semana: 1, accion: 'Validar idea con 5 personas', estado: 'completada' as const },
      { semana: 2, accion: 'Crear MVP del producto', estado: 'completada' as const },
      { semana: 3, accion: 'Publicar primer video/testimonio', estado: 'en-progreso' as const },
      { semana: 4, accion: 'Lanzar campaña de ventas', estado: 'pendiente' as const },
      { semana: 5, accion: 'Analizar resultados iniciales', estado: 'pendiente' as const },
      { semana: 6, accion: 'Implementar mejoras del producto', estado: 'pendiente' as const },
      { semana: 7, accion: 'Escalar marketing', estado: 'pendiente' as const },
      { semana: 8, accion: 'Automatizar procesos clave', estado: 'pendiente' as const },
      { semana: 9, accion: 'Establecer embudo de ventas', estado: 'pendiente' as const },
      { semana: 10, accion: 'Crear contenido para 1 mes', estado: 'pendiente' as const },
      { semana: 11, accion: 'Expandir a nuevo segmento', estado: 'pendiente' as const },
      { semana: 12, accion: 'Evaluar resultados del ciclo', estado: 'pendiente' as const }
    ];
    
    return acciones.map((item, index) => {
      // Generar algunas acciones de ejemplo para cada semana
      const numAcciones = 3 + Math.floor(Math.random() * 3); // 3-5 acciones
      const accionesSemana: AccionSemanal[] = [];
      
      for (let i = 1; i <= numAcciones; i++) {
        const estado = index < 2 
          ? 'completada' as const 
          : index === 2 
            ? (i <= 2 ? 'completada' as const : (i === 3 ? 'fallida' as const : 'pendiente' as const))
            : 'pendiente' as const;
            
        accionesSemana.push({
          id: `s${index+1}-a${i}`,
          descripcion: `Tarea ${i} de la semana ${index+1}`,
          estado,
          comentario: estado === 'fallida' ? 'Hubo problemas técnicos' : undefined
        });
      }
      
      // Estado de la semana basado en la fecha actual simulada
      const estado = 
        index < 2 ? 'completada' as const : 
        index === 2 ? 'en-progreso' as const : 
        'pendiente' as const;
      
      // Calcular puntuación para semanas pasadas
      const puntuacion = index < 2 
        ? 85 + Math.floor(Math.random() * 16) // 85-100 para semanas completadas
        : index === 2 
          ? 60 + Math.floor(Math.random() * 26) // 60-85 para semana actual
          : 0;
          
      // Reflexiones para semanas completadas
      const reflexion = {
        logros: index < 2 ? 'Logré mantener el foco y completar la mayoría de las tareas' : '',
        ajustes: index < 2 ? 'Mejorar la planificación del tiempo para tareas largas' : '',
        perdidaFoco: index < 2 ? 'Distracciones con redes sociales y noticias' : ''
      };
      
      return {
        id: `semana-${index+1}`,
        numero: index + 1,
        accionClave: item.accion,
        acciones: accionesSemana,
        estado,
        puntuacion,
        reflexion
      };
    });
  }
  
  // Efecto para calcular días restantes
  useEffect(() => {
    const calcularDiasRestantes = () => {
      const hoy = new Date();
      const finCiclo = new Date(cicloActual.fechaFin);
      
      // Si ya pasó la fecha de finalización
      if (hoy > finCiclo) {
        setDiasRestantes(0);
        return;
      }
      
      // Calcular diferencia en días
      const diferencia = Math.ceil((finCiclo.getTime() - hoy.getTime()) / (1000 * 60 * 60 * 24));
      setDiasRestantes(diferencia);
    };
    
    calcularDiasRestantes();
    
    // Actualizar cada día
    const intervalo = setInterval(calcularDiasRestantes, 1000 * 60 * 60 * 24);
    
    return () => clearInterval(intervalo);
  }, [cicloActual.fechaFin]);
  
  // Función para alternar la expansión de secciones
  const toggleSeccion = (seccion: string) => {
    if (seccionExpandida === seccion) {
      setSeccionExpandida('');
    } else {
      setSeccionExpandida(seccion);
    }
  };
  
  // Función para guardar cambios en el objetivo
  const guardarObjetivo = () => {
    setCicloActual({
      ...cicloActual,
      objetivo: nuevoObjetivo,
      razon: nuevaRazon
    });
    setEditandoObjetivo(false);
  };
  
  // Función para calcular estadísticas del ciclo
  const calcularEstadisticasCiclo = () => {
    const totalSemanas = cicloActual.semanas.length;
    const semanasCompletadas = cicloActual.semanas.filter(s => s.estado === 'completada').length;
    const semanasEnProgreso = cicloActual.semanas.filter(s => s.estado === 'en-progreso').length;
    
    // Calcular puntuación promedio de semanas completadas o en progreso
    const semanasConPuntuacion = cicloActual.semanas.filter(s => s.puntuacion > 0);
    const puntuacionPromedio = semanasConPuntuacion.length > 0
      ? semanasConPuntuacion.reduce((acc, sem) => acc + sem.puntuacion, 0) / semanasConPuntuacion.length
      : 0;
    
    // Calcular tareas
    const todasLasAcciones = cicloActual.semanas.flatMap(s => s.acciones);
    const accionesCompletadas = todasLasAcciones.filter(a => a.estado === 'completada').length;
    const accionesFallidas = todasLasAcciones.filter(a => a.estado === 'fallida').length;
    const accionesPendientes = todasLasAcciones.filter(a => a.estado === 'pendiente').length;
    
    return {
      totalSemanas,
      semanasCompletadas,
      semanasEnProgreso,
      puntuacionPromedio,
      accionesCompletadas,
      accionesFallidas,
      accionesPendientes,
      totalAcciones: todasLasAcciones.length
    };
  };
  
  // Función para actualizar el estado de una acción
  const actualizarEstadoAccion = (semanaIndex: number, accionId: string, nuevoEstado: 'pendiente' | 'completada' | 'fallida') => {
    const nuevasSemanas = [...cicloActual.semanas];
    const semana = nuevasSemanas[semanaIndex];
    
    // Actualizar la acción específica
    const nuevasAcciones = semana.acciones.map(accion => 
      accion.id === accionId ? { ...accion, estado: nuevoEstado } : accion
    );
    
    // Calcular nueva puntuación
    const accionesCompletadas = nuevasAcciones.filter(a => a.estado === 'completada').length;
    const nuevaPuntuacion = Math.round((accionesCompletadas / nuevasAcciones.length) * 100);
    
    // Actualizar semana
    nuevasSemanas[semanaIndex] = {
      ...semana,
      acciones: nuevasAcciones,
      puntuacion: nuevaPuntuacion,
      estado: 
        nuevaPuntuacion === 100 ? 'completada' : 
        nuevaPuntuacion > 0 ? 'en-progreso' : 
        'pendiente'
    };
    
    setCicloActual({
      ...cicloActual,
      semanas: nuevasSemanas
    });
  };
  
  // Función para añadir una nueva acción a una semana
  const agregarAccion = () => {
    if (!nuevaAccion.trim() || semanaSeleccionada === null) {
      return;
    }
    
    const nuevasSemanas = [...cicloActual.semanas];
    const semana = nuevasSemanas[semanaSeleccionada];
    
    const nuevaAccionObj: AccionSemanal = {
      id: `s${semana.numero}-a${semana.acciones.length + 1}`,
      descripcion: nuevaAccion,
      estado: 'pendiente'
    };
    
    // Añadir la acción
    nuevasSemanas[semanaSeleccionada] = {
      ...semana,
      acciones: [...semana.acciones, nuevaAccionObj]
    };
    
    setCicloActual({
      ...cicloActual,
      semanas: nuevasSemanas
    });
    
    // Resetear el formulario
    setNuevaAccion('');
    setMostrarModalNuevaAccion(false);
  };
  
  // Función para guardar reflexión semanal
  const guardarReflexion = () => {
    if (semanaSeleccionada === null) return;
    
    const nuevasSemanas = [...cicloActual.semanas];
    
    nuevasSemanas[semanaSeleccionada] = {
      ...nuevasSemanas[semanaSeleccionada],
      reflexion: { ...reflexionTemporal }
    };
    
    setCicloActual({
      ...cicloActual,
      semanas: nuevasSemanas
    });
    
    setMostrarModalReflexion(false);
  };
  
  // Función para abrir el modal de reflexión
  const abrirModalReflexion = (semanaIndex: number) => {
    setSemanaSeleccionada(semanaIndex);
    setReflexionTemporal({ ...cicloActual.semanas[semanaIndex].reflexion });
    setMostrarModalReflexion(true);
  };
  
  // Función para generar el mensaje de motivación
  const generarMensajeMotivacion = () => {
    const stats = calcularEstadisticasCiclo();
    
    if (stats.semanasCompletadas === 0 && stats.semanasEnProgreso === 0) {
      return "¡Es el momento de empezar! Enfócate en avanzar cada día.";
    }
    
    if (stats.puntuacionPromedio > 90) {
      return "¡Estás manteniendo un ritmo excelente! Sigue así para lograr grandes resultados.";
    }
    
    if (stats.puntuacionPromedio > 75) {
      return "Buen progreso. Mantén la consistencia y revisa qué puedes mejorar.";
    }
    
    if (stats.puntuacionPromedio > 50) {
      return "Vas por buen camino. Identifica qué te está frenando y ajusta tu estrategia.";
    }
    
    return "No te rindas. Este es el momento de enfocarte y recuperar impulso.";
  };
  
  // Datos para el gráfico de progreso semanal
  const datosGraficoSemanal = cicloActual.semanas.map(semana => ({
    semana: semana.numero,
    puntuacion: semana.puntuacion,
    color: 
      semana.puntuacion >= 80 ? '#10B981' : 
      semana.puntuacion >= 60 ? '#FBBF24' : 
      semana.puntuacion > 0 ? '#F87171' : 
      '#6B7280'
  }));
  
  // Verificar si hay alertas de foco
  const alertaFoco = 
    cicloActual.semanas
      .filter(s => s.estado === 'en-progreso' || s.estado === 'completada')
      .slice(-2)
      .filter(s => s.acciones.filter(a => a.estado === 'fallida').length >= 3).length >= 2;
    
  // Estadísticas del ciclo
  const estadisticasCiclo = calcularEstadisticasCiclo();
  
  return (
    <div className="space-y-8 pb-20">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6">
        <h1 className="text-3xl font-bold text-white">12 Semanas Poderosas</h1>
        
        <div className="flex items-center gap-2">
          <InfoTooltip title="Crear nuevo ciclo" description="Crear nuevo ciclo">
            <button className="bg-[#2a2b38] text-gray-300 hover:text-white p-2 rounded-full">
              <FilePlus className="w-5 h-5" />
            </button>
          </InfoTooltip>

          <InfoTooltip title="Descargar reporte PDF" description="Descargar reporte PDF">
            <button className="bg-[#2a2b38] text-gray-300 hover:text-white p-2 rounded-full">
              <FileDown className="w-5 h-5" />
            </button>
          </InfoTooltip>

          <InfoTooltip title="Ver ciclos anteriores" description="Ver ciclos anteriores">
            <div className="bg-[#2a2b38] rounded-lg px-3 py-1.5 text-sm text-gray-300 flex items-center">
              <Calendar className="w-4 h-4 mr-1.5" />
              Ciclo actual
            </div>
          </InfoTooltip>
        </div>
      </div>
      
      {/* SECCIÓN 1 - VISIÓN DEL CICLO */}
      <Card>
        <div className="p-6">
          <div className="flex justify-between items-center mb-4">
            <div className="flex items-center">
              <Target className="h-6 w-6 text-[#f9769d] mr-2" />
              <h2 className="text-xl font-semibold">Visión del Ciclo</h2>
            </div>
            <button 
              onClick={() => toggleSeccion('vision')}
              className="text-gray-400 hover:text-white transition-colors"
            >
              {seccionExpandida === 'vision' ? <ChevronUp /> : <ChevronDown />}
            </button>
          </div>
          
          {seccionExpandida === 'vision' && (
            <div className="animate-fadeIn space-y-6">
              {/* Fechas y contador */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                <div className="bg-[#2a2b38] rounded-xl p-4 flex items-center gap-4">
                  <div className="w-12 h-12 rounded-full bg-blue-900/50 flex items-center justify-center text-blue-400">
                    <Calendar className="w-6 h-6" />
                  </div>
                  <div>
                    <h3 className="text-sm text-gray-400 mb-1">Fecha de inicio</h3>
                    <p className="font-semibold">{cicloActual.fechaInicio.toLocaleDateString('es-ES', { day: 'numeric', month: 'long', year: 'numeric' })}</p>
                  </div>
                </div>
                
                <div className="bg-[#2a2b38] rounded-xl p-4 flex items-center gap-4">
                  <div className="w-12 h-12 rounded-full bg-purple-900/50 flex items-center justify-center text-purple-400">
                    <Calendar className="w-6 h-6" />
                  </div>
                  <div>
                    <h3 className="text-sm text-gray-400 mb-1">Fecha de finalización</h3>
                    <p className="font-semibold">{cicloActual.fechaFin.toLocaleDateString('es-ES', { day: 'numeric', month: 'long', year: 'numeric' })}</p>
                  </div>
                </div>
                
                <div className="bg-gradient-to-r from-blue-900/50 to-purple-900/50 rounded-xl p-4 flex items-center gap-4">
                  <div className="w-12 h-12 rounded-full bg-white/10 backdrop-blur-sm flex items-center justify-center text-white">
                    <span className="text-2xl font-bold">{diasRestantes}</span>
                  </div>
                  <div>
                    <h3 className="text-sm text-gray-300 mb-1">⏳ Te quedan</h3>
                    <p className="font-semibold text-white">{diasRestantes} días para completar tu ciclo</p>
                  </div>
                </div>
              </div>
              
              {/* Objetivo y propósito */}
              {editandoObjetivo ? (
                <div className="bg-[#2a2b38] rounded-xl p-6 animate-fadeIn">
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="objetivo" className="block text-sm font-medium text-gray-400 mb-1">
                        ¿Cuál es mi objetivo principal en estas 12 semanas?
                      </Label>
                      <textarea
                        id="objetivo"
                        value={nuevoObjetivo}
                        onChange={(e) => setNuevoObjetivo(e.target.value)}
                        className="w-full bg-[#21222d] border border-gray-700 rounded-lg p-3 text-white resize-none focus:ring-blue-500 focus:border-blue-500"
                        rows={2}
                        placeholder="Ej: Automatizar mi ingreso con un producto digital y subir mi Vida Rica al 60%"
                      />
                    </div>
                    
                    <div>
                      <Label htmlFor="razon" className="block text-sm font-medium text-gray-400 mb-1">
                        ¿Por qué esto es importante para mí?
                      </Label>
                      <textarea
                        id="razon"
                        value={nuevaRazon}
                        onChange={(e) => setNuevaRazon(e.target.value)}
                        className="w-full bg-[#21222d] border border-gray-700 rounded-lg p-3 text-white resize-none focus:ring-blue-500 focus:border-blue-500"
                        rows={2}
                        placeholder="Ej: Quiero tener mayor libertad financiera y tiempo para mi familia"
                      />
                    </div>
                    
                    <div className="flex justify-end gap-2 pt-2">
                      <button
                        onClick={() => setEditandoObjetivo(false)}
                        className="px-4 py-2 border border-gray-600 rounded-lg text-gray-300 hover:bg-gray-700"
                      >
                        Cancelar
                      </button>
                      <button
                        onClick={guardarObjetivo}
                        className="px-4 py-2 bg-blue-600 hover:bg-blue-500 rounded-lg text-white"
                      >
                        Guardar
                      </button>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="bg-[#2a2b38] rounded-xl p-6">
                  <div className="flex justify-between">
                    <h3 className="text-lg font-semibold mb-4">Mi objetivo para este ciclo:</h3>
                    <button 
                      onClick={() => {
                        setNuevoObjetivo(cicloActual.objetivo);
                        setNuevaRazon(cicloActual.razon);
                        setEditandoObjetivo(true);
                      }}
                      className="text-gray-400 hover:text-white"
                    >
                      <Pencil className="w-4 h-4" />
                    </button>
                  </div>
                  
                  <div className="p-4 bg-gradient-to-r from-[#1e1e2d] to-[#23233a] rounded-lg border border-[#2a2a3d] mb-4">
                    <p className="text-xl font-medium text-blue-300">
                      "{cicloActual.objetivo}"
                    </p>
                  </div>
                  
                  <h3 className="text-lg font-semibold mb-2">Por qué es importante:</h3>
                  <p className="text-gray-300">
                    {cicloActual.razon}
                  </p>
                </div>
              )}
              
              {/* Estadísticas de progreso */}
              <div className="bg-[#2a2b38] rounded-xl p-6">
                <h3 className="text-lg font-semibold mb-4">Progreso del ciclo</h3>
                
                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                  <div className="bg-[#21222d] rounded-lg p-4 text-center">
                    <div className="text-3xl font-bold text-blue-400 mb-1">
                      {estadisticasCiclo.semanasCompletadas}/{estadisticasCiclo.totalSemanas}
                    </div>
                    <div className="text-sm text-gray-400">Semanas completadas</div>
                  </div>
                  
                  <div className="bg-[#21222d] rounded-lg p-4 text-center">
                    <div className="text-3xl font-bold text-green-400 mb-1">
                      {Math.round(estadisticasCiclo.puntuacionPromedio)}%
                    </div>
                    <div className="text-sm text-gray-400">Puntuación promedio</div>
                  </div>
                  
                  <div className="bg-[#21222d] rounded-lg p-4 text-center">
                    <div className="text-3xl font-bold text-yellow-400 mb-1">
                      {estadisticasCiclo.accionesCompletadas}
                    </div>
                    <div className="text-sm text-gray-400">Acciones completadas</div>
                  </div>
                  
                  <div className="bg-[#21222d] rounded-lg p-4 text-center">
                    <div className="text-3xl font-bold text-red-400 mb-1">
                      {estadisticasCiclo.accionesFallidas}
                    </div>
                    <div className="text-sm text-gray-400">Acciones fallidas</div>
                  </div>
                </div>
                
                <div className="mb-6">
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-sm text-gray-400">Progreso general del ciclo</span>
                    <span className="text-sm font-medium">
                      {Math.round((estadisticasCiclo.semanasCompletadas / estadisticasCiclo.totalSemanas) * 100)}%
                    </span>
                  </div>
                  <div className="w-full bg-gray-700 h-3 rounded-full overflow-hidden">
                    <div 
                      className="h-full bg-gradient-to-r from-blue-500 to-purple-500"
                      style={{ 
                        width: `${(estadisticasCiclo.semanasCompletadas / estadisticasCiclo.totalSemanas) * 100}%`,
                        transition: 'width 0.5s ease-in-out'
                      }}
                    ></div>
                  </div>
                </div>
                
                <div className="p-4 bg-[#1e1e2d] rounded-lg border border-[#2a2a3d]">
                  <div className="flex items-start gap-2">
                    <MessageSquare className="w-5 h-5 text-blue-400 flex-shrink-0 mt-0.5" />
                    <p className="text-blue-300">
                      {generarMensajeMotivacion()}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </Card>
      
      {/* SECCIÓN 2 - HITOS SEMANALES */}
      <Card>
        <div className="p-6">
          <div className="flex justify-between items-center mb-4">
            <div className="flex items-center">
              <ListChecks className="h-6 w-6 text-[#96a7ff] mr-2" />
              <h2 className="text-xl font-semibold">Hitos Semanales</h2>
            </div>
            <button 
              onClick={() => toggleSeccion('hitos')}
              className="text-gray-400 hover:text-white transition-colors"
            >
              {seccionExpandida === 'hitos' ? <ChevronUp /> : <ChevronDown />}
            </button>
          </div>
          
          {seccionExpandida === 'hitos' && (
            <div className="animate-fadeIn">
              {/* Semana activa */}
              <div className="mb-6">
                <div className="grid grid-cols-6 gap-2 md:gap-4 mb-4 overflow-x-auto">
                  {cicloActual.semanas.map((semana, index) => (
                    <button
                      key={semana.id}
                      className={`py-2 px-3 rounded-lg transition-colors ${
                        semanaActiva === index 
                          ? 'bg-blue-600 text-white' 
                          : semana.estado === 'completada'
                            ? 'bg-green-900/30 text-green-300 hover:bg-green-900/50'
                            : semana.estado === 'en-progreso'
                              ? 'bg-yellow-900/30 text-yellow-300 hover:bg-yellow-900/50'
                              : 'bg-[#2a2b38] text-gray-300 hover:bg-[#30314a]'
                      }`}
                      onClick={() => setSemanaActiva(index)}
                    >
                      <span className="text-xs">Semana</span>
                      <div className="text-lg font-bold">{semana.numero}</div>
                    </button>
                  ))}
                </div>
                
                {/* Detalles de la semana activa */}
                <div className="bg-[#2a2b38] rounded-xl p-6">
                  <div className="flex flex-col md:flex-row justify-between md:items-center mb-4">
                    <div className="mb-4 md:mb-0">
                      <div className="text-sm text-gray-400 mb-1">Acción clave:</div>
                      <h3 className="text-xl font-semibold">
                        {cicloActual.semanas[semanaActiva].accionClave}
                      </h3>
                    </div>
                    
                    <div className="flex items-center space-x-3">
                      <div className="text-sm text-center">
                        <div className="font-medium mb-1">Puntuación</div>
                        <div 
                          className={`text-xl font-bold ${
                            cicloActual.semanas[semanaActiva].puntuacion >= 80 ? 'text-green-400' :
                            cicloActual.semanas[semanaActiva].puntuacion >= 60 ? 'text-yellow-400' :
                            cicloActual.semanas[semanaActiva].puntuacion > 0 ? 'text-red-400' :
                            'text-gray-500'
                          }`}
                        >
                          {cicloActual.semanas[semanaActiva].puntuacion}%
                        </div>
                      </div>
                      
                      <div className="relative w-16 h-16">
                        <svg className="w-16 h-16 transform -rotate-90">
                          <circle
                            cx="32"
                            cy="32"
                            r="28"
                            fill="none"
                            stroke="#2d2d3d"
                            strokeWidth="8"
                          />
                          <circle
                            cx="32"
                            cy="32"
                            r="28"
                            fill="none"
                            stroke={
                              cicloActual.semanas[semanaActiva].puntuacion >= 80 ? '#10B981' :
                              cicloActual.semanas[semanaActiva].puntuacion >= 60 ? '#FBBF24' :
                              cicloActual.semanas[semanaActiva].puntuacion > 0 ? '#EF4444' :
                              '#374151'
                            }
                            strokeWidth="8"
                            strokeDasharray={`${2 * Math.PI * 28 * cicloActual.semanas[semanaActiva].puntuacion / 100} ${2 * Math.PI * 28}`}
                            strokeLinecap="round"
                          />
                        </svg>
                      </div>
                    </div>
                  </div>
                  
                  {/* Lista de acciones de la semana */}
                  <div className="mt-6">
                    <div className="flex justify-between items-center mb-3">
                      <h4 className="font-medium">Acciones para completar</h4>
                      <button 
                        onClick={() => {
                          setSemanaSeleccionada(semanaActiva);
                          setMostrarModalNuevaAccion(true);
                        }}
                        className="text-blue-400 hover:text-blue-300 flex items-center text-sm"
                      >
                        <PlusCircle className="w-4 h-4 mr-1" />
                        <span>Añadir acción</span>
                      </button>
                    </div>
                    
                    <div className="space-y-2">
                      {cicloActual.semanas[semanaActiva].acciones.map((accion) => (
                        <div 
                          key={accion.id} 
                          className={`p-3 rounded-lg flex items-center justify-between ${
                            accion.estado === 'completada' 
                              ? 'bg-green-900/20 border border-green-900' 
                              : accion.estado === 'fallida'
                                ? 'bg-red-900/20 border border-red-900'
                                : 'bg-[#21222d] border border-gray-700'
                          }`}
                        >
                          <div>
                            <div className="flex items-center gap-2">
                              {accion.estado === 'completada' && (
                                <span className="w-5 h-5 bg-green-500 rounded-full flex items-center justify-center">
                                  <Check className="w-3 h-3 text-white" />
                                </span>
                              )}
                              {accion.estado === 'fallida' && (
                                <span className="w-5 h-5 bg-red-500 rounded-full flex items-center justify-center">
                                  <X className="w-3 h-3 text-white" />
                                </span>
                              )}
                              <span className={accion.estado === 'completada' ? 'line-through text-gray-400' : ''}>
                                {accion.descripcion}
                              </span>
                            </div>
                            {accion.comentario && (
                              <div className="ml-7 text-sm text-gray-400 mt-1">
                                Nota: {accion.comentario}
                              </div>
                            )}
                          </div>
                          
                          <div className="flex gap-1">
                            <button 
                              onClick={() => actualizarEstadoAccion(semanaActiva, accion.id, 'completada')}
                              className={`p-1 rounded ${
                                accion.estado === 'completada' 
                                  ? 'bg-green-500 text-white' 
                                  : 'bg-[#2a2b38] text-gray-400 hover:text-white'
                              }`}
                              disabled={accion.estado === 'completada'}
                            >
                              <Check className="w-4 h-4" />
                            </button>
                            <button 
                              onClick={() => actualizarEstadoAccion(semanaActiva, accion.id, 'fallida')}
                              className={`p-1 rounded ${
                                accion.estado === 'fallida' 
                                  ? 'bg-red-500 text-white' 
                                  : 'bg-[#2a2b38] text-gray-400 hover:text-white'
                              }`}
                              disabled={accion.estado === 'fallida'}
                            >
                              <X className="w-4 h-4" />
                            </button>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                  
                  {/* Botón de reflexión semanal */}
                  <div className="mt-6 flex justify-end">
                    <button
                      onClick={() => abrirModalReflexion(semanaActiva)}
                      className="flex items-center gap-1 text-blue-400 hover:text-blue-300"
                    >
                      <BookText className="w-4 h-4" />
                      <span>
                        {cicloActual.semanas[semanaActiva].reflexion.logros 
                          ? 'Ver reflexión semanal' 
                          : 'Añadir reflexión semanal'}
                      </span>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </Card>
      
      {/* SECCIÓN 3 - DASHBOARD VISUAL */}
      <Card>
        <div className="p-6">
          <div className="flex justify-between items-center mb-4">
            <div className="flex items-center">
              <BarChart className="h-6 w-6 text-[#f9769d] mr-2" />
              <h2 className="text-xl font-semibold">Dashboard Visual</h2>
            </div>
            <button 
              onClick={() => toggleSeccion('dashboard')}
              className="text-gray-400 hover:text-white transition-colors"
            >
              {seccionExpandida === 'dashboard' ? <ChevronUp /> : <ChevronDown />}
            </button>
          </div>
          
          {seccionExpandida === 'dashboard' && (
            <div className="animate-fadeIn">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Gráfico de progreso semanal */}
                <div className="bg-[#2a2b38] rounded-xl p-6">
                  <h3 className="text-lg font-semibold mb-4">Progreso Semanal</h3>
                  
                  <div className="space-y-3">
                    {datosGraficoSemanal.map((dato) => (
                      <div key={dato.semana} className="flex items-center">
                        <div className="w-8 text-gray-400">S{dato.semana}</div>
                        <div className="flex-1 mx-2">
                          <div className="w-full bg-gray-700 h-6 rounded-full overflow-hidden">
                            <div 
                              className="h-full flex items-center pl-2 font-medium text-white"
                              style={{ 
                                width: `${dato.puntuacion}%`,
                                backgroundColor: dato.color,
                                transition: 'width 0.5s ease-in-out'
                              }}
                            >
                              {dato.puntuacion > 0 && (
                                dato.puntuacion >= 20 ? `${dato.puntuacion}%` : ''
                              )}
                            </div>
                          </div>
                        </div>
                        <div className="w-12 text-right">
                          {dato.puntuacion > 0 ? `${dato.puntuacion}%` : '-'}
                        </div>
                      </div>
                    ))}
                  </div>
                  
                  <div className="flex justify-center mt-4 space-x-6">
                    <div className="flex items-center">
                      <div className="w-3 h-3 rounded-full bg-green-500 mr-1"></div>
                      <span className="text-sm text-gray-400">Excelente</span>
                    </div>
                    <div className="flex items-center">
                      <div className="w-3 h-3 rounded-full bg-yellow-500 mr-1"></div>
                      <span className="text-sm text-gray-400">Regular</span>
                    </div>
                    <div className="flex items-center">
                      <div className="w-3 h-3 rounded-full bg-red-500 mr-1"></div>
                      <span className="text-sm text-gray-400">Por mejorar</span>
                    </div>
                  </div>
                </div>
                
                {/* Indicador de Foco */}
                <div className="bg-[#2a2b38] rounded-xl p-6">
                  <h3 className="text-lg font-semibold mb-4">Indicador de Foco</h3>
                  
                  <div className="flex justify-center mb-6">
                    <div className="relative w-48 h-48">
                      <svg className="w-48 h-48" viewBox="0 0 100 100">
                        {/* Fondo gris */}
                        <circle cx="50" cy="50" r="45" fill="#2d2d3d" />
                        
                        {/* Segmento completado */}
                        <circle
                          cx="50"
                          cy="50"
                          r="45"
                          fill="none"
                          stroke="#10B981"
                          strokeWidth="10"
                          strokeDasharray={`${2 * Math.PI * 45 * (estadisticasCiclo.accionesCompletadas / estadisticasCiclo.totalAcciones)} ${2 * Math.PI * 45}`}
                          strokeDashoffset={`${2 * Math.PI * 45 * 0.25}`}
                          strokeLinecap="round"
                          transform="rotate(-90 50 50)"
                        />
                        
                        {/* Segmento pendiente */}
                        <circle
                          cx="50"
                          cy="50"
                          r="45"
                          fill="none"
                          stroke="#FBBF24"
                          strokeWidth="10"
                          strokeDasharray={`${2 * Math.PI * 45 * (estadisticasCiclo.accionesPendientes / estadisticasCiclo.totalAcciones)} ${2 * Math.PI * 45}`}
                          strokeDashoffset={`${2 * Math.PI * 45 * (0.25 - (estadisticasCiclo.accionesCompletadas / estadisticasCiclo.totalAcciones))}`}
                          strokeLinecap="round"
                          transform="rotate(-90 50 50)"
                        />
                        
                        {/* Segmento fallido */}
                        <circle
                          cx="50"
                          cy="50"
                          r="45"
                          fill="none"
                          stroke="#EF4444"
                          strokeWidth="10"
                          strokeDasharray={`${2 * Math.PI * 45 * (estadisticasCiclo.accionesFallidas / estadisticasCiclo.totalAcciones)} ${2 * Math.PI * 45}`}
                          strokeDashoffset={`${2 * Math.PI * 45 * (0.25 - (estadisticasCiclo.accionesCompletadas / estadisticasCiclo.totalAcciones) - (estadisticasCiclo.accionesPendientes / estadisticasCiclo.totalAcciones))}`}
                          strokeLinecap="round"
                          transform="rotate(-90 50 50)"
                        />
                        
                        {/* Texto central */}
                        <text x="50" y="45" textAnchor="middle" fontSize="14" fill="white" fontWeight="bold">
                          {estadisticasCiclo.totalAcciones} tareas
                        </text>
                        <text x="50" y="65" textAnchor="middle" fontSize="10" fill="#cccccc">
                          en este ciclo
                        </text>
                      </svg>
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-3 text-center">
                    <div>
                      <div className="font-bold text-green-400 text-xl">
                        {estadisticasCiclo.accionesCompletadas}
                      </div>
                      <div className="text-sm text-gray-400">Completadas</div>
                    </div>
                    <div>
                      <div className="font-bold text-yellow-400 text-xl">
                        {estadisticasCiclo.accionesPendientes}
                      </div>
                      <div className="text-sm text-gray-400">Pendientes</div>
                    </div>
                    <div>
                      <div className="font-bold text-red-400 text-xl">
                        {estadisticasCiclo.accionesFallidas}
                      </div>
                      <div className="text-sm text-gray-400">Fallidas</div>
                    </div>
                  </div>
                </div>
              </div>
              
              {/* Tarjeta de motivación */}
              <div className="mt-6 bg-[#2a2b38] rounded-xl p-6">
                <div className="flex items-start gap-4">
                  <div className="bg-[#1e1e2d] p-4 rounded-full">
                    <Medal className="w-8 h-8 text-[#f9769d]" />
                  </div>
                  
                  <div>
                    <h3 className="text-lg font-semibold mb-2">Motivación automática</h3>
                    <div className="p-4 bg-[#1e1e2d] rounded-lg border border-[#2a2a3d]">
                      <p className="text-blue-300 font-medium">
                        {cicloActual.semanas[2].estado === 'en-progreso' ? (
                          <>
                            ¡Estás en tu semana 3 y llevas {estadisticasCiclo.puntuacionPromedio.toFixed(0)}%! {estadisticasCiclo.puntuacionPromedio > 70 ? '¡No bajes el ritmo!' : '¡Reenfoca tus esfuerzos!'}
                          </>
                        ) : estadisticasCiclo.semanasCompletadas >= 6 ? (
                          <>
                            ¡Ya has completado {estadisticasCiclo.semanasCompletadas} semanas! Solo te quedan {12 - estadisticasCiclo.semanasCompletadas} más para terminar tu ciclo.
                          </>
                        ) : estadisticasCiclo.semanasCompletadas >= 1 ? (
                          <>
                            Has completado {estadisticasCiclo.semanasCompletadas} semanas con un promedio de {estadisticasCiclo.puntuacionPromedio.toFixed(0)}%. ¡Sigue así!
                          </>
                        ) : (
                          <>¡Es hora de empezar! Define tus acciones para esta primera semana.</>
                        )}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
              
              {/* Alerta de foco (condicional) */}
              {alertaFoco && (
                <div className="mt-6 bg-red-900/30 border border-red-800 rounded-xl p-6 animate-fadeIn">
                  <div className="flex items-start gap-4">
                    <div className="bg-red-900/50 p-3 rounded-full">
                      <AlertTriangle className="w-6 h-6 text-red-400" />
                    </div>
                    
                    <div>
                      <h3 className="text-lg font-semibold mb-2 text-red-300">¡Alerta de pérdida de foco!</h3>
                      <p className="text-red-300">
                        Carlos, parece que estás perdiendo impulso. Has fallado 3 o más acciones durante dos semanas consecutivas. ¿Revisamos tus prioridades?
                      </p>
                      
                      <div className="mt-3 flex gap-3">
                        <button className="px-4 py-2 bg-red-700 hover:bg-red-600 text-white rounded-lg text-sm font-medium">
                          Revisar prioridades
                        </button>
                        <button className="px-4 py-2 bg-transparent border border-red-700 text-red-400 hover:bg-red-900/20 rounded-lg text-sm font-medium">
                          Descartar
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </Card>
      
      {/* SECCIÓN 4 - VINCULACIÓN CON VIDA RICA */}
      <Card>
        <div className="p-6">
          <div className="flex justify-between items-center mb-4">
            <div className="flex items-center">
              <Heart className="h-6 w-6 text-red-400 mr-2" />
              <h2 className="text-xl font-semibold">Vinculación con Vida Rica</h2>
            </div>
            <button 
              onClick={() => toggleSeccion('vida-rica')}
              className="text-gray-400 hover:text-white transition-colors"
            >
              {seccionExpandida === 'vida-rica' ? <ChevronUp /> : <ChevronDown />}
            </button>
          </div>
          
          {seccionExpandida === 'vida-rica' && (
            <div className="animate-fadeIn">
              <div className="bg-[#2a2b38] rounded-xl p-6">
                <div className="flex justify-between items-center mb-6">
                  <div>
                    <h3 className="text-lg font-semibold mb-1">Impacto en tu Vida Rica</h3>
                    <p className="text-sm text-gray-400">
                      Este ciclo está diseñado para impactar positivamente tu definición de una vida rica.
                    </p>
                  </div>
                  
                  <Link 
                    to="/vida-rica" 
                    className="flex items-center text-blue-400 hover:text-blue-300"
                  >
                    <span>Ver Vida Rica</span>
                    <ExternalLink className="w-4 h-4 ml-1" />
                  </Link>
                </div>
                
                <div className="flex flex-col md:flex-row gap-6">
                  <div className="flex-1 p-5 bg-[#21222d] rounded-xl">
                    <div className="flex justify-between items-center mb-4">
                      <h4 className="font-medium">Al inicio del ciclo</h4>
                      <div className="bg-blue-900/30 text-blue-300 px-2 py-1 rounded text-sm font-medium">
                        {cicloActual.vidaRicaInicial}%
                      </div>
                    </div>
                    
                    <div className="w-full bg-gray-700 h-3 rounded-full overflow-hidden">
                      <div 
                        className="h-full bg-blue-500"
                        style={{ width: `${cicloActual.vidaRicaInicial}%` }}
                      ></div>
                    </div>
                  </div>
                  
                  <div className="flex-1 p-5 bg-[#21222d] rounded-xl">
                    <div className="flex justify-between items-center mb-4">
                      <h4 className="font-medium">Actualmente</h4>
                      <div className="bg-green-900/30 text-green-300 px-2 py-1 rounded text-sm font-medium">
                        51%
                      </div>
                    </div>
                    
                    <div className="w-full bg-gray-700 h-3 rounded-full overflow-hidden">
                      <div 
                        className="h-full bg-green-500"
                        style={{ width: `51%` }}
                      ></div>
                    </div>
                  </div>
                </div>
                
                <div className="mt-6 p-4 bg-[#1e1e2d] rounded-lg border border-[#2a2a3d]">
                  <div className="flex items-start gap-2">
                    <Sparkles className="w-5 h-5 text-[#f9769d] flex-shrink-0 mt-0.5" />
                    <p className="text-blue-300">
                      <span className="font-medium">¡Has aumentado un 6% tu alineación con Vida Rica!</span> Tu enfoque en automatizar ingresos está teniendo un impacto positivo en tus metas de largo plazo.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </Card>
      
      {/* SECCIÓN 6 - CIERRE DEL CICLO */}
      {cicloActual.semanas[11].estado === 'completada' && (
        <Card>
          <div className="p-6">
            <div className="flex justify-between items-center mb-4">
              <div className="flex items-center">
                <Trophy className="h-6 w-6 text-yellow-400 mr-2" />
                <h2 className="text-xl font-semibold">Cierre del Ciclo</h2>
              </div>
            </div>
            
            <div className="bg-[#2a2b38] rounded-xl p-6">
              <h3 className="text-lg font-semibold mb-6 text-center">¡Felicitaciones! Has completado tu ciclo de 12 semanas</h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div className="p-5 bg-[#21222d] rounded-xl">
                  <h4 className="font-medium mb-3">¿Lograste tu objetivo?</h4>
                  <div className="space-y-3">
                    <button className="w-full flex items-center justify-between p-3 bg-green-900/20 border border-green-900 rounded-lg hover:bg-green-900/30">
                      <span>Sí, completamente</span>
                      <Check className="w-5 h-5 text-green-500" />
                    </button>
                    <button className="w-full flex items-center justify-between p-3 bg-[#2a2b38] border border-gray-700 rounded-lg hover:bg-[#30314a]">
                      <span>Parcialmente</span>
                      <Check className="w-5 h-5 text-gray-600" />
                    </button>
                    <button className="w-full flex items-center justify-between p-3 bg-[#2a2b38] border border-gray-700 rounded-lg hover:bg-[#30314a]">
                      <span>No lo logré</span>
                      <Check className="w-5 h-5 text-gray-600" />
                    </button>
                  </div>
                </div>
                
                <div className="p-5 bg-[#21222d] rounded-xl">
                  <h4 className="font-medium mb-3">¿Qué aprendiste?</h4>
                  <textarea
                    placeholder="Comparte tus reflexiones..."
                    className="w-full bg-[#2a2b38] border border-gray-700 rounded-lg p-3 text-white resize-none focus:ring-blue-500 focus:border-blue-500"
                    rows={4}
                  />
                </div>
              </div>
              
              <div className="flex flex-col md:flex-row justify-center gap-4 mt-8">
                <button className="flex items-center justify-center gap-2 px-6 py-3 bg-blue-600 hover:bg-blue-500 text-white rounded-lg font-medium">
                  <FileDown className="w-5 h-5" />
                  Generar Informe de 12 Semanas (PDF)
                </button>
                
                <button className="flex items-center justify-center gap-2 px-6 py-3 bg-[#f9769d] hover:bg-[#fa85a8] text-white rounded-lg font-medium">
                  <RefreshCw className="w-5 h-5" />
                  Iniciar nuevo ciclo
                </button>
              </div>
            </div>
          </div>
        </Card>
      )}
      
      {/* Modal para nueva acción */}
      {mostrarModalNuevaAccion && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-[#21222d] rounded-xl w-full max-w-md shadow-xl animate-fadeIn">
            <div className="p-6">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-semibold">Nueva acción para Semana {cicloActual.semanas[semanaSeleccionada!].numero}</h3>
                <button 
                  onClick={() => setMostrarModalNuevaAccion(false)}
                  className="text-gray-500 hover:text-white"
                >
                  <X className="w-5 h-5" />
                </button>
              </div>
              
              <div className="mb-4">
                <Label htmlFor="nuevaAccion" className="block text-sm font-medium text-gray-400 mb-1">
                  Descripción de la acción
                </Label>
                <textarea
                  id="nuevaAccion"
                  value={nuevaAccion}
                  onChange={(e) => setNuevaAccion(e.target.value)}
                  className="w-full bg-[#2a2b38] border border-gray-700 rounded-lg p-3 text-white resize-none focus:ring-blue-500 focus:border-blue-500"
                  rows={3}
                  placeholder="Describe la acción a realizar esta semana"
                />
              </div>
              
              <div className="flex justify-end gap-2">
                <button
                  onClick={() => setMostrarModalNuevaAccion(false)}
                  className="px-4 py-2 border border-gray-600 rounded-lg text-gray-300 hover:bg-gray-700"
                >
                  Cancelar
                </button>
                <button
                  onClick={agregarAccion}
                  className="px-4 py-2 bg-blue-600 hover:bg-blue-500 rounded-lg text-white disabled:opacity-50 disabled:cursor-not-allowed"
                  disabled={!nuevaAccion.trim()}
                >
                  Guardar
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
      
      {/* Modal para reflexión semanal */}
      {mostrarModalReflexion && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-[#21222d] rounded-xl w-full max-w-lg shadow-xl animate-fadeIn">
            <div className="p-6">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-semibold">Reflexión Semanal #{cicloActual.semanas[semanaSeleccionada!].numero}</h3>
                <button 
                  onClick={() => setMostrarModalReflexion(false)}
                  className="text-gray-500 hover:text-white"
                >
                  <X className="w-5 h-5" />
                </button>
              </div>
              
              <div className="space-y-4 mb-4">
                <div>
                  <Label htmlFor="logros" className="block text-sm font-medium text-gray-400 mb-1">
                    ¿Qué hice bien esta semana?
                  </Label>
                  <textarea
                    id="logros"
                    value={reflexionTemporal.logros}
                    onChange={(e) => setReflexionTemporal({...reflexionTemporal, logros: e.target.value})}
                    className="w-full bg-[#2a2b38] border border-gray-700 rounded-lg p-3 text-white resize-none focus:ring-blue-500 focus:border-blue-500"
                    rows={2}
                    placeholder="Logros, acciones positivas, hábitos que funcionaron..."
                  />
                </div>
                
                <div>
                  <Label htmlFor="ajustes" className="block text-sm font-medium text-gray-400 mb-1">
                    ¿Qué necesito ajustar?
                  </Label>
                  <textarea
                    id="ajustes"
                    value={reflexionTemporal.ajustes}
                    onChange={(e) => setReflexionTemporal({...reflexionTemporal, ajustes: e.target.value})}
                    className="w-full bg-[#2a2b38] border border-gray-700 rounded-lg p-3 text-white resize-none focus:ring-blue-500 focus:border-blue-500"
                    rows={2}
                    placeholder="Mejoras, ajustes, cambios para la próxima semana..."
                  />
                </div>
                
                <div>
                  <Label htmlFor="perdidaFoco" className="block text-sm font-medium text-gray-400 mb-1">
                    ¿Dónde perdí foco?
                  </Label>
                  <textarea
                    id="perdidaFoco"
                    value={reflexionTemporal.perdidaFoco}
                    onChange={(e) => setReflexionTemporal({...reflexionTemporal, perdidaFoco: e.target.value})}
                    className="w-full bg-[#2a2b38] border border-gray-700 rounded-lg p-3 text-white resize-none focus:ring-blue-500 focus:border-blue-500"
                    rows={2}
                    placeholder="Distracciones, obstáculos, procrastinación..."
                  />
                </div>
              </div>
              
              <div className="flex justify-end gap-2">
                <button
                  onClick={() => setMostrarModalReflexion(false)}
                  className="px-4 py-2 border border-gray-600 rounded-lg text-gray-300 hover:bg-gray-700"
                >
                  Cancelar
                </button>
                <button
                  onClick={guardarReflexion}
                  className="px-4 py-2 bg-blue-600 hover:bg-blue-500 rounded-lg text-white"
                >
                  Guardar reflexión
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}