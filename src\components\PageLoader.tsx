import React from 'react';
import { useTheme } from '../lib/ThemeContext';
import { LoadingSpinner } from './LoadingSpinner';

export function PageLoader() {
  const { theme } = useTheme();
  
  return (
    <div className={`min-h-screen flex items-center justify-center ${
      theme === 'dark' ? 'bg-[#0f0f23]' : 'bg-gray-50'
    }`}>
      <div className="text-center space-y-4">
        <LoadingSpinner size="lg" />
        <p className={`text-sm ${
          theme === 'dark' ? 'text-gray-400' : 'text-gray-600'
        }`}>
          Cargando página...
        </p>
      </div>
    </div>
  );
}
