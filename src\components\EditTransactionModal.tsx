import React, { useState, useEffect } from 'react';
import { X, Save, Calendar, DollarSign, Tag, FileText } from 'lucide-react';
import { useTheme } from '../lib/ThemeContext';
import { useTransactionContext } from '../lib/TransactionContext';
import { Transaction } from '../lib/types';
import { useToast } from './Toast';
import { useCustomCategories } from '../hooks/useCustomCategories';

const categoryIcons = {
  // Ingresos
  salario: '💼',
  freelance: '💻',
  intereses: '💹',
  dividendos: '📊',
  
  // Gastos
  vivienda: '🏠',
  alimentacion: '🍎',
  transporte: '🚗',
  entretenimiento: '🎬',
  servicios: '📱',
  salud: '🩺',
  educacion: '📚',
  compras: '🛍️',
  suscripciones: '📺',
  
  // Deudas
  tarjeta: '💳',
  prestamo: '🏦',
  
  // Ahorros
  emergencia: '🧰',
  metasCP: '🎯',
  
  // Inversiones
  acciones: '📈',
  cripto: '₿',
  fondos: '💹'
};

interface EditTransactionModalProps {
  transaction: Transaction;
  isOpen: boolean;
  onClose: () => void;
  onSave: (updatedTransaction: Transaction) => void;
}

export function EditTransactionModal({ transaction, isOpen, onClose, onSave }: EditTransactionModalProps) {
  const { theme } = useTheme();
  const { showSuccess, showError } = useToast();
  const { getCategoriesForType, getCategoryIcon } = useCustomCategories();
  
  // Form state
  const [formData, setFormData] = useState({
    description: transaction.description,
    amount: transaction.amount.toString(),
    category: transaction.category,
    date: transaction.date,
    type: transaction.type
  });
  
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});
  const [isLoading, setIsLoading] = useState(false);

  // Reset form when transaction changes
  useEffect(() => {
    setFormData({
      description: transaction.description,
      amount: transaction.amount.toString(),
      category: transaction.category,
      date: transaction.date,
      type: transaction.type
    });
    setValidationErrors({});
  }, [transaction]);

  // Validation functions
  const validateAmount = (amount: string): number | null => {
    const numAmount = parseFloat(amount.replace(/[,$]/g, ''));
    if (isNaN(numAmount) || numAmount <= 0) return null;
    return numAmount;
  };

  const validateForm = () => {
    const errors: Record<string, string> = {};
    
    if (!formData.description.trim()) {
      errors.description = 'La descripción es requerida';
    }
    
    if (!formData.category) {
      errors.category = 'La categoría es requerida';
    }
    
    const validAmount = validateAmount(formData.amount);
    if (validAmount === null) {
      errors.amount = 'El monto debe ser un número positivo';
    }
    
    if (!formData.date) {
      errors.date = 'La fecha es requerida';
    }
    
    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      showError('Error de validación', 'Por favor corrige los errores en el formulario');
      return;
    }

    setIsLoading(true);
    
    try {
      const validAmount = validateAmount(formData.amount);
      if (validAmount === null) {
        throw new Error('Monto inválido');
      }

      const updatedTransaction: Transaction = {
        ...transaction,
        description: formData.description.trim(),
        amount: validAmount,
        category: formData.category,
        date: formData.date,
        type: formData.type as 'income' | 'expense',
        icon: getCategoryIcon(formData.category) || (formData.type === 'income' ? '💰' : '💸')
      };

      onSave(updatedTransaction);
      showSuccess('Transacción actualizada', 'Los cambios se guardaron correctamente');
      onClose();
    } catch (error) {
      console.error('Error updating transaction:', error);
      showError('Error al actualizar', 'No se pudo guardar la transacción');
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear validation error when user starts typing
    if (validationErrors[field]) {
      setValidationErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className={`rounded-xl max-w-md w-full max-h-[90vh] overflow-y-auto transition-colors duration-200 ${
        theme === 'dark' 
          ? 'bg-[#21222d] border border-[#2a2a3d]' 
          : 'bg-white border border-slate-200 shadow-xl'
      }`}>
        {/* Header */}
        <div className="flex justify-between items-center p-6 border-b border-gray-600">
          <h2 className={`text-xl font-bold transition-colors duration-200 ${
            theme === 'dark' ? 'text-white' : 'text-slate-900'
          }`}>
            Editar Transacción
          </h2>
          <button
            onClick={onClose}
            className={`p-1 rounded-lg transition-colors duration-200 ${
              theme === 'dark'
                ? 'text-gray-400 hover:text-white hover:bg-gray-700'
                : 'text-slate-400 hover:text-slate-600 hover:bg-slate-100'
            }`}
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6 space-y-4">
          {/* Transaction Type */}
          <div>
            <label className={`block text-sm font-medium mb-2 transition-colors duration-200 ${
              theme === 'dark' ? 'text-gray-300' : 'text-slate-700'
            }`}>
              Tipo de Transacción
            </label>
            <div className="flex gap-2">
              <button
                type="button"
                onClick={() => handleInputChange('type', 'income')}
                className={`flex-1 py-2 px-4 rounded-lg border transition-colors duration-200 ${
                  formData.type === 'income'
                    ? 'bg-green-500 text-white border-green-500'
                    : theme === 'dark'
                      ? 'bg-[#2a2b38] text-gray-300 border-gray-600 hover:border-green-500'
                      : 'bg-slate-50 text-slate-700 border-slate-300 hover:border-green-500'
                }`}
              >
                💰 Ingreso
              </button>
              <button
                type="button"
                onClick={() => handleInputChange('type', 'expense')}
                className={`flex-1 py-2 px-4 rounded-lg border transition-colors duration-200 ${
                  formData.type === 'expense'
                    ? 'bg-red-500 text-white border-red-500'
                    : theme === 'dark'
                      ? 'bg-[#2a2b38] text-gray-300 border-gray-600 hover:border-red-500'
                      : 'bg-slate-50 text-slate-700 border-slate-300 hover:border-red-500'
                }`}
              >
                💸 Gasto
              </button>
            </div>
          </div>

          {/* Description */}
          <div>
            <label className={`block text-sm font-medium mb-2 transition-colors duration-200 ${
              theme === 'dark' ? 'text-gray-300' : 'text-slate-700'
            }`}>
              <FileText className="w-4 h-4 inline mr-1" />
              Descripción
            </label>
            <input
              type="text"
              value={formData.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              className={`w-full px-3 py-2 rounded-lg border transition-colors duration-200 ${
                validationErrors.description
                  ? 'border-red-500'
                  : theme === 'dark'
                    ? 'bg-[#2a2b38] border-gray-600 text-white placeholder-gray-400'
                    : 'bg-white border-slate-300 text-slate-900 placeholder-slate-400'
              }`}
              placeholder="Ej. Compra en supermercado"
            />
            {validationErrors.description && (
              <p className="text-red-500 text-sm mt-1">{validationErrors.description}</p>
            )}
          </div>

          {/* Amount */}
          <div>
            <label className={`block text-sm font-medium mb-2 transition-colors duration-200 ${
              theme === 'dark' ? 'text-gray-300' : 'text-slate-700'
            }`}>
              <DollarSign className="w-4 h-4 inline mr-1" />
              Monto
            </label>
            <input
              type="number"
              step="0.01"
              value={formData.amount}
              onChange={(e) => handleInputChange('amount', e.target.value)}
              className={`w-full px-3 py-2 rounded-lg border transition-colors duration-200 ${
                validationErrors.amount
                  ? 'border-red-500'
                  : theme === 'dark'
                    ? 'bg-[#2a2b38] border-gray-600 text-white placeholder-gray-400'
                    : 'bg-white border-slate-300 text-slate-900 placeholder-slate-400'
              }`}
              placeholder="0.00"
            />
            {validationErrors.amount && (
              <p className="text-red-500 text-sm mt-1">{validationErrors.amount}</p>
            )}
          </div>

          {/* Category */}
          <div>
            <label className={`block text-sm font-medium mb-2 transition-colors duration-200 ${
              theme === 'dark' ? 'text-gray-300' : 'text-slate-700'
            }`}>
              <Tag className="w-4 h-4 inline mr-1" />
              Categoría
            </label>
            <select
              value={formData.category}
              onChange={(e) => handleInputChange('category', e.target.value)}
              className={`w-full px-3 py-2 rounded-lg border transition-colors duration-200 ${
                validationErrors.category
                  ? 'border-red-500'
                  : theme === 'dark'
                    ? 'bg-[#2a2b38] border-gray-600 text-white'
                    : 'bg-white border-slate-300 text-slate-900'
              }`}
            >
              <option value="">Seleccionar categoría</option>
              {getCategoriesForType(formData.type).map(cat => (
                <option key={cat.key} value={cat.key}>
                  {cat.icon} {cat.name}
                </option>
              ))}
            </select>
            {validationErrors.category && (
              <p className="text-red-500 text-sm mt-1">{validationErrors.category}</p>
            )}
          </div>

          {/* Date */}
          <div>
            <label className={`block text-sm font-medium mb-2 transition-colors duration-200 ${
              theme === 'dark' ? 'text-gray-300' : 'text-slate-700'
            }`}>
              <Calendar className="w-4 h-4 inline mr-1" />
              Fecha
            </label>
            <input
              type="date"
              value={formData.date}
              onChange={(e) => handleInputChange('date', e.target.value)}
              className={`w-full px-3 py-2 rounded-lg border transition-colors duration-200 ${
                validationErrors.date
                  ? 'border-red-500'
                  : theme === 'dark'
                    ? 'bg-[#2a2b38] border-gray-600 text-white'
                    : 'bg-white border-slate-300 text-slate-900'
              }`}
            />
            {validationErrors.date && (
              <p className="text-red-500 text-sm mt-1">{validationErrors.date}</p>
            )}
          </div>

          {/* Actions */}
          <div className="flex gap-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className={`flex-1 py-2 px-4 rounded-lg border transition-colors duration-200 ${
                theme === 'dark'
                  ? 'border-gray-600 text-gray-300 hover:bg-gray-700'
                  : 'border-slate-300 text-slate-700 hover:bg-slate-50'
              }`}
            >
              Cancelar
            </button>
            <button
              type="submit"
              disabled={isLoading}
              className={`flex-1 py-2 px-4 rounded-lg transition-colors duration-200 flex items-center justify-center gap-2 ${
                isLoading
                  ? 'bg-gray-500 cursor-not-allowed'
                  : 'bg-blue-500 hover:bg-blue-600'
              } text-white`}
            >
              {isLoading ? (
                <>
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                  Guardando...
                </>
              ) : (
                <>
                  <Save className="w-4 h-4" />
                  Guardar
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
