import React, { useState, useEffect } from 'react';
import { useAutoBackup } from '../hooks/useAutoBackup';
import { useTheme } from '../lib/ThemeContext';
import { 
  Cloud, 
  Download, 
  Upload, 
  RefreshCw, 
  Clock, 
  CheckCircle, 
  AlertCircle,
  Settings,
  History,
  X
} from 'lucide-react';
import { Card } from './ui/Card';

interface BackupManagerProps {
  isOpen: boolean;
  onClose: () => void;
}

export function BackupManager({ isOpen, onClose }: BackupManagerProps) {
  const { theme } = useTheme();
  const {
    manualBackup,
    restoreFromBackup,
    getBackupHistory,
    toggleAutoBackup,
    setBackupFrequency,
    getBackupStatus,
    clearBackupError
  } = useAutoBackup();

  const [backupHistory, setBackupHistory] = useState<any[]>([]);
  const [isRestoring, setIsRestoring] = useState(false);
  const [showHistory, setShowHistory] = useState(false);
  const [showSettings, setShowSettings] = useState(false);

  const status = getBackupStatus();

  useEffect(() => {
    if (isOpen) {
      loadBackupHistory();
    }
  }, [isOpen]);

  const loadBackupHistory = async () => {
    const history = await getBackupHistory();
    setBackupHistory(history);
  };

  const handleManualBackup = async () => {
    const success = await manualBackup();
    if (success) {
      await loadBackupHistory();
    }
  };

  const handleRestore = async (backupId?: string) => {
    if (!confirm('¿Estás seguro de que quieres restaurar este backup? Esto sobrescribirá tus datos actuales.')) {
      return;
    }

    setIsRestoring(true);
    const success = await restoreFromBackup(backupId);
    setIsRestoring(false);

    if (success) {
      alert('Backup restaurado exitosamente. La página se recargará.');
      window.location.reload();
    } else {
      alert('Error al restaurar el backup.');
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('es-ES', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getTimeSinceLastBackup = () => {
    if (!status.lastBackupDate) return 'Nunca';
    
    const now = new Date();
    const diff = now.getTime() - status.lastBackupDate.getTime();
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const days = Math.floor(hours / 24);
    
    if (days > 0) return `Hace ${days} día${days > 1 ? 's' : ''}`;
    if (hours > 0) return `Hace ${hours} hora${hours > 1 ? 's' : ''}`;
    return 'Hace menos de 1 hora';
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className={`w-full max-w-2xl rounded-lg shadow-xl ${
        theme === 'dark' ? 'bg-gray-800' : 'bg-white'
      }`}>
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center gap-3">
            <Cloud className="w-6 h-6 text-blue-500" />
            <h2 className="text-xl font-semibold">Gestión de Backups</h2>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        <div className="p-6 space-y-6">
          {/* Status Card */}
          <Card className="p-4">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium">Estado del Backup</h3>
              <div className="flex items-center gap-2">
                {status.isBackingUp ? (
                  <RefreshCw className="w-4 h-4 animate-spin text-blue-500" />
                ) : status.needsBackup ? (
                  <AlertCircle className="w-4 h-4 text-yellow-500" />
                ) : (
                  <CheckCircle className="w-4 h-4 text-green-500" />
                )}
                <span className={`text-sm ${
                  status.isBackingUp ? 'text-blue-500' :
                  status.needsBackup ? 'text-yellow-500' : 'text-green-500'
                }`}>
                  {status.isBackingUp ? 'Creando backup...' :
                   status.needsBackup ? 'Backup recomendado' : 'Actualizado'}
                </span>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-500">Último backup:</span>
                <div className="font-medium">{getTimeSinceLastBackup()}</div>
              </div>
              <div>
                <span className="text-gray-500">Auto backup:</span>
                <div className="font-medium">
                  {status.autoBackupEnabled ? `Activo (${status.backupFrequency})` : 'Desactivado'}
                </div>
              </div>
            </div>

            {status.backupError && (
              <div className="mt-4 p-3 bg-red-100 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <AlertCircle className="w-4 h-4 text-red-500" />
                    <span className="text-sm text-red-700 dark:text-red-300">
                      Error: {status.backupError}
                    </span>
                  </div>
                  <button
                    onClick={clearBackupError}
                    className="text-red-500 hover:text-red-700"
                  >
                    <X className="w-4 h-4" />
                  </button>
                </div>
              </div>
            )}
          </Card>

          {/* Actions */}
          <div className="flex gap-3">
            <button
              onClick={handleManualBackup}
              disabled={status.isBackingUp}
              className="flex items-center gap-2 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50"
            >
              {status.isBackingUp ? (
                <RefreshCw className="w-4 h-4 animate-spin" />
              ) : (
                <Upload className="w-4 h-4" />
              )}
              Crear Backup
            </button>

            <button
              onClick={() => handleRestore()}
              disabled={isRestoring || backupHistory.length === 0}
              className="flex items-center gap-2 px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 disabled:opacity-50"
            >
              {isRestoring ? (
                <RefreshCw className="w-4 h-4 animate-spin" />
              ) : (
                <Download className="w-4 h-4" />
              )}
              Restaurar Último
            </button>

            <button
              onClick={() => setShowHistory(!showHistory)}
              className="flex items-center gap-2 px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600"
            >
              <History className="w-4 h-4" />
              Historial
            </button>

            <button
              onClick={() => setShowSettings(!showSettings)}
              className="flex items-center gap-2 px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600"
            >
              <Settings className="w-4 h-4" />
              Configuración
            </button>
          </div>

          {/* Settings Panel */}
          {showSettings && (
            <Card className="p-4">
              <h3 className="text-lg font-medium mb-4">Configuración de Backup</h3>
              
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span>Backup automático</span>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      checked={status.autoBackupEnabled}
                      onChange={(e) => toggleAutoBackup(e.target.checked)}
                      className="sr-only peer"
                    />
                    <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                  </label>
                </div>

                {status.autoBackupEnabled && (
                  <div>
                    <span className="block mb-2">Frecuencia</span>
                    <select
                      value={status.backupFrequency}
                      onChange={(e) => setBackupFrequency(e.target.value as any)}
                      className={`w-full p-2 border rounded-lg ${
                        theme === 'dark' 
                          ? 'bg-gray-700 border-gray-600 text-white' 
                          : 'bg-white border-gray-300'
                      }`}
                    >
                      <option value="daily">Diario</option>
                      <option value="weekly">Semanal</option>
                      <option value="monthly">Mensual</option>
                    </select>
                  </div>
                )}
              </div>
            </Card>
          )}

          {/* History Panel */}
          {showHistory && (
            <Card className="p-4">
              <h3 className="text-lg font-medium mb-4">Historial de Backups</h3>
              
              {backupHistory.length === 0 ? (
                <p className="text-gray-500 text-center py-4">No hay backups disponibles</p>
              ) : (
                <div className="space-y-2 max-h-60 overflow-y-auto">
                  {backupHistory.map((backup) => (
                    <div
                      key={backup.id}
                      className={`flex items-center justify-between p-3 rounded-lg border ${
                        theme === 'dark' 
                          ? 'border-gray-600 bg-gray-700' 
                          : 'border-gray-200 bg-gray-50'
                      }`}
                    >
                      <div>
                        <div className="font-medium">
                          {backup.backup_type === 'manual' ? 'Manual' : 'Automático'}
                        </div>
                        <div className="text-sm text-gray-500">
                          {formatDate(backup.created_at)}
                        </div>
                      </div>
                      <button
                        onClick={() => handleRestore(backup.id)}
                        disabled={isRestoring}
                        className="px-3 py-1 text-sm bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"
                      >
                        Restaurar
                      </button>
                    </div>
                  ))}
                </div>
              )}
            </Card>
          )}
        </div>
      </div>
    </div>
  );
}
