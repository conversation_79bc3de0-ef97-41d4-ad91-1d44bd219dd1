import { useState, useEffect } from 'react';
import { CustomCategory } from '../components/CategoryManager';

const defaultCategories = {
  income: [
    { key: 'salario', name: '<PERSON><PERSON>', icon: '💼' },
    { key: 'freelance', name: 'Freelance', icon: '💻' },
    { key: 'intereses', name: 'Interese<PERSON>', icon: '💹' },
    { key: 'dividendos', name: 'Dividendos', icon: '📊' },
  ],
  expense: [
    { key: 'vivienda', name: 'Vivienda', icon: '🏠' },
    { key: 'alimentacion', name: 'Alimentación', icon: '🍎' },
    { key: 'transporte', name: 'Transporte', icon: '🚗' },
    { key: 'entretenimiento', name: 'Entretenimiento', icon: '🎬' },
    { key: 'servicios', name: 'Servic<PERSON>', icon: '📱' },
    { key: 'salud', name: 'Salud', icon: '🩺' },
    { key: 'educacion', name: 'Educación', icon: '📚' },
    { key: 'compras', name: 'Co<PERSON><PERSON>', icon: '🛍️' },
    { key: 'suscripciones', name: 'Sus<PERSON>ripcion<PERSON>', icon: '📺' },
  ]
};

export interface CategoryOption {
  key: string;
  name: string;
  icon: string;
  isCustom?: boolean;
  color?: string;
}

export function useCustomCategories() {
  const [customCategories, setCustomCategories] = useState<CustomCategory[]>([]);
  const [loading, setLoading] = useState(false);

  // Load custom categories from localStorage
  useEffect(() => {
    loadCustomCategories();
  }, []);

  const loadCustomCategories = () => {
    try {
      setLoading(true);
      const saved = localStorage.getItem('customCategories');
      if (saved) {
        setCustomCategories(JSON.parse(saved));
      }
    } catch (error) {
      console.error('Error loading custom categories:', error);
    } finally {
      setLoading(false);
    }
  };

  // Get all categories (default + custom) for a specific type
  const getCategoriesForType = (type: 'income' | 'expense'): CategoryOption[] => {
    const defaults = defaultCategories[type].map(cat => ({
      key: cat.key,
      name: cat.name,
      icon: cat.icon,
      isCustom: false
    }));

    const customs = customCategories
      .filter(cat => cat.type === type)
      .map(cat => ({
        key: cat.id,
        name: cat.name,
        icon: cat.icon,
        isCustom: true,
        color: cat.color
      }));

    return [...defaults, ...customs];
  };

  // Get category info by key
  const getCategoryInfo = (categoryKey: string): CategoryOption | null => {
    // Check default categories first
    for (const type of ['income', 'expense'] as const) {
      const defaultCat = defaultCategories[type].find(cat => cat.key === categoryKey);
      if (defaultCat) {
        return {
          key: defaultCat.key,
          name: defaultCat.name,
          icon: defaultCat.icon,
          isCustom: false
        };
      }
    }

    // Check custom categories
    const customCat = customCategories.find(cat => cat.id === categoryKey);
    if (customCat) {
      return {
        key: customCat.id,
        name: customCat.name,
        icon: customCat.icon,
        isCustom: true,
        color: customCat.color
      };
    }

    return null;
  };

  // Get icon for a category
  const getCategoryIcon = (categoryKey: string): string => {
    const categoryInfo = getCategoryInfo(categoryKey);
    return categoryInfo?.icon || '💸';
  };

  // Check if a category exists
  const categoryExists = (categoryKey: string): boolean => {
    return getCategoryInfo(categoryKey) !== null;
  };

  // Refresh custom categories (useful after adding/editing/deleting)
  const refreshCategories = () => {
    loadCustomCategories();
  };

  return {
    customCategories,
    loading,
    getCategoriesForType,
    getCategoryInfo,
    getCategoryIcon,
    categoryExists,
    refreshCategories
  };
}
