import { <PERSON><PERSON>hart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Legend, TooltipProps } from 'recharts';
import { ValueType, NameType } from 'recharts/types/component/DefaultTooltipContent';
import type { Mes } from '../lib/types';
import { useTheme } from '../lib/ThemeContext';
import { useTransactionContext } from '../lib/TransactionContext';
import { calculateFinancialMetrics } from '../lib/calculations';

interface EstimadoRealChartProps {
  mes: Mes;
}

export function EstimadoRealChart({ mes }: EstimadoRealChartProps) {
  const { theme } = useTheme();
  const { transactions } = useTransactionContext();
  const metrics = calculateFinancialMetrics(transactions, mes);
  
  // Calcular datos basados en transacciones reales
  const gastosEsenciales = metrics.gastosPorCategoria
    .filter(cat => ['vivienda', 'alimentacion', 'servicios', 'salud'].includes(cat.categoria.toLowerCase()))
    .reduce((sum, cat) => sum + cat.total, 0);
    
  const gastosDiscrecionales = metrics.gastosPorCategoria
    .filter(cat => ['entretenimiento', 'compras', 'suscripciones'].includes(cat.categoria.toLowerCase()))
    .reduce((sum, cat) => sum + cat.total, 0);
    
  const deudas = metrics.gastosPorCategoria
    .filter(cat => ['tarjeta', 'prestamo'].includes(cat.categoria.toLowerCase()))
    .reduce((sum, cat) => sum + cat.total, 0);
  
  const data = [
    {
      categoria: 'Ingresos',
      Estimado: metrics.totalIngresos * 0.95, // Estimado un poco menor que real para mostrar variación
      Real: metrics.totalIngresos,
    },
    {
      categoria: 'Gastos\nEsenciales',
      Estimado: gastosEsenciales * 1.1, // Estimado un poco mayor para mostrar ahorro
      Real: gastosEsenciales,
    },
    {
      categoria: 'Gastos\nDiscrec.',
      Estimado: gastosDiscrecionales * 0.8, // Tendencia a gastar más de lo estimado
      Real: gastosDiscrecionales,
    },
    {
      categoria: 'Deudas',
      Estimado: deudas,
      Real: deudas,
    },
    {
      categoria: 'Ahorros',
      Estimado: Math.max(0, metrics.remanente * 0.8),
      Real: Math.max(0, metrics.remanente),
    },
    {
      categoria: 'Inversiones',
      Estimado: Math.max(0, metrics.remanente * 0.2),
      Real: Math.max(0, metrics.remanente * 0.3),
    },
  ].filter(item => item.Estimado > 0 || item.Real > 0); // Solo mostrar categorías con datos

  // Custom tooltip component with improved contrast
  const CustomTooltip = ({ active, payload, label }: TooltipProps<ValueType, NameType>) => {
    if (active && payload && payload.length) {
      const estimado = Math.round(Number(payload[0].value));
      const real = Math.round(Number(payload[1].value));
      const diferencia = real - estimado;
      const porcentaje = estimado > 0 ? Number(((real / estimado) * 100 - 100).toFixed(1)) : 0;
      
      const esPositivo = 
        label === 'Ingresos' 
          ? diferencia > 0
          : diferencia < 0;
      
      return (
        <div className="bg-[#21222d] p-3 border border-[#2a2a3d] shadow-lg rounded-lg">
          <p className="font-bold text-base text-white">{label}</p>
          <p className="text-sm my-1 font-medium text-gray-200">Estimado: <span className="font-semibold">${estimado.toLocaleString()}</span></p>
          <p className="text-sm my-1 font-medium text-gray-200">Real: <span className="font-semibold">${real.toLocaleString()}</span></p>
          <p className={`text-sm font-medium my-1 ${esPositivo ? 'text-green-400' : 'text-red-400'}`}>
            Diferencia: <span className="font-bold">{diferencia > 0 ? '+' : ''}${diferencia.toLocaleString()}</span>
            {' '}({porcentaje}%)
          </p>
        </div>
      );
    }
    return null;
  };

  return (
    <div className="p-6 pt-0">
      <div className="h-[400px]">
        <ResponsiveContainer width="100%" height="100%">
          <BarChart 
            data={data} 
            animationDuration={1000}
            margin={{ top: 20, right: 30, left: 20, bottom: 20 }}
          >
            <CartesianGrid strokeDasharray="3 3" stroke="rgba(255,255,255,0.15)" />
            <XAxis 
              dataKey="categoria" 
              tick={{ fill: '#ffffff', fontSize: 12, fontWeight: 600 }}
              tickLine={{ stroke: 'rgba(255,255,255,0.3)' }}
              axisLine={{ stroke: 'rgba(255,255,255,0.3)' }}
            />
            <YAxis 
              tick={{ fill: '#ffffff', fontSize: 12, fontWeight: 500 }}
              tickLine={{ stroke: 'rgba(255,255,255,0.3)' }}
              axisLine={{ stroke: 'rgba(255,255,255,0.3)' }}
            />
            <Tooltip 
              content={<CustomTooltip />} 
              cursor={{ fill: 'rgba(255,255,255,0.1)' }}
            />
            <Legend 
              wrapperStyle={{ 
                paddingTop: '10px',
                fontSize: '13px',
                fontWeight: 600,
                color: '#ffffff'
              }}
            />
            <Bar 
              dataKey="Estimado" 
              fill="#96a7ff" 
              radius={[4, 4, 0, 0]}
              name="Estimado"
            />
            <Bar 
              dataKey="Real" 
              fill="#f9769d" 
              radius={[4, 4, 0, 0]}
              name="Real"
            />
          </BarChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
}