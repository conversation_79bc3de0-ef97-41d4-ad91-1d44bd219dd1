import { Card } from './ui/Card';
import { <PERSON><PERSON><PERSON>, Pie, Cell, ResponsiveContainer, Tooltip } from 'recharts';
import type { Mes } from '../lib/types';
import { getTasaAhorroMessage } from '../lib/calculations';
import { useState, useEffect, useRef } from 'react';
import { TooltipRoot, TooltipTrigger, TooltipContent } from './ui/Tooltip';
import { Info } from 'lucide-react';
import { useTransactionContext } from '../lib/TransactionContext';
import { calculateFinancialMetrics, formatNumber, formatDecimal } from '../lib/calculations';

interface VisionGeneralPanelProps {
  mes: Mes;
}

export function VisionGeneralPanel({ mes }: VisionGeneralPanelProps) {
  const { transactions } = useTransactionContext();
  const metrics = calculateFinancialMetrics(transactions, mes);
  
  const totalIngresos = Math.round(metrics.totalIngresos);
  const totalGastos = Math.round(metrics.totalGastos);
  const remanente = Math.round(metrics.remanente);
  const tasaAhorroInversion = Number(metrics.tasaAhorroInversion.toFixed(1));
  
  // State for animated percentage
  const [animatedPercentage, setAnimatedPercentage] = useState(0);
  const [animatedDistribucion, setAnimatedDistribucion] = useState({
    egresos: Array(3).fill(0),
    detallada: Array(5).fill(0)
  });
  
  // Animation refs
  const animationStartedRef = useRef(false);
  
  // Calcular distribución de egresos basada en categorías reales
  const categoriasEsenciales = ['vivienda', 'alimentacion', 'servicios', 'salud'];
  const categoriasDiscrecionales = ['entretenimiento', 'compras', 'suscripciones'];
  const categoriasDeudas = ['tarjeta', 'prestamo'];
  
  const gastosEsenciales = metrics.gastosPorCategoria
    .filter(cat => categoriasEsenciales.includes(cat.categoria.toLowerCase()))
    .reduce((sum, cat) => sum + cat.total, 0);
    
  const gastosDiscrecionales = metrics.gastosPorCategoria
    .filter(cat => categoriasDiscrecionales.includes(cat.categoria.toLowerCase()))
    .reduce((sum, cat) => sum + cat.total, 0);
    
  const pagoDeudas = metrics.gastosPorCategoria
    .filter(cat => categoriasDeudas.includes(cat.categoria.toLowerCase()))
    .reduce((sum, cat) => sum + cat.total, 0);
  
  const totalCategorizado = gastosEsenciales + gastosDiscrecionales + pagoDeudas;
  
  const distribucionEgresos = totalCategorizado > 0 ? [
    { 
      name: 'Gastos Esenciales', 
      value: (gastosEsenciales / totalCategorizado) * 100, 
      color: '#2dd4bf' 
    },
    { 
      name: 'Gastos Discrecionales', 
      value: (gastosDiscrecionales / totalCategorizado) * 100, 
      color: '#22d3ee' 
    },
    { 
      name: 'Pago de Deudas', 
      value: (pagoDeudas / totalCategorizado) * 100, 
      color: '#38bdf8' 
    },
  ] : [
    { name: 'Sin gastos registrados', value: 100, color: '#6B7280' }
  ];

  // Tomar las top 5 categorías de gastos para el gráfico detallado
  const distribucionDetallada = metrics.gastosPorCategoria.length > 0 
    ? metrics.gastosPorCategoria
        .sort((a, b) => b.total - a.total)
        .slice(0, 5)
        .map(cat => ({
          name: cat.categoria,
          value: (cat.total / metrics.totalGastos) * 100,
          color: cat.color
        }))
    : [{ name: 'Sin gastos registrados', value: 100, color: '#6B7280' }];

  const message = getTasaAhorroMessage(tasaAhorroInversion);

  // Get positive remanente emoji
  const getRemanenteEmoji = () => {
    if (remanente >= 1000) return "✨";
    if (remanente > 0) return "✅";
    return "";
  };
  
  // Animate percentages when component mounts
  useEffect(() => {
    if (!animationStartedRef.current) {
      animationStartedRef.current = true;
      
      // Animate tasa ahorro
      const tasaAhorroInterval = setInterval(() => {
        setAnimatedPercentage(prev => {
          if (prev < tasaAhorroInversion) {
            return Math.min(prev + 1, tasaAhorroInversion);
          }
          clearInterval(tasaAhorroInterval);
          return tasaAhorroInversion;
        });
      }, 30);
      
      // Animate pie charts
      const distributionInterval = setInterval(() => {
        setAnimatedDistribucion(prev => {
          const newEgresos = [...prev.egresos];
          const newDetallada = [...prev.detallada];
          
          let completed = true;
          
          // Update egresos
          distribucionEgresos.forEach((item, index) => {
            if (newEgresos[index] < item.value) {
              newEgresos[index] = Math.min(newEgresos[index] + 1, item.value);
              completed = false;
            }
          });
          
          // Update detallada
          distribucionDetallada.forEach((item, index) => {
            if (newDetallada[index] < item.value) {
              newDetallada[index] = Math.min(newDetallada[index] + 1, item.value);
              completed = false;
            }
          });
          
          if (completed) {
            clearInterval(distributionInterval);
          }
          
          return {
            egresos: newEgresos,
            detallada: newDetallada
          };
        });
      }, 20);
      
      return () => {
        clearInterval(tasaAhorroInterval);
        clearInterval(distributionInterval);
      };
    }
  }, [tasaAhorroInversion]);
  
  // Create animated data for charts
  const animatedEgresosData = distribucionEgresos.map((item, index) => ({
    ...item,
    value: animatedDistribucion.egresos[index]
  }));
  
  const animatedDetalladaData = distribucionDetallada.map((item, index) => ({
    ...item,
    value: animatedDistribucion.detallada[index]
  }));
  
  // Custom label for donut charts
  const renderCustomizedLabel = ({ cx, cy, name, percent }) => {
    return (
      <text 
        x={cx} 
        y={cy} 
        dy={0} 
        textAnchor="middle" 
        fill="#ffffff"
        className="percentage-label"
      >
        {`${(percent * 100).toFixed(0)}%`}
      </text>
    );
  };

  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
      <Card className="lg:col-span-1 hover:shadow-lg transition-shadow duration-300">
        <div className="p-6">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-semibold">Visión General</h2>
            <TooltipRoot>
              <TooltipTrigger>
                <Info className="w-4 h-4 text-gray-400 hover:text-white cursor-help" />
              </TooltipTrigger>
              <TooltipContent className="max-w-sm">
                <p className="font-medium mb-1">Resumen Financiero</p>
                <p>Tu situación financiera en números: total de ingresos, gastos y lo que te queda (remanente). La tasa de ahorro te dice qué porcentaje de tus ingresos estás guardando.</p>
              </TooltipContent>
            </TooltipRoot>
          </div>
          
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="font-medium text-base">💰 Ingresos:</span>
              <span className="text-lg font-semibold">${formatNumber(totalIngresos)}</span>
            </div>
            
            <div className="flex justify-between items-center">
              <span className="font-medium text-base">💸 Gastos Totales:</span>
              <span className="text-lg font-semibold">${formatNumber(totalGastos)}</span>
            </div>
            
            <div className="pt-4 border-t border-gray-700">
              <div className="flex justify-between items-center">
                <span className="font-medium text-base">Remanente:</span>
                <span className={`text-lg font-bold ${remanente >= 0 ? 'text-[#10B981]' : 'text-red-500'}`}>
                  ${formatNumber(remanente)} {remanente >= 0 ? '✨' : '⚠️'}
                </span>
              </div>
              <div className="flex justify-between items-center mt-2">
                <span className="font-medium text-base">Tasa Ahorro/Inversión:</span>
                <span className={`text-lg font-bold ${tasaAhorroInversion >= 0 ? 'text-[#96a7ff]' : 'text-red-500'}`}>
                  {formatDecimal(tasaAhorroInversion, 1)}%
                </span>
              </div>
              
              <div className="mt-6 p-3 rounded-lg shadow-sm bg-[#1e1e2d] border border-[#2a2a3d]">
                <p className="text-blue-300 font-medium">
                  {getTasaAhorroMessage(tasaAhorroInversion)}
                </p>
              </div>
            </div>
          </div>
        </div>
      </Card>

      <Card className="lg:col-span-1 hover:shadow-lg transition-shadow duration-300">
        <div className="p-6">
          <div className="flex items-center justify-between mb-5">
            <h2 className="text-xl font-semibold">Distribución de Egresos</h2>
            <TooltipRoot>
              <TooltipTrigger>
                <Info className="w-4 h-4 text-gray-400 hover:text-white cursor-help" />
              </TooltipTrigger>
              <TooltipContent className="max-w-sm">
                <p className="font-medium mb-1">¿En qué gastas?</p>
                <p>Este gráfico te muestra cómo se distribuyen tus gastos: esenciales (necesarios para vivir), discrecionales (gustos) y pagos de deudas.</p>
              </TooltipContent>
            </TooltipRoot>
          </div>
          <div className="h-[280px] relative">
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={animatedEgresosData}
                  dataKey="value"
                  nameKey="name"
                  cx="50%"
                  cy="50%"
                  innerRadius={60}
                  outerRadius={80}
                  paddingAngle={2}
                  label={renderCustomizedLabel}
                  labelLine={false}
                  animationDuration={0}
                  stroke="none"
                >
                  {animatedEgresosData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
            
            {/* Legend */}
            <div className="absolute bottom-0 left-0 right-0 flex flex-col space-y-2 px-4">
              {distribucionEgresos.map((entry, index) => (
                <div key={index} className="flex items-center">
                  <div className="w-3 h-3 rounded-full mr-2" style={{ backgroundColor: entry.color }}></div>
                  <span className="font-medium">{entry.name}</span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </Card>

      <Card className="lg:col-span-1 hover:shadow-lg transition-shadow duration-300">
        <div className="p-6">
          <div className="flex items-center justify-between mb-5">
            <h2 className="text-xl font-semibold">¿Dónde se está yendo mi dinero?</h2>
            <TooltipRoot delayDuration={0}>
              <TooltipTrigger asChild>
                <Info className="w-4 h-4 text-gray-400 hover:text-white cursor-help" />
              </TooltipTrigger>
              <TooltipContent className="max-w-sm p-3 bg-gray-800 text-white rounded-lg shadow-lg">
                <div>
                  <p className="font-medium mb-1">Top 5 Categorías</p>
                  <p className="text-sm">Muestra las 5 categorías donde más dinero gastas. Te ayuda a identificar patrones y oportunidades de ahorro.</p>
                </div>
              </TooltipContent>
            </TooltipRoot>
          </div>
          <div className="h-[280px] relative">
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={animatedDetalladaData}
                  dataKey="value"
                  nameKey="name"
                  cx="50%"
                  cy="50%"
                  innerRadius={60}
                  outerRadius={80}
                  paddingAngle={2}
                  label={renderCustomizedLabel}
                  labelLine={false}
                  animationDuration={0}
                  stroke="none"
                >
                  {animatedDetalladaData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
            
            {/* Legend */}
            <div className="absolute bottom-0 left-0 right-0 flex justify-center gap-x-4 gap-y-2 flex-wrap px-4">
              {distribucionDetallada.map((entry, index) => (
                <div key={index} className="flex items-center">
                  <div className="w-3 h-3 rounded-full mr-1" style={{ backgroundColor: entry.color }}></div>
                  <span className="font-medium">{entry.name}</span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </Card>
    </div>
  );
}