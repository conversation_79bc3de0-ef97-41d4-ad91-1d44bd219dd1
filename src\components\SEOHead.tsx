import { Helmet } from 'react-helmet-async';

interface SEOHeadProps {
  title?: string;
  description?: string;
  keywords?: string;
  image?: string;
  url?: string;
  type?: 'website' | 'article';
  noIndex?: boolean;
}

export function SEOHead({
  title = 'Finanzas Personales - Gestiona tu dinero de forma inteligente',
  description = 'Aplicación completa para gestión de finanzas personales. Controla tus gastos, establece presupuestos, alcanza tus metas de ahorro y planifica tu futuro financiero.',
  keywords = 'finanzas personales, presupuesto, ahorro, gastos, ingresos, metas financieras, FIRE, patrimonio neto, inversiones',
  image = '/og-image.png',
  url = 'https://finanzas-personales.app',
  type = 'website',
  noIndex = false
}: SEOHeadProps) {
  const fullTitle = title.includes('Finanzas Personales') ? title : `${title} | Finanzas Personales`;
  const fullUrl = url.startsWith('http') ? url : `https://finanzas-personales.app${url}`;
  const fullImage = image.startsWith('http') ? image : `https://finanzas-personales.app${image}`;

  return (
    <Helmet>
      {/* Basic Meta Tags */}
      <title>{fullTitle}</title>
      <meta name="description" content={description} />
      <meta name="keywords" content={keywords} />
      <meta name="author" content="Finanzas Personales App" />
      <meta name="viewport" content="width=device-width, initial-scale=1.0" />
      <meta name="theme-color" content="#0f0f23" />
      
      {/* Robots */}
      {noIndex && <meta name="robots" content="noindex, nofollow" />}
      
      {/* Canonical URL */}
      <link rel="canonical" href={fullUrl} />
      
      {/* Open Graph / Facebook */}
      <meta property="og:type" content={type} />
      <meta property="og:url" content={fullUrl} />
      <meta property="og:title" content={fullTitle} />
      <meta property="og:description" content={description} />
      <meta property="og:image" content={fullImage} />
      <meta property="og:image:width" content="1200" />
      <meta property="og:image:height" content="630" />
      <meta property="og:site_name" content="Finanzas Personales" />
      <meta property="og:locale" content="es_ES" />
      
      {/* Twitter */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:url" content={fullUrl} />
      <meta name="twitter:title" content={fullTitle} />
      <meta name="twitter:description" content={description} />
      <meta name="twitter:image" content={fullImage} />
      <meta name="twitter:creator" content="@finanzaspersonales" />
      
      {/* Additional Meta Tags */}
      <meta name="application-name" content="Finanzas Personales" />
      <meta name="apple-mobile-web-app-title" content="Finanzas" />
      <meta name="apple-mobile-web-app-capable" content="yes" />
      <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
      <meta name="mobile-web-app-capable" content="yes" />
      <meta name="msapplication-TileColor" content="#0f0f23" />
      <meta name="msapplication-config" content="/browserconfig.xml" />
      
      {/* Structured Data - Organization */}
      <script type="application/ld+json">
        {JSON.stringify({
          "@context": "https://schema.org",
          "@type": "WebApplication",
          "name": "Finanzas Personales",
          "description": description,
          "url": "https://finanzas-personales.app",
          "applicationCategory": "FinanceApplication",
          "operatingSystem": "Web Browser",
          "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "USD"
          },
          "author": {
            "@type": "Organization",
            "name": "Finanzas Personales",
            "url": "https://finanzas-personales.app"
          },
          "featureList": [
            "Gestión de presupuestos",
            "Seguimiento de gastos e ingresos",
            "Metas de ahorro",
            "Análisis financiero",
            "Reportes detallados",
            "Calculadora FIRE",
            "Gestión de patrimonio neto",
            "Transacciones recurrentes"
          ]
        })}
      </script>
      
      {/* Structured Data - BreadcrumbList for navigation */}
      <script type="application/ld+json">
        {JSON.stringify({
          "@context": "https://schema.org",
          "@type": "BreadcrumbList",
          "itemListElement": [
            {
              "@type": "ListItem",
              "position": 1,
              "name": "Inicio",
              "item": "https://finanzas-personales.app/"
            },
            {
              "@type": "ListItem",
              "position": 2,
              "name": "Dashboard",
              "item": "https://finanzas-personales.app/dashboard"
            }
          ]
        })}
      </script>
    </Helmet>
  );
}

// Predefined SEO configurations for different pages
export const seoConfigs = {
  dashboard: {
    title: 'Dashboard - Finanzas Personales',
    description: 'Panel principal con resumen de tus finanzas: ingresos, gastos, presupuestos y metas de ahorro.',
    url: '/'
  },
  
  transactions: {
    title: 'Transacciones - Finanzas Personales',
    description: 'Gestiona todas tus transacciones financieras: ingresos, gastos y transferencias.',
    url: '/transactions'
  },
  
  budgets: {
    title: 'Presupuestos - Finanzas Personales',
    description: 'Crea y gestiona presupuestos mensuales para controlar tus gastos por categoría.',
    url: '/budgets'
  },
  
  savingsGoals: {
    title: 'Metas de Ahorro - Finanzas Personales',
    description: 'Establece y alcanza tus metas de ahorro a corto y largo plazo.',
    url: '/savings-goals'
  },
  
  reports: {
    title: 'Reportes - Finanzas Personales',
    description: 'Análisis detallados de tus finanzas con gráficos y estadísticas avanzadas.',
    url: '/reports'
  },
  
  patrimonio: {
    title: 'Patrimonio Neto - Finanzas Personales',
    description: 'Calcula y gestiona tu patrimonio neto: activos, pasivos y evolución temporal.',
    url: '/patrimonio'
  },
  
  fire: {
    title: 'Calculadora FIRE - Finanzas Personales',
    description: 'Calcula cuánto necesitas ahorrar para alcanzar la independencia financiera y retirarte temprano.',
    url: '/calculadora-fire'
  },
  
  fondoEmergencia: {
    title: 'Fondo de Emergencia - Finanzas Personales',
    description: 'Construye tu fondo de emergencia siguiendo los Baby Steps de Dave Ramsey.',
    url: '/fondo-emergencia'
  },
  
  flujoGasto: {
    title: 'Flujo de Gastos - Finanzas Personales',
    description: 'Organiza y prioriza tus gastos según tu flujo de ingresos disponible.',
    url: '/flujo-gasto'
  },
  
  doceSemanas: {
    title: '12 Semanas Poderosas - Finanzas Personales',
    description: 'Programa de transformación financiera en 12 semanas para cambiar tus hábitos.',
    url: '/12-semanas'
  },
  
  ingresosPasivos: {
    title: 'Ingresos Pasivos - Finanzas Personales',
    description: 'Planifica y gestiona tus fuentes de ingresos pasivos para alcanzar la libertad financiera.',
    url: '/ingresos-pasivos'
  },
  
  vidaRica: {
    title: 'Vida Rica - Finanzas Personales',
    description: 'Filosofía de vida rica: gasta en lo que amas, ahorra en lo que no te importa.',
    url: '/vida-rica'
  }
};

// Hook for easy SEO management
export function useSEO(pageKey?: keyof typeof seoConfigs, customConfig?: Partial<SEOHeadProps>) {
  const config = pageKey ? seoConfigs[pageKey] : {};
  const finalConfig = { ...config, ...customConfig };
  
  return <SEOHead {...finalConfig} />;
}
